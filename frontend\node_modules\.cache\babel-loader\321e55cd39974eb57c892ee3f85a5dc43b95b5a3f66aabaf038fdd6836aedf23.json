{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Advanced\\\\Advanced CRM filed all use\\\\frontend\\\\src\\\\components\\\\forms\\\\AllFormsModal.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport formConfigService from '../../services/formConfigService';\nimport apiService from '../../services/apiService';\nimport './AllFormsModal.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AllFormsModal = ({\n  onClose,\n  onEditForm,\n  onDeleteForm,\n  isInline = false\n}) => {\n  _s();\n  const [forms, setForms] = useState([]);\n  const [divisions, setDivisions] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [deleteConfirm, setDeleteConfirm] = useState(null);\n  useEffect(() => {\n    loadData();\n  }, []);\n  const loadData = async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      // Load divisions and categories\n      const [divisionsResponse, categoriesResponse] = await Promise.all([apiService.getDivisions(), apiService.getCategories()]);\n      setDivisions(divisionsResponse.data || []);\n      setCategories(categoriesResponse.data || []);\n\n      // Load all forms from localStorage\n      const allForms = formConfigService.getAllFormConfigs();\n      console.log('Loaded forms from localStorage:', allForms);\n\n      // Enrich forms with division and category information\n      const enrichedForms = allForms.map(form => {\n        var _form$hierarchy, _form$hierarchy2, _form$hierarchy3, _form$hierarchy4;\n        let divisionName = 'General';\n        let categoryName = 'Default';\n        if ((_form$hierarchy = form.hierarchy) !== null && _form$hierarchy !== void 0 && _form$hierarchy.divisionId) {\n          var _divisionsResponse$da;\n          const division = (_divisionsResponse$da = divisionsResponse.data) === null || _divisionsResponse$da === void 0 ? void 0 : _divisionsResponse$da.find(d => d.id === form.hierarchy.divisionId);\n          if (division) {\n            divisionName = division.name;\n          }\n        }\n        if ((_form$hierarchy2 = form.hierarchy) !== null && _form$hierarchy2 !== void 0 && _form$hierarchy2.categoryId) {\n          var _categoriesResponse$d;\n          const category = (_categoriesResponse$d = categoriesResponse.data) === null || _categoriesResponse$d === void 0 ? void 0 : _categoriesResponse$d.find(c => c.id === form.hierarchy.categoryId);\n          if (category) {\n            categoryName = category.name;\n          }\n        }\n        return {\n          ...form,\n          divisionName,\n          categoryName,\n          divisionId: ((_form$hierarchy3 = form.hierarchy) === null || _form$hierarchy3 === void 0 ? void 0 : _form$hierarchy3.divisionId) || null,\n          categoryId: ((_form$hierarchy4 = form.hierarchy) === null || _form$hierarchy4 === void 0 ? void 0 : _form$hierarchy4.categoryId) || null\n        };\n      });\n      console.log('Enriched forms:', enrichedForms);\n      setForms(enrichedForms);\n    } catch (err) {\n      console.error('Error loading forms data:', err);\n      setError('Failed to load forms data. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleDeleteForm = form => {\n    setDeleteConfirm(form);\n  };\n  const confirmDelete = () => {\n    if (deleteConfirm) {\n      try {\n        formConfigService.deleteFormConfig(deleteConfirm.type, deleteConfirm.associatedId);\n        setForms(forms.filter(f => f.id !== deleteConfirm.id));\n        setDeleteConfirm(null);\n        if (onDeleteForm) {\n          onDeleteForm(deleteConfirm);\n        }\n      } catch (err) {\n        console.error('Error deleting form:', err);\n        setError('Failed to delete form. Please try again.');\n      }\n    }\n  };\n  const cancelDelete = () => {\n    setDeleteConfirm(null);\n  };\n  const handleEditForm = form => {\n    if (onEditForm) {\n      onEditForm(form);\n    }\n    onClose();\n  };\n\n  // Group forms by division and then by category\n  const groupedForms = forms.reduce((acc, form) => {\n    const divisionKey = form.divisionId || 'general';\n    const categoryKey = form.categoryId || 'default';\n    if (!acc[divisionKey]) {\n      acc[divisionKey] = {\n        name: form.divisionName,\n        categories: {}\n      };\n    }\n    if (!acc[divisionKey].categories[categoryKey]) {\n      acc[divisionKey].categories[categoryKey] = {\n        name: form.categoryName,\n        forms: []\n      };\n    }\n    acc[divisionKey].categories[categoryKey].forms.push(form);\n    return acc;\n  }, {});\n  const formatDate = dateString => {\n    if (!dateString) return 'N/A';\n    return new Date(dateString).toLocaleDateString();\n  };\n  if (loading) {\n    const content = /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"All Forms\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onClose,\n          className: \"close-button\",\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-body\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-state\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Loading forms...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 7\n    }, this);\n    return isInline ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"all-forms-inline\",\n      children: content\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 7\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"all-forms-modal\",\n      children: content\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 7\n    }, this);\n  }\n  const content = /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"modal-content\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"All Forms\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onClose,\n        className: \"close-button\",\n        children: \"\\xD7\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-body\",\n      children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-message\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"error-icon\",\n          children: \"\\u26A0\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 11\n      }, this), forms.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"empty-state\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"empty-icon\",\n          children: \"\\uD83D\\uDCDD\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"No Forms Found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"No custom forms have been created yet. Use the Form Builder to create your first form.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"forms-container\",\n        children: Object.entries(groupedForms).map(([divisionKey, division]) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"division-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"division-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: [\"\\uD83C\\uDFE2 \", division.name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"forms-count\",\n              children: [Object.values(division.categories).reduce((total, cat) => total + cat.forms.length, 0), \" form(s)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 17\n          }, this), Object.entries(division.categories).map(([categoryKey, category]) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"category-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"category-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: [\"\\uD83D\\uDCC2 \", category.name]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"category-forms-count\",\n                children: [category.forms.length, \" form(s)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"forms-list\",\n              children: category.forms.map(form => {\n                var _form$fields;\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-list-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"form-info\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"form-name-section\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                        className: \"form-name\",\n                        children: form.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 205,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `type-badge ${form.type}`,\n                        children: form.type === 'category' ? 'Category' : form.type === 'subcategory' ? 'SubCategory' : 'Custom'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 206,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 204,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"form-description\",\n                      children: form.description || 'No description'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 212,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"form-meta\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"meta-item\",\n                        children: [\"\\uD83D\\uDCDD \", ((_form$fields = form.fields) === null || _form$fields === void 0 ? void 0 : _form$fields.length) || 0, \" fields\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 214,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"meta-item\",\n                        children: [\"\\uD83D\\uDCC5 \", formatDate(form.updatedAt)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 215,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 213,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 203,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"form-actions\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => handleEditForm(form),\n                      className: \"btn-action edit\",\n                      title: \"Edit form\",\n                      children: \"\\u270F\\uFE0F Edit\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 219,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => handleDeleteForm(form),\n                      className: \"btn-action delete\",\n                      title: \"Delete form\",\n                      children: \"\\uD83D\\uDDD1\\uFE0F Delete\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 226,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 218,\n                    columnNumber: 27\n                  }, this)]\n                }, form.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 25\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 21\n            }, this)]\n          }, categoryKey, true, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 19\n          }, this))]\n        }, divisionKey, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 7\n    }, this), deleteConfirm && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"delete-confirm-modal\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"delete-confirm-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"delete-confirm-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Confirm Delete\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"delete-confirm-body\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"Are you sure you want to delete the form \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: [\"\\\"\", deleteConfirm.name, \"\\\"\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 59\n            }, this), \"?\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"warning-text\",\n            children: \"This action cannot be undone.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"delete-confirm-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: cancelDelete,\n            className: \"btn-cancel\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: confirmDelete,\n            className: \"btn-delete\",\n            children: \"Delete\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 247,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 162,\n    columnNumber: 5\n  }, this);\n  return isInline ? /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"all-forms-inline\",\n    children: content\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 267,\n    columnNumber: 5\n  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"all-forms-modal\",\n    children: content\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 271,\n    columnNumber: 5\n  }, this);\n};\n_s(AllFormsModal, \"RQ+hg0Bxf6zGnwZ1c4ZpKa9CSY4=\");\n_c = AllFormsModal;\nexport default AllFormsModal;\nvar _c;\n$RefreshReg$(_c, \"AllFormsModal\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "formConfigService", "apiService", "jsxDEV", "_jsxDEV", "AllFormsModal", "onClose", "onEditForm", "onDeleteForm", "isInline", "_s", "forms", "setForms", "divisions", "setDivisions", "categories", "setCategories", "loading", "setLoading", "error", "setError", "deleteConfirm", "setDeleteConfirm", "loadData", "divisionsResponse", "categoriesResponse", "Promise", "all", "getDivisions", "getCategories", "data", "allForms", "getAllFormConfigs", "console", "log", "enrichedForms", "map", "form", "_form$hierarchy", "_form$hierarchy2", "_form$hierarchy3", "_form$hierarchy4", "divisionName", "categoryName", "hierarchy", "divisionId", "_divisionsResponse$da", "division", "find", "d", "id", "name", "categoryId", "_categoriesResponse$d", "category", "c", "err", "handleDeleteForm", "confirmDelete", "deleteFormConfig", "type", "associatedId", "filter", "f", "cancelDelete", "handleEditForm", "groupedForms", "reduce", "acc", "divisionKey", "categoryKey", "push", "formatDate", "dateString", "Date", "toLocaleDateString", "content", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "length", "Object", "entries", "values", "total", "cat", "_form$fields", "description", "fields", "updatedAt", "title", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/components/forms/AllFormsModal.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport formConfigService from '../../services/formConfigService';\nimport apiService from '../../services/apiService';\nimport './AllFormsModal.css';\n\nconst AllFormsModal = ({ onClose, onEditForm, onDeleteForm, isInline = false }) => {\n  const [forms, setForms] = useState([]);\n  const [divisions, setDivisions] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [deleteConfirm, setDeleteConfirm] = useState(null);\n\n  useEffect(() => {\n    loadData();\n  }, []);\n\n  const loadData = async () => {\n    setLoading(true);\n    setError(null);\n    \n    try {\n      // Load divisions and categories\n      const [divisionsResponse, categoriesResponse] = await Promise.all([\n        apiService.getDivisions(),\n        apiService.getCategories()\n      ]);\n\n      setDivisions(divisionsResponse.data || []);\n      setCategories(categoriesResponse.data || []);\n\n      // Load all forms from localStorage\n      const allForms = formConfigService.getAllFormConfigs();\n      console.log('Loaded forms from localStorage:', allForms);\n\n      // Enrich forms with division and category information\n      const enrichedForms = allForms.map(form => {\n        let divisionName = 'General';\n        let categoryName = 'Default';\n\n        if (form.hierarchy?.divisionId) {\n          const division = divisionsResponse.data?.find(d => d.id === form.hierarchy.divisionId);\n          if (division) {\n            divisionName = division.name;\n          }\n        }\n\n        if (form.hierarchy?.categoryId) {\n          const category = categoriesResponse.data?.find(c => c.id === form.hierarchy.categoryId);\n          if (category) {\n            categoryName = category.name;\n          }\n        }\n\n        return {\n          ...form,\n          divisionName,\n          categoryName,\n          divisionId: form.hierarchy?.divisionId || null,\n          categoryId: form.hierarchy?.categoryId || null\n        };\n      });\n\n      console.log('Enriched forms:', enrichedForms);\n      setForms(enrichedForms);\n    } catch (err) {\n      console.error('Error loading forms data:', err);\n      setError('Failed to load forms data. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDeleteForm = (form) => {\n    setDeleteConfirm(form);\n  };\n\n  const confirmDelete = () => {\n    if (deleteConfirm) {\n      try {\n        formConfigService.deleteFormConfig(deleteConfirm.type, deleteConfirm.associatedId);\n        setForms(forms.filter(f => f.id !== deleteConfirm.id));\n        setDeleteConfirm(null);\n        if (onDeleteForm) {\n          onDeleteForm(deleteConfirm);\n        }\n      } catch (err) {\n        console.error('Error deleting form:', err);\n        setError('Failed to delete form. Please try again.');\n      }\n    }\n  };\n\n  const cancelDelete = () => {\n    setDeleteConfirm(null);\n  };\n\n  const handleEditForm = (form) => {\n    if (onEditForm) {\n      onEditForm(form);\n    }\n    onClose();\n  };\n\n  // Group forms by division and then by category\n  const groupedForms = forms.reduce((acc, form) => {\n    const divisionKey = form.divisionId || 'general';\n    const categoryKey = form.categoryId || 'default';\n    \n    if (!acc[divisionKey]) {\n      acc[divisionKey] = {\n        name: form.divisionName,\n        categories: {}\n      };\n    }\n    \n    if (!acc[divisionKey].categories[categoryKey]) {\n      acc[divisionKey].categories[categoryKey] = {\n        name: form.categoryName,\n        forms: []\n      };\n    }\n    \n    acc[divisionKey].categories[categoryKey].forms.push(form);\n    \n    return acc;\n  }, {});\n\n  const formatDate = (dateString) => {\n    if (!dateString) return 'N/A';\n    return new Date(dateString).toLocaleDateString();\n  };\n\n  if (loading) {\n    const content = (\n      <div className=\"modal-content\">\n        <div className=\"modal-header\">\n          <h2>All Forms</h2>\n          <button onClick={onClose} className=\"close-button\">×</button>\n        </div>\n        <div className=\"modal-body\">\n          <div className=\"loading-state\">\n            <div className=\"spinner\"></div>\n            <p>Loading forms...</p>\n          </div>\n        </div>\n      </div>\n    );\n\n    return isInline ? (\n      <div className=\"all-forms-inline\">\n        {content}\n      </div>\n    ) : (\n      <div className=\"all-forms-modal\">\n        {content}\n      </div>\n    );\n  }\n\n  const content = (\n    <div className=\"modal-content\">\n      <div className=\"modal-header\">\n        <h2>All Forms</h2>\n        <button onClick={onClose} className=\"close-button\">×</button>\n      </div>\n\n      <div className=\"modal-body\">\n        {error && (\n          <div className=\"error-message\">\n            <span className=\"error-icon\">⚠️</span>\n            <span>{error}</span>\n          </div>\n        )}\n\n        {forms.length === 0 ? (\n          <div className=\"empty-state\">\n            <div className=\"empty-icon\">📝</div>\n            <h3>No Forms Found</h3>\n            <p>No custom forms have been created yet. Use the Form Builder to create your first form.</p>\n          </div>\n        ) : (\n          <div className=\"forms-container\">\n            {Object.entries(groupedForms).map(([divisionKey, division]) => (\n              <div key={divisionKey} className=\"division-section\">\n                <div className=\"division-header\">\n                  <h3>🏢 {division.name}</h3>\n                  <span className=\"forms-count\">\n                    {Object.values(division.categories).reduce((total, cat) => total + cat.forms.length, 0)} form(s)\n                  </span>\n                </div>\n\n                {Object.entries(division.categories).map(([categoryKey, category]) => (\n                  <div key={categoryKey} className=\"category-section\">\n                    <div className=\"category-header\">\n                      <h4>📂 {category.name}</h4>\n                      <span className=\"category-forms-count\">{category.forms.length} form(s)</span>\n                    </div>\n\n                    <div className=\"forms-list\">\n                      {category.forms.map(form => (\n                        <div key={form.id} className=\"form-list-item\">\n                          <div className=\"form-info\">\n                            <div className=\"form-name-section\">\n                              <h5 className=\"form-name\">{form.name}</h5>\n                              <span className={`type-badge ${form.type}`}>\n                                {form.type === 'category' ? 'Category' :\n                                 form.type === 'subcategory' ? 'SubCategory' :\n                                 'Custom'}\n                              </span>\n                            </div>\n                            <p className=\"form-description\">{form.description || 'No description'}</p>\n                            <div className=\"form-meta\">\n                              <span className=\"meta-item\">📝 {form.fields?.length || 0} fields</span>\n                              <span className=\"meta-item\">📅 {formatDate(form.updatedAt)}</span>\n                            </div>\n                          </div>\n                          <div className=\"form-actions\">\n                            <button\n                              onClick={() => handleEditForm(form)}\n                              className=\"btn-action edit\"\n                              title=\"Edit form\"\n                            >\n                              ✏️ Edit\n                            </button>\n                            <button\n                              onClick={() => handleDeleteForm(form)}\n                              className=\"btn-action delete\"\n                              title=\"Delete form\"\n                            >\n                              🗑️ Delete\n                            </button>\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n                ))}\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n\n      {/* Delete Confirmation Modal */}\n      {deleteConfirm && (\n        <div className=\"delete-confirm-modal\">\n          <div className=\"delete-confirm-content\">\n            <div className=\"delete-confirm-header\">\n              <h3>Confirm Delete</h3>\n            </div>\n            <div className=\"delete-confirm-body\">\n              <p>Are you sure you want to delete the form <strong>\"{deleteConfirm.name}\"</strong>?</p>\n              <p className=\"warning-text\">This action cannot be undone.</p>\n            </div>\n            <div className=\"delete-confirm-actions\">\n              <button onClick={cancelDelete} className=\"btn-cancel\">Cancel</button>\n              <button onClick={confirmDelete} className=\"btn-delete\">Delete</button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n\n  return isInline ? (\n    <div className=\"all-forms-inline\">\n      {content}\n    </div>\n  ) : (\n    <div className=\"all-forms-modal\">\n      {content}\n    </div>\n  );\n};\n\nexport default AllFormsModal;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,iBAAiB,MAAM,kCAAkC;AAChE,OAAOC,UAAU,MAAM,2BAA2B;AAClD,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,aAAa,GAAGA,CAAC;EAAEC,OAAO;EAAEC,UAAU;EAAEC,YAAY;EAAEC,QAAQ,GAAG;AAAM,CAAC,KAAK;EAAAC,EAAA;EACjF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACc,SAAS,EAAEC,YAAY,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACgB,UAAU,EAAEC,aAAa,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoB,KAAK,EAAEC,QAAQ,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACsB,aAAa,EAAEC,gBAAgB,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAExDC,SAAS,CAAC,MAAM;IACduB,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3BL,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF;MACA,MAAM,CAACI,iBAAiB,EAAEC,kBAAkB,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAChEzB,UAAU,CAAC0B,YAAY,CAAC,CAAC,EACzB1B,UAAU,CAAC2B,aAAa,CAAC,CAAC,CAC3B,CAAC;MAEFf,YAAY,CAACU,iBAAiB,CAACM,IAAI,IAAI,EAAE,CAAC;MAC1Cd,aAAa,CAACS,kBAAkB,CAACK,IAAI,IAAI,EAAE,CAAC;;MAE5C;MACA,MAAMC,QAAQ,GAAG9B,iBAAiB,CAAC+B,iBAAiB,CAAC,CAAC;MACtDC,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEH,QAAQ,CAAC;;MAExD;MACA,MAAMI,aAAa,GAAGJ,QAAQ,CAACK,GAAG,CAACC,IAAI,IAAI;QAAA,IAAAC,eAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA;QACzC,IAAIC,YAAY,GAAG,SAAS;QAC5B,IAAIC,YAAY,GAAG,SAAS;QAE5B,KAAAL,eAAA,GAAID,IAAI,CAACO,SAAS,cAAAN,eAAA,eAAdA,eAAA,CAAgBO,UAAU,EAAE;UAAA,IAAAC,qBAAA;UAC9B,MAAMC,QAAQ,IAAAD,qBAAA,GAAGtB,iBAAiB,CAACM,IAAI,cAAAgB,qBAAA,uBAAtBA,qBAAA,CAAwBE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKb,IAAI,CAACO,SAAS,CAACC,UAAU,CAAC;UACtF,IAAIE,QAAQ,EAAE;YACZL,YAAY,GAAGK,QAAQ,CAACI,IAAI;UAC9B;QACF;QAEA,KAAAZ,gBAAA,GAAIF,IAAI,CAACO,SAAS,cAAAL,gBAAA,eAAdA,gBAAA,CAAgBa,UAAU,EAAE;UAAA,IAAAC,qBAAA;UAC9B,MAAMC,QAAQ,IAAAD,qBAAA,GAAG5B,kBAAkB,CAACK,IAAI,cAAAuB,qBAAA,uBAAvBA,qBAAA,CAAyBL,IAAI,CAACO,CAAC,IAAIA,CAAC,CAACL,EAAE,KAAKb,IAAI,CAACO,SAAS,CAACQ,UAAU,CAAC;UACvF,IAAIE,QAAQ,EAAE;YACZX,YAAY,GAAGW,QAAQ,CAACH,IAAI;UAC9B;QACF;QAEA,OAAO;UACL,GAAGd,IAAI;UACPK,YAAY;UACZC,YAAY;UACZE,UAAU,EAAE,EAAAL,gBAAA,GAAAH,IAAI,CAACO,SAAS,cAAAJ,gBAAA,uBAAdA,gBAAA,CAAgBK,UAAU,KAAI,IAAI;UAC9CO,UAAU,EAAE,EAAAX,gBAAA,GAAAJ,IAAI,CAACO,SAAS,cAAAH,gBAAA,uBAAdA,gBAAA,CAAgBW,UAAU,KAAI;QAC5C,CAAC;MACH,CAAC,CAAC;MAEFnB,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEC,aAAa,CAAC;MAC7CvB,QAAQ,CAACuB,aAAa,CAAC;IACzB,CAAC,CAAC,OAAOqB,GAAG,EAAE;MACZvB,OAAO,CAACd,KAAK,CAAC,2BAA2B,EAAEqC,GAAG,CAAC;MAC/CpC,QAAQ,CAAC,8CAA8C,CAAC;IAC1D,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMuC,gBAAgB,GAAIpB,IAAI,IAAK;IACjCf,gBAAgB,CAACe,IAAI,CAAC;EACxB,CAAC;EAED,MAAMqB,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIrC,aAAa,EAAE;MACjB,IAAI;QACFpB,iBAAiB,CAAC0D,gBAAgB,CAACtC,aAAa,CAACuC,IAAI,EAAEvC,aAAa,CAACwC,YAAY,CAAC;QAClFjD,QAAQ,CAACD,KAAK,CAACmD,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACb,EAAE,KAAK7B,aAAa,CAAC6B,EAAE,CAAC,CAAC;QACtD5B,gBAAgB,CAAC,IAAI,CAAC;QACtB,IAAId,YAAY,EAAE;UAChBA,YAAY,CAACa,aAAa,CAAC;QAC7B;MACF,CAAC,CAAC,OAAOmC,GAAG,EAAE;QACZvB,OAAO,CAACd,KAAK,CAAC,sBAAsB,EAAEqC,GAAG,CAAC;QAC1CpC,QAAQ,CAAC,0CAA0C,CAAC;MACtD;IACF;EACF,CAAC;EAED,MAAM4C,YAAY,GAAGA,CAAA,KAAM;IACzB1C,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAM2C,cAAc,GAAI5B,IAAI,IAAK;IAC/B,IAAI9B,UAAU,EAAE;MACdA,UAAU,CAAC8B,IAAI,CAAC;IAClB;IACA/B,OAAO,CAAC,CAAC;EACX,CAAC;;EAED;EACA,MAAM4D,YAAY,GAAGvD,KAAK,CAACwD,MAAM,CAAC,CAACC,GAAG,EAAE/B,IAAI,KAAK;IAC/C,MAAMgC,WAAW,GAAGhC,IAAI,CAACQ,UAAU,IAAI,SAAS;IAChD,MAAMyB,WAAW,GAAGjC,IAAI,CAACe,UAAU,IAAI,SAAS;IAEhD,IAAI,CAACgB,GAAG,CAACC,WAAW,CAAC,EAAE;MACrBD,GAAG,CAACC,WAAW,CAAC,GAAG;QACjBlB,IAAI,EAAEd,IAAI,CAACK,YAAY;QACvB3B,UAAU,EAAE,CAAC;MACf,CAAC;IACH;IAEA,IAAI,CAACqD,GAAG,CAACC,WAAW,CAAC,CAACtD,UAAU,CAACuD,WAAW,CAAC,EAAE;MAC7CF,GAAG,CAACC,WAAW,CAAC,CAACtD,UAAU,CAACuD,WAAW,CAAC,GAAG;QACzCnB,IAAI,EAAEd,IAAI,CAACM,YAAY;QACvBhC,KAAK,EAAE;MACT,CAAC;IACH;IAEAyD,GAAG,CAACC,WAAW,CAAC,CAACtD,UAAU,CAACuD,WAAW,CAAC,CAAC3D,KAAK,CAAC4D,IAAI,CAAClC,IAAI,CAAC;IAEzD,OAAO+B,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;EAEN,MAAMI,UAAU,GAAIC,UAAU,IAAK;IACjC,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;IAC7B,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,CAAC;EAClD,CAAC;EAED,IAAI1D,OAAO,EAAE;IACX,MAAM2D,OAAO,gBACXxE,OAAA;MAAKyE,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5B1E,OAAA;QAAKyE,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B1E,OAAA;UAAA0E,QAAA,EAAI;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClB9E,OAAA;UAAQ+E,OAAO,EAAE7E,OAAQ;UAACuE,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,eACN9E,OAAA;QAAKyE,SAAS,EAAC,YAAY;QAAAC,QAAA,eACzB1E,OAAA;UAAKyE,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B1E,OAAA;YAAKyE,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/B9E,OAAA;YAAA0E,QAAA,EAAG;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;IAED,OAAOzE,QAAQ,gBACbL,OAAA;MAAKyE,SAAS,EAAC,kBAAkB;MAAAC,QAAA,EAC9BF;IAAO;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,gBAEN9E,OAAA;MAAKyE,SAAS,EAAC,iBAAiB;MAAAC,QAAA,EAC7BF;IAAO;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN;EACH;EAEA,MAAMN,OAAO,gBACXxE,OAAA;IAAKyE,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAC5B1E,OAAA;MAAKyE,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3B1E,OAAA;QAAA0E,QAAA,EAAI;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAClB9E,OAAA;QAAQ+E,OAAO,EAAE7E,OAAQ;QAACuE,SAAS,EAAC,cAAc;QAAAC,QAAA,EAAC;MAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1D,CAAC,eAEN9E,OAAA;MAAKyE,SAAS,EAAC,YAAY;MAAAC,QAAA,GACxB3D,KAAK,iBACJf,OAAA;QAAKyE,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5B1E,OAAA;UAAMyE,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtC9E,OAAA;UAAA0E,QAAA,EAAO3D;QAAK;UAAA4D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CACN,EAEAvE,KAAK,CAACyE,MAAM,KAAK,CAAC,gBACjBhF,OAAA;QAAKyE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B1E,OAAA;UAAKyE,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACpC9E,OAAA;UAAA0E,QAAA,EAAI;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvB9E,OAAA;UAAA0E,QAAA,EAAG;QAAsF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1F,CAAC,gBAEN9E,OAAA;QAAKyE,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAC7BO,MAAM,CAACC,OAAO,CAACpB,YAAY,CAAC,CAAC9B,GAAG,CAAC,CAAC,CAACiC,WAAW,EAAEtB,QAAQ,CAAC,kBACxD3C,OAAA;UAAuByE,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBACjD1E,OAAA;YAAKyE,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9B1E,OAAA;cAAA0E,QAAA,GAAI,eAAG,EAAC/B,QAAQ,CAACI,IAAI;YAAA;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC3B9E,OAAA;cAAMyE,SAAS,EAAC,aAAa;cAAAC,QAAA,GAC1BO,MAAM,CAACE,MAAM,CAACxC,QAAQ,CAAChC,UAAU,CAAC,CAACoD,MAAM,CAAC,CAACqB,KAAK,EAAEC,GAAG,KAAKD,KAAK,GAAGC,GAAG,CAAC9E,KAAK,CAACyE,MAAM,EAAE,CAAC,CAAC,EAAC,UAC1F;YAAA;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,EAELG,MAAM,CAACC,OAAO,CAACvC,QAAQ,CAAChC,UAAU,CAAC,CAACqB,GAAG,CAAC,CAAC,CAACkC,WAAW,EAAEhB,QAAQ,CAAC,kBAC/DlD,OAAA;YAAuByE,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBACjD1E,OAAA;cAAKyE,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9B1E,OAAA;gBAAA0E,QAAA,GAAI,eAAG,EAACxB,QAAQ,CAACH,IAAI;cAAA;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC3B9E,OAAA;gBAAMyE,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,GAAExB,QAAQ,CAAC3C,KAAK,CAACyE,MAAM,EAAC,UAAQ;cAAA;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1E,CAAC,eAEN9E,OAAA;cAAKyE,SAAS,EAAC,YAAY;cAAAC,QAAA,EACxBxB,QAAQ,CAAC3C,KAAK,CAACyB,GAAG,CAACC,IAAI;gBAAA,IAAAqD,YAAA;gBAAA,oBACtBtF,OAAA;kBAAmByE,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC3C1E,OAAA;oBAAKyE,SAAS,EAAC,WAAW;oBAAAC,QAAA,gBACxB1E,OAAA;sBAAKyE,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,gBAChC1E,OAAA;wBAAIyE,SAAS,EAAC,WAAW;wBAAAC,QAAA,EAAEzC,IAAI,CAACc;sBAAI;wBAAA4B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC1C9E,OAAA;wBAAMyE,SAAS,EAAE,cAAcxC,IAAI,CAACuB,IAAI,EAAG;wBAAAkB,QAAA,EACxCzC,IAAI,CAACuB,IAAI,KAAK,UAAU,GAAG,UAAU,GACrCvB,IAAI,CAACuB,IAAI,KAAK,aAAa,GAAG,aAAa,GAC3C;sBAAQ;wBAAAmB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACN9E,OAAA;sBAAGyE,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAEzC,IAAI,CAACsD,WAAW,IAAI;oBAAgB;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC1E9E,OAAA;sBAAKyE,SAAS,EAAC,WAAW;sBAAAC,QAAA,gBACxB1E,OAAA;wBAAMyE,SAAS,EAAC,WAAW;wBAAAC,QAAA,GAAC,eAAG,EAAC,EAAAY,YAAA,GAAArD,IAAI,CAACuD,MAAM,cAAAF,YAAA,uBAAXA,YAAA,CAAaN,MAAM,KAAI,CAAC,EAAC,SAAO;sBAAA;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACvE9E,OAAA;wBAAMyE,SAAS,EAAC,WAAW;wBAAAC,QAAA,GAAC,eAAG,EAACN,UAAU,CAACnC,IAAI,CAACwD,SAAS,CAAC;sBAAA;wBAAAd,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/D,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN9E,OAAA;oBAAKyE,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBAC3B1E,OAAA;sBACE+E,OAAO,EAAEA,CAAA,KAAMlB,cAAc,CAAC5B,IAAI,CAAE;sBACpCwC,SAAS,EAAC,iBAAiB;sBAC3BiB,KAAK,EAAC,WAAW;sBAAAhB,QAAA,EAClB;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACT9E,OAAA;sBACE+E,OAAO,EAAEA,CAAA,KAAM1B,gBAAgB,CAACpB,IAAI,CAAE;sBACtCwC,SAAS,EAAC,mBAAmB;sBAC7BiB,KAAK,EAAC,aAAa;sBAAAhB,QAAA,EACpB;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA,GA/BE7C,IAAI,CAACa,EAAE;kBAAA6B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAgCZ,CAAC;cAAA,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA,GA1CEZ,WAAW;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA2ChB,CACN,CAAC;QAAA,GArDMb,WAAW;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAsDhB,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGL7D,aAAa,iBACZjB,OAAA;MAAKyE,SAAS,EAAC,sBAAsB;MAAAC,QAAA,eACnC1E,OAAA;QAAKyE,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrC1E,OAAA;UAAKyE,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eACpC1E,OAAA;YAAA0E,QAAA,EAAI;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eACN9E,OAAA;UAAKyE,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAClC1E,OAAA;YAAA0E,QAAA,GAAG,2CAAyC,eAAA1E,OAAA;cAAA0E,QAAA,GAAQ,IAAC,EAACzD,aAAa,CAAC8B,IAAI,EAAC,IAAC;YAAA;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACxF9E,OAAA;YAAGyE,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAA6B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC,eACN9E,OAAA;UAAKyE,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrC1E,OAAA;YAAQ+E,OAAO,EAAEnB,YAAa;YAACa,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACrE9E,OAAA;YAAQ+E,OAAO,EAAEzB,aAAc;YAACmB,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACN;EAED,OAAOzE,QAAQ,gBACbL,OAAA;IAAKyE,SAAS,EAAC,kBAAkB;IAAAC,QAAA,EAC9BF;EAAO;IAAAG,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC,gBAEN9E,OAAA;IAAKyE,SAAS,EAAC,iBAAiB;IAAAC,QAAA,EAC7BF;EAAO;IAAAG,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CACN;AACH,CAAC;AAACxE,EAAA,CA7QIL,aAAa;AAAA0F,EAAA,GAAb1F,aAAa;AA+QnB,eAAeA,aAAa;AAAC,IAAA0F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}