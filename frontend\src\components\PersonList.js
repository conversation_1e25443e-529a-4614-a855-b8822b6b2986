import React, { useState, useEffect } from 'react';
import apiService from '../services/apiService';
import HierarchicalSelector from './forms/HierarchicalSelector';
import './PersonList.css';

const PersonList = ({ onEditPerson }) => {
  const [persons, setPersons] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [searchFilters, setSearchFilters] = useState({
    name: '',
    mobileNumber: '',
    email: '',
    divisionId: null,
    categoryId: null,
    subCategoryId: null
  });
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 10,
    totalCount: 0,
    totalPages: 0
  });

  useEffect(() => {
    loadPersons();
  }, [pagination.page, pagination.pageSize]);

  const loadPersons = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const searchRequest = {
        page: pagination.page,
        pageSize: pagination.pageSize,
        sortBy: 'createdAt',
        sortDirection: 'desc',
        includeDivision: true,
        includeCategory: true,
        includeSubCategory: true,
        ...searchFilters
      };

      const response = await apiService.searchPersons(searchRequest);
      setPersons(response.data.persons || []);
      setPagination(prev => ({
        ...prev,
        totalCount: response.data.totalCount || 0,
        totalPages: response.data.totalPages || 0
      }));
    } catch (err) {
      console.error('Error loading persons:', err);
      setError('Failed to load persons. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    setPagination(prev => ({ ...prev, page: 1 }));
    loadPersons();
  };

  const handleFilterChange = (key, value) => {
    setSearchFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleHierarchyChange = (selection) => {
    setSearchFilters(prev => ({
      ...prev,
      divisionId: selection.divisionId,
      categoryId: selection.categoryId,
      subCategoryId: selection.subCategoryId
    }));
  };

  const handlePageChange = (newPage) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  const handlePageSizeChange = (newPageSize) => {
    setPagination(prev => ({ ...prev, pageSize: newPageSize, page: 1 }));
  };

  const clearFilters = () => {
    setSearchFilters({
      name: '',
      mobileNumber: '',
      email: '',
      divisionId: null,
      categoryId: null,
      subCategoryId: null
    });
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString();
  };

  const renderPagination = () => {
    const { page, totalPages } = pagination;
    const pages = [];
    
    // Calculate page range
    const startPage = Math.max(1, page - 2);
    const endPage = Math.min(totalPages, page + 2);
    
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return (
      <div className="pagination">
        <button
          onClick={() => handlePageChange(page - 1)}
          disabled={page === 1}
          className="pagination-btn"
        >
          Previous
        </button>
        
        {startPage > 1 && (
          <>
            <button onClick={() => handlePageChange(1)} className="pagination-btn">1</button>
            {startPage > 2 && <span className="pagination-ellipsis">...</span>}
          </>
        )}
        
        {pages.map(pageNum => (
          <button
            key={pageNum}
            onClick={() => handlePageChange(pageNum)}
            className={`pagination-btn ${pageNum === page ? 'active' : ''}`}
          >
            {pageNum}
          </button>
        ))}
        
        {endPage < totalPages && (
          <>
            {endPage < totalPages - 1 && <span className="pagination-ellipsis">...</span>}
            <button onClick={() => handlePageChange(totalPages)} className="pagination-btn">
              {totalPages}
            </button>
          </>
        )}
        
        <button
          onClick={() => handlePageChange(page + 1)}
          disabled={page === totalPages}
          className="pagination-btn"
        >
          Next
        </button>
      </div>
    );
  };

  return (
    <div className="person-list">
      <div className="list-header">
        <div className="header-title">
          <h2>Person Directory</h2>
        </div>

        {/* Search and Filters */}
        <div className="search-section">
          <div className="search-filters">
            <div className="filter-group">
              <input
                type="text"
                placeholder="Search by name..."
                value={searchFilters.name}
                onChange={(e) => handleFilterChange('name', e.target.value)}
                className="search-input"
              />
            </div>
            
            <div className="filter-group">
              <input
                type="text"
                placeholder="Search by mobile..."
                value={searchFilters.mobileNumber}
                onChange={(e) => handleFilterChange('mobileNumber', e.target.value)}
                className="search-input"
              />
            </div>
            
            <div className="filter-group">
              <input
                type="email"
                placeholder="Search by email..."
                value={searchFilters.email}
                onChange={(e) => handleFilterChange('email', e.target.value)}
                className="search-input"
              />
            </div>
          </div>

          <div className="hierarchy-filter">
            <HierarchicalSelector
              onSelectionChange={handleHierarchyChange}
              showLabels={false}
            />
          </div>

          <div className="search-actions">
            <button onClick={handleSearch} className="btn btn-primary">
              🔍 Search
            </button>
            <button onClick={clearFilters} className="btn btn-outline">
              🗑️ Clear
            </button>
          </div>
        </div>
      </div>

      {/* Results Summary */}
      <div className="results-summary">
        <div className="summary-info">
          <span>Showing {persons.length} of {pagination.totalCount} persons</span>
          <div className="page-size-selector">
            <label>Show:</label>
            <select
              value={pagination.pageSize}
              onChange={(e) => handlePageSizeChange(parseInt(e.target.value))}
            >
              <option value={10}>10</option>
              <option value={25}>25</option>
              <option value={50}>50</option>
              <option value={100}>100</option>
            </select>
            <span>per page</span>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="alert alert-error">
          {error}
          <button onClick={loadPersons} className="retry-btn">Retry</button>
        </div>
      )}

      {/* Loading State */}
      {loading && (
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading persons...</p>
        </div>
      )}

      {/* Person Table */}
      {!loading && !error && (
        <div className="person-table-container">
          <table className="person-table">
            <thead>
              <tr>
                <th>Name</th>
                <th>Mobile</th>
                <th>Email</th>
                <th>Division</th>
                <th>Category</th>
                <th>Nature</th>
                <th>Created</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {persons.length === 0 ? (
                <tr>
                  <td colSpan="8" className="no-data">
                    No persons found.
                  </td>
                </tr>
              ) : (
                persons.map(person => (
                  <tr key={person.id}>
                    <td>
                      <div className="person-name">
                        <strong>{person.name}</strong>
                        {person.firmName && (
                          <div className="firm-name">{person.firmName}</div>
                        )}
                      </div>
                    </td>
                    <td>
                      <div className="contact-info">
                        <div>{person.mobileNumber}</div>
                        {person.alternateNumbers.length > 0 && (
                          <div className="alternate">+{person.alternateNumbers.length} more</div>
                        )}
                      </div>
                    </td>
                    <td>
                      <div className="email-info">
                        {person.primaryEmailId && (
                          <div>{person.primaryEmailId}</div>
                        )}
                        {person.alternateEmailIds.length > 0 && (
                          <div className="alternate">+{person.alternateEmailIds.length} more</div>
                        )}
                      </div>
                    </td>
                    <td>{person.division?.name}</td>
                    <td>
                      <div>
                        {person.category?.name}
                        {person.subCategory && (
                          <div className="subcategory">{person.subCategory.name}</div>
                        )}
                      </div>
                    </td>
                    <td>
                      <span className={`nature-badge nature-${person.nature}`}>
                        {person.natureDisplay}
                      </span>
                    </td>
                    <td>{formatDate(person.createdAt)}</td>
                    <td>
                      <div className="action-buttons">
                        <button
                          onClick={() => onEditPerson(person)}
                          className="btn-action edit"
                          title="Edit person"
                        >
                          ✏️
                        </button>
                        <button
                          onClick={() => {/* Handle view */}}
                          className="btn-action view"
                          title="View details"
                        >
                          👁️
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      )}

      {/* Pagination */}
      {!loading && !error && pagination.totalPages > 1 && (
        <div className="pagination-container">
          {renderPagination()}
        </div>
      )}
    </div>
  );
};

export default PersonList;
