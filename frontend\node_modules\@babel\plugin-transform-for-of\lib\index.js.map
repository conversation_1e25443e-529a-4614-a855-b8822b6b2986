{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_core", "_noHelperImplementation", "_helperSkipTransparentExpressionWrappers", "buildLoopBody", "path", "declar", "newBody", "block", "bodyPath", "get", "body", "node", "t", "isBlockStatement", "Object", "keys", "getBindingIdentifiers", "some", "id", "scope", "hasOwnBinding", "blockStatement", "toBlock", "unshift", "_default", "exports", "default", "declare", "api", "options", "_options$assumeArray", "_options$allowArrayLi", "_api$assumption", "assertVersion", "<PERSON><PERSON><PERSON><PERSON>", "allowArrayLike", "loose", "Error", "test", "version", "iterableIsArray", "assumption", "arrayLikeIsIterable", "skipIteratorClosing", "name", "visitor", "ForOfStatement", "left", "await", "isAwait", "right", "skipTransparentExprWrapperNodes", "i", "generateUidIdentifier", "array", "maybeGenerateMemoised", "isIdentifier", "inits", "variableDeclarator", "numericLiteral", "push", "item", "memberExpression", "cloneNode", "assignment", "isVariableDeclaration", "declarations", "init", "expressionStatement", "assignmentExpression", "replaceWith", "forStatement", "variableDeclaration", "binaryExpression", "identifier", "updateExpression", "buildForOfArray", "template", "buildForOfNoIteratorClosing", "statements", "buildForOf", "builder", "build", "helper", "getContainer", "nodes", "_ForOfStatementArray", "generateUidIdentifierBasedOnNode", "iterationKey", "loop", "BODY", "KEY", "NAME", "ARR", "inherits", "iterationValue", "state", "isArrayExpression", "isGenericType", "isArrayTypeAnnotation", "getTypeAnnotation", "availableHelper", "transformWithoutHelper", "parent", "<PERSON><PERSON><PERSON>", "generateUid", "<PERSON><PERSON><PERSON><PERSON>", "kind", "CREATE_ITERATOR_HELPER", "addHelper", "ITERATOR_HELPER", "ARRAY_LIKE_IS_ITERABLE", "booleanLiteral", "STEP_KEY", "OBJECT", "container", "isLabeledStatement", "labeledStatement", "label", "parentPath", "replaceWithMultiple", "skip"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport { template, types as t, type NodePath } from \"@babel/core\";\n\nimport transformWithoutHelper from \"./no-helper-implementation.ts\" with { if: \"!process.env.BABEL_8_BREAKING\" };\nimport { skipTransparentExprWrapperNodes } from \"@babel/helper-skip-transparent-expression-wrappers\";\n\nexport interface Options {\n  allowArrayLike?: boolean;\n  assumeArray?: boolean;\n  loose?: boolean;\n}\n\nfunction buildLoopBody(\n  path: NodePath<t.ForXStatement>,\n  declar: t.Statement,\n  newBody?: t.Statement | t.Expression,\n) {\n  let block;\n  const bodyPath = path.get(\"body\");\n  const body = newBody ?? bodyPath.node;\n  if (\n    t.isBlockStatement(body) &&\n    Object.keys(path.getBindingIdentifiers()).some(id =>\n      bodyPath.scope.hasOwnBinding(id),\n    )\n  ) {\n    block = t.blockStatement([declar, body]);\n  } else {\n    block = t.toBlock(body);\n    block.body.unshift(declar);\n  }\n  return block;\n}\n\nexport default declare((api, options: Options) => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  {\n    const { assumeArray, allowArrayLike, loose } = options;\n\n    if (loose === true && assumeArray === true) {\n      throw new Error(\n        `The loose and assumeArray options cannot be used together in @babel/plugin-transform-for-of`,\n      );\n    }\n\n    if (assumeArray === true && allowArrayLike === true) {\n      throw new Error(\n        `The assumeArray and allowArrayLike options cannot be used together in @babel/plugin-transform-for-of`,\n      );\n    }\n\n    if (!process.env.BABEL_8_BREAKING) {\n      // TODO: Remove in Babel 8\n      if (allowArrayLike && /^7\\.\\d\\./.test(api.version)) {\n        throw new Error(\n          `The allowArrayLike is only supported when using @babel/core@^7.10.0`,\n        );\n      }\n    }\n  }\n\n  const iterableIsArray =\n    options.assumeArray ??\n    // Loose mode is not compatible with 'assumeArray', so we shouldn't read\n    // 'iterableIsArray' if 'loose' is true.\n    (!options.loose && api.assumption(\"iterableIsArray\"));\n\n  const arrayLikeIsIterable =\n    options.allowArrayLike ?? api.assumption(\"arrayLikeIsIterable\");\n\n  const skipIteratorClosing =\n    api.assumption(\"skipForOfIteratorClosing\") ?? options.loose;\n\n  if (iterableIsArray && arrayLikeIsIterable) {\n    throw new Error(\n      `The \"iterableIsArray\" and \"arrayLikeIsIterable\" assumptions are not compatible.`,\n    );\n  }\n\n  if (iterableIsArray) {\n    return {\n      name: \"transform-for-of\",\n\n      visitor: {\n        ForOfStatement(path) {\n          const { scope } = path;\n          const { left, await: isAwait } = path.node;\n          if (isAwait) {\n            return;\n          }\n          // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n          const right = skipTransparentExprWrapperNodes(\n            path.node.right,\n          ) as t.Expression;\n          const i = scope.generateUidIdentifier(\"i\");\n          let array: t.Identifier | t.ThisExpression =\n            scope.maybeGenerateMemoised(right, true);\n          if (\n            !array &&\n            t.isIdentifier(right) &&\n            path.get(\"body\").scope.hasOwnBinding(right.name)\n          ) {\n            array = scope.generateUidIdentifier(\"arr\");\n          }\n\n          const inits = [t.variableDeclarator(i, t.numericLiteral(0))];\n          if (array) {\n            inits.push(t.variableDeclarator(array, right));\n          } else {\n            array = right as t.Identifier | t.ThisExpression;\n          }\n\n          const item = t.memberExpression(\n            t.cloneNode(array),\n            t.cloneNode(i),\n            true,\n          );\n          let assignment;\n          if (t.isVariableDeclaration(left)) {\n            assignment = left;\n            assignment.declarations[0].init = item;\n          } else {\n            assignment = t.expressionStatement(\n              t.assignmentExpression(\"=\", left, item),\n            );\n          }\n\n          path.replaceWith(\n            t.forStatement(\n              t.variableDeclaration(\"let\", inits),\n              t.binaryExpression(\n                \"<\",\n                t.cloneNode(i),\n                t.memberExpression(t.cloneNode(array), t.identifier(\"length\")),\n              ),\n              t.updateExpression(\"++\", t.cloneNode(i)),\n              buildLoopBody(path, assignment),\n            ),\n          );\n        },\n      },\n    };\n  }\n\n  const buildForOfArray = template`\n    for (var KEY = 0, NAME = ARR; KEY < NAME.length; KEY++) BODY;\n  `;\n\n  const buildForOfNoIteratorClosing = template.statements`\n    for (var ITERATOR_HELPER = CREATE_ITERATOR_HELPER(OBJECT, ARRAY_LIKE_IS_ITERABLE), STEP_KEY;\n        !(STEP_KEY = ITERATOR_HELPER()).done;) BODY;\n  `;\n\n  const buildForOf = template.statements`\n    var ITERATOR_HELPER = CREATE_ITERATOR_HELPER(OBJECT, ARRAY_LIKE_IS_ITERABLE), STEP_KEY;\n    try {\n      for (ITERATOR_HELPER.s(); !(STEP_KEY = ITERATOR_HELPER.n()).done;) BODY;\n    } catch (err) {\n      ITERATOR_HELPER.e(err);\n    } finally {\n      ITERATOR_HELPER.f();\n    }\n  `;\n\n  const builder = skipIteratorClosing\n    ? {\n        build: buildForOfNoIteratorClosing,\n        helper: \"createForOfIteratorHelperLoose\",\n        getContainer: (nodes: t.Statement[]): [t.ForStatement] =>\n          nodes as [t.ForStatement],\n      }\n    : {\n        build: buildForOf,\n        helper: \"createForOfIteratorHelper\",\n        getContainer: (nodes: t.Statement[]): [t.ForStatement] =>\n          (nodes[1] as t.TryStatement).block.body as [t.ForStatement],\n      };\n\n  function _ForOfStatementArray(path: NodePath<t.ForOfStatement>) {\n    const { node, scope } = path;\n\n    const right = scope.generateUidIdentifierBasedOnNode(node.right, \"arr\");\n    const iterationKey = scope.generateUidIdentifier(\"i\");\n\n    const loop = buildForOfArray({\n      BODY: node.body,\n      KEY: iterationKey,\n      NAME: right,\n      ARR: node.right,\n    }) as t.For;\n\n    t.inherits(loop, node);\n\n    const iterationValue = t.memberExpression(\n      t.cloneNode(right),\n      t.cloneNode(iterationKey),\n      true,\n    );\n\n    let declar;\n    const left = node.left;\n    if (t.isVariableDeclaration(left)) {\n      left.declarations[0].init = iterationValue;\n      declar = left;\n    } else {\n      declar = t.expressionStatement(\n        t.assignmentExpression(\"=\", left, iterationValue),\n      );\n    }\n\n    loop.body = buildLoopBody(path, declar, loop.body);\n\n    return loop;\n  }\n\n  return {\n    name: \"transform-for-of\",\n    visitor: {\n      ForOfStatement(path, state) {\n        const right = path.get(\"right\");\n        if (\n          right.isArrayExpression() ||\n          (process.env.BABEL_8_BREAKING\n            ? right.isGenericType(\"Array\")\n            : right.isGenericType(\"Array\") ||\n              t.isArrayTypeAnnotation(right.getTypeAnnotation()))\n        ) {\n          path.replaceWith(_ForOfStatementArray(path));\n          return;\n        }\n\n        if (!process.env.BABEL_8_BREAKING) {\n          if (!state.availableHelper(builder.helper)) {\n            // Babel <7.9.0 doesn't support this helper\n            transformWithoutHelper(skipIteratorClosing, path, state);\n            return;\n          }\n        }\n\n        const { node, parent, scope } = path;\n        const left = node.left;\n        let declar;\n\n        const stepKey = scope.generateUid(\"step\");\n        const stepValue = t.memberExpression(\n          t.identifier(stepKey),\n          t.identifier(\"value\"),\n        );\n\n        if (t.isVariableDeclaration(left)) {\n          // for (let i of test)\n          declar = t.variableDeclaration(left.kind, [\n            t.variableDeclarator(left.declarations[0].id, stepValue),\n          ]);\n        } else {\n          // for (i of test), for ({ i } of test)\n          declar = t.expressionStatement(\n            t.assignmentExpression(\"=\", left, stepValue),\n          );\n        }\n\n        const nodes = builder.build({\n          CREATE_ITERATOR_HELPER: state.addHelper(builder.helper),\n          ITERATOR_HELPER: scope.generateUidIdentifier(\"iterator\"),\n          ARRAY_LIKE_IS_ITERABLE: arrayLikeIsIterable\n            ? t.booleanLiteral(true)\n            : null,\n          STEP_KEY: t.identifier(stepKey),\n          OBJECT: node.right,\n          BODY: buildLoopBody(path, declar),\n        });\n        const container = builder.getContainer(nodes);\n\n        t.inherits(container[0], node);\n        t.inherits(container[0].body, node.body);\n\n        if (t.isLabeledStatement(parent)) {\n          // @ts-expect-error replacing node types\n          container[0] = t.labeledStatement(parent.label, container[0]);\n\n          path.parentPath.replaceWithMultiple(nodes);\n\n          // The parent has been replaced, prevent Babel from traversing a detached path\n          path.skip();\n        } else {\n          path.replaceWithMultiple(nodes);\n        }\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AAEA,IAAAE,uBAAA,GAAAF,OAAA;AACA,IAAAG,wCAAA,GAAAH,OAAA;AAQA,SAASI,aAAaA,CACpBC,IAA+B,EAC/BC,MAAmB,EACnBC,OAAoC,EACpC;EACA,IAAIC,KAAK;EACT,MAAMC,QAAQ,GAAGJ,IAAI,CAACK,GAAG,CAAC,MAAM,CAAC;EACjC,MAAMC,IAAI,GAAGJ,OAAO,WAAPA,OAAO,GAAIE,QAAQ,CAACG,IAAI;EACrC,IACEC,WAAC,CAACC,gBAAgB,CAACH,IAAI,CAAC,IACxBI,MAAM,CAACC,IAAI,CAACX,IAAI,CAACY,qBAAqB,CAAC,CAAC,CAAC,CAACC,IAAI,CAACC,EAAE,IAC/CV,QAAQ,CAACW,KAAK,CAACC,aAAa,CAACF,EAAE,CACjC,CAAC,EACD;IACAX,KAAK,GAAGK,WAAC,CAACS,cAAc,CAAC,CAAChB,MAAM,EAAEK,IAAI,CAAC,CAAC;EAC1C,CAAC,MAAM;IACLH,KAAK,GAAGK,WAAC,CAACU,OAAO,CAACZ,IAAI,CAAC;IACvBH,KAAK,CAACG,IAAI,CAACa,OAAO,CAAClB,MAAM,CAAC;EAC5B;EACA,OAAOE,KAAK;AACd;AAAC,IAAAiB,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEc,IAAAC,0BAAO,EAAC,CAACC,GAAG,EAAEC,OAAgB,KAAK;EAAA,IAAAC,oBAAA,EAAAC,qBAAA,EAAAC,eAAA;EAChDJ,GAAG,CAACK,aAAa,CAAkB,CAAE,CAAC;EAEtC;IACE,MAAM;MAAEC,WAAW;MAAEC,cAAc;MAAEC;IAAM,CAAC,GAAGP,OAAO;IAEtD,IAAIO,KAAK,KAAK,IAAI,IAAIF,WAAW,KAAK,IAAI,EAAE;MAC1C,MAAM,IAAIG,KAAK,CACb,6FACF,CAAC;IACH;IAEA,IAAIH,WAAW,KAAK,IAAI,IAAIC,cAAc,KAAK,IAAI,EAAE;MACnD,MAAM,IAAIE,KAAK,CACb,sGACF,CAAC;IACH;IAEmC;MAEjC,IAAIF,cAAc,IAAI,UAAU,CAACG,IAAI,CAACV,GAAG,CAACW,OAAO,CAAC,EAAE;QAClD,MAAM,IAAIF,KAAK,CACb,qEACF,CAAC;MACH;IACF;EACF;EAEA,MAAMG,eAAe,IAAAV,oBAAA,GACnBD,OAAO,CAACK,WAAW,YAAAJ,oBAAA,GAGlB,CAACD,OAAO,CAACO,KAAK,IAAIR,GAAG,CAACa,UAAU,CAAC,iBAAiB,CAAE;EAEvD,MAAMC,mBAAmB,IAAAX,qBAAA,GACvBF,OAAO,CAACM,cAAc,YAAAJ,qBAAA,GAAIH,GAAG,CAACa,UAAU,CAAC,qBAAqB,CAAC;EAEjE,MAAME,mBAAmB,IAAAX,eAAA,GACvBJ,GAAG,CAACa,UAAU,CAAC,0BAA0B,CAAC,YAAAT,eAAA,GAAIH,OAAO,CAACO,KAAK;EAE7D,IAAII,eAAe,IAAIE,mBAAmB,EAAE;IAC1C,MAAM,IAAIL,KAAK,CACb,iFACF,CAAC;EACH;EAEA,IAAIG,eAAe,EAAE;IACnB,OAAO;MACLI,IAAI,EAAE,kBAAkB;MAExBC,OAAO,EAAE;QACPC,cAAcA,CAAC1C,IAAI,EAAE;UACnB,MAAM;YAAEe;UAAM,CAAC,GAAGf,IAAI;UACtB,MAAM;YAAE2C,IAAI;YAAEC,KAAK,EAAEC;UAAQ,CAAC,GAAG7C,IAAI,CAACO,IAAI;UAC1C,IAAIsC,OAAO,EAAE;YACX;UACF;UAEA,MAAMC,KAAK,GAAG,IAAAC,wEAA+B,EAC3C/C,IAAI,CAACO,IAAI,CAACuC,KACZ,CAAiB;UACjB,MAAME,CAAC,GAAGjC,KAAK,CAACkC,qBAAqB,CAAC,GAAG,CAAC;UAC1C,IAAIC,KAAsC,GACxCnC,KAAK,CAACoC,qBAAqB,CAACL,KAAK,EAAE,IAAI,CAAC;UAC1C,IACE,CAACI,KAAK,IACN1C,WAAC,CAAC4C,YAAY,CAACN,KAAK,CAAC,IACrB9C,IAAI,CAACK,GAAG,CAAC,MAAM,CAAC,CAACU,KAAK,CAACC,aAAa,CAAC8B,KAAK,CAACN,IAAI,CAAC,EAChD;YACAU,KAAK,GAAGnC,KAAK,CAACkC,qBAAqB,CAAC,KAAK,CAAC;UAC5C;UAEA,MAAMI,KAAK,GAAG,CAAC7C,WAAC,CAAC8C,kBAAkB,CAACN,CAAC,EAAExC,WAAC,CAAC+C,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5D,IAAIL,KAAK,EAAE;YACTG,KAAK,CAACG,IAAI,CAAChD,WAAC,CAAC8C,kBAAkB,CAACJ,KAAK,EAAEJ,KAAK,CAAC,CAAC;UAChD,CAAC,MAAM;YACLI,KAAK,GAAGJ,KAAwC;UAClD;UAEA,MAAMW,IAAI,GAAGjD,WAAC,CAACkD,gBAAgB,CAC7BlD,WAAC,CAACmD,SAAS,CAACT,KAAK,CAAC,EAClB1C,WAAC,CAACmD,SAAS,CAACX,CAAC,CAAC,EACd,IACF,CAAC;UACD,IAAIY,UAAU;UACd,IAAIpD,WAAC,CAACqD,qBAAqB,CAAClB,IAAI,CAAC,EAAE;YACjCiB,UAAU,GAAGjB,IAAI;YACjBiB,UAAU,CAACE,YAAY,CAAC,CAAC,CAAC,CAACC,IAAI,GAAGN,IAAI;UACxC,CAAC,MAAM;YACLG,UAAU,GAAGpD,WAAC,CAACwD,mBAAmB,CAChCxD,WAAC,CAACyD,oBAAoB,CAAC,GAAG,EAAEtB,IAAI,EAAEc,IAAI,CACxC,CAAC;UACH;UAEAzD,IAAI,CAACkE,WAAW,CACd1D,WAAC,CAAC2D,YAAY,CACZ3D,WAAC,CAAC4D,mBAAmB,CAAC,KAAK,EAAEf,KAAK,CAAC,EACnC7C,WAAC,CAAC6D,gBAAgB,CAChB,GAAG,EACH7D,WAAC,CAACmD,SAAS,CAACX,CAAC,CAAC,EACdxC,WAAC,CAACkD,gBAAgB,CAAClD,WAAC,CAACmD,SAAS,CAACT,KAAK,CAAC,EAAE1C,WAAC,CAAC8D,UAAU,CAAC,QAAQ,CAAC,CAC/D,CAAC,EACD9D,WAAC,CAAC+D,gBAAgB,CAAC,IAAI,EAAE/D,WAAC,CAACmD,SAAS,CAACX,CAAC,CAAC,CAAC,EACxCjD,aAAa,CAACC,IAAI,EAAE4D,UAAU,CAChC,CACF,CAAC;QACH;MACF;IACF,CAAC;EACH;EAEA,MAAMY,eAAe,GAAG,IAAAC,cAAQ;AAClC;AACA,GAAG;EAED,MAAMC,2BAA2B,GAAGD,cAAQ,CAACE,UAAU;AACzD;AACA;AACA,GAAG;EAED,MAAMC,UAAU,GAAGH,cAAQ,CAACE,UAAU;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;EAED,MAAME,OAAO,GAAGtC,mBAAmB,GAC/B;IACEuC,KAAK,EAAEJ,2BAA2B;IAClCK,MAAM,EAAE,gCAAgC;IACxCC,YAAY,EAAGC,KAAoB,IACjCA;EACJ,CAAC,GACD;IACEH,KAAK,EAAEF,UAAU;IACjBG,MAAM,EAAE,2BAA2B;IACnCC,YAAY,EAAGC,KAAoB,IAChCA,KAAK,CAAC,CAAC,CAAC,CAAoB9E,KAAK,CAACG;EACvC,CAAC;EAEL,SAAS4E,oBAAoBA,CAAClF,IAAgC,EAAE;IAC9D,MAAM;MAAEO,IAAI;MAAEQ;IAAM,CAAC,GAAGf,IAAI;IAE5B,MAAM8C,KAAK,GAAG/B,KAAK,CAACoE,gCAAgC,CAAC5E,IAAI,CAACuC,KAAK,EAAE,KAAK,CAAC;IACvE,MAAMsC,YAAY,GAAGrE,KAAK,CAACkC,qBAAqB,CAAC,GAAG,CAAC;IAErD,MAAMoC,IAAI,GAAGb,eAAe,CAAC;MAC3Bc,IAAI,EAAE/E,IAAI,CAACD,IAAI;MACfiF,GAAG,EAAEH,YAAY;MACjBI,IAAI,EAAE1C,KAAK;MACX2C,GAAG,EAAElF,IAAI,CAACuC;IACZ,CAAC,CAAU;IAEXtC,WAAC,CAACkF,QAAQ,CAACL,IAAI,EAAE9E,IAAI,CAAC;IAEtB,MAAMoF,cAAc,GAAGnF,WAAC,CAACkD,gBAAgB,CACvClD,WAAC,CAACmD,SAAS,CAACb,KAAK,CAAC,EAClBtC,WAAC,CAACmD,SAAS,CAACyB,YAAY,CAAC,EACzB,IACF,CAAC;IAED,IAAInF,MAAM;IACV,MAAM0C,IAAI,GAAGpC,IAAI,CAACoC,IAAI;IACtB,IAAInC,WAAC,CAACqD,qBAAqB,CAAClB,IAAI,CAAC,EAAE;MACjCA,IAAI,CAACmB,YAAY,CAAC,CAAC,CAAC,CAACC,IAAI,GAAG4B,cAAc;MAC1C1F,MAAM,GAAG0C,IAAI;IACf,CAAC,MAAM;MACL1C,MAAM,GAAGO,WAAC,CAACwD,mBAAmB,CAC5BxD,WAAC,CAACyD,oBAAoB,CAAC,GAAG,EAAEtB,IAAI,EAAEgD,cAAc,CAClD,CAAC;IACH;IAEAN,IAAI,CAAC/E,IAAI,GAAGP,aAAa,CAACC,IAAI,EAAEC,MAAM,EAAEoF,IAAI,CAAC/E,IAAI,CAAC;IAElD,OAAO+E,IAAI;EACb;EAEA,OAAO;IACL7C,IAAI,EAAE,kBAAkB;IACxBC,OAAO,EAAE;MACPC,cAAcA,CAAC1C,IAAI,EAAE4F,KAAK,EAAE;QAC1B,MAAM9C,KAAK,GAAG9C,IAAI,CAACK,GAAG,CAAC,OAAO,CAAC;QAC/B,IACEyC,KAAK,CAAC+C,iBAAiB,CAAC,CAAC,IAGrB/C,KAAK,CAACgD,aAAa,CAAC,OAAO,CAAC,IAC5BtF,WAAC,CAACuF,qBAAqB,CAACjD,KAAK,CAACkD,iBAAiB,CAAC,CAAC,CAAE,EACvD;UACAhG,IAAI,CAACkE,WAAW,CAACgB,oBAAoB,CAAClF,IAAI,CAAC,CAAC;UAC5C;QACF;QAEmC;UACjC,IAAI,CAAC4F,KAAK,CAACK,eAAe,CAACpB,OAAO,CAACE,MAAM,CAAC,EAAE;YAE1C,IAAAmB,+BAAsB,EAAC3D,mBAAmB,EAAEvC,IAAI,EAAE4F,KAAK,CAAC;YACxD;UACF;QACF;QAEA,MAAM;UAAErF,IAAI;UAAE4F,MAAM;UAAEpF;QAAM,CAAC,GAAGf,IAAI;QACpC,MAAM2C,IAAI,GAAGpC,IAAI,CAACoC,IAAI;QACtB,IAAI1C,MAAM;QAEV,MAAMmG,OAAO,GAAGrF,KAAK,CAACsF,WAAW,CAAC,MAAM,CAAC;QACzC,MAAMC,SAAS,GAAG9F,WAAC,CAACkD,gBAAgB,CAClClD,WAAC,CAAC8D,UAAU,CAAC8B,OAAO,CAAC,EACrB5F,WAAC,CAAC8D,UAAU,CAAC,OAAO,CACtB,CAAC;QAED,IAAI9D,WAAC,CAACqD,qBAAqB,CAAClB,IAAI,CAAC,EAAE;UAEjC1C,MAAM,GAAGO,WAAC,CAAC4D,mBAAmB,CAACzB,IAAI,CAAC4D,IAAI,EAAE,CACxC/F,WAAC,CAAC8C,kBAAkB,CAACX,IAAI,CAACmB,YAAY,CAAC,CAAC,CAAC,CAAChD,EAAE,EAAEwF,SAAS,CAAC,CACzD,CAAC;QACJ,CAAC,MAAM;UAELrG,MAAM,GAAGO,WAAC,CAACwD,mBAAmB,CAC5BxD,WAAC,CAACyD,oBAAoB,CAAC,GAAG,EAAEtB,IAAI,EAAE2D,SAAS,CAC7C,CAAC;QACH;QAEA,MAAMrB,KAAK,GAAGJ,OAAO,CAACC,KAAK,CAAC;UAC1B0B,sBAAsB,EAAEZ,KAAK,CAACa,SAAS,CAAC5B,OAAO,CAACE,MAAM,CAAC;UACvD2B,eAAe,EAAE3F,KAAK,CAACkC,qBAAqB,CAAC,UAAU,CAAC;UACxD0D,sBAAsB,EAAErE,mBAAmB,GACvC9B,WAAC,CAACoG,cAAc,CAAC,IAAI,CAAC,GACtB,IAAI;UACRC,QAAQ,EAAErG,WAAC,CAAC8D,UAAU,CAAC8B,OAAO,CAAC;UAC/BU,MAAM,EAAEvG,IAAI,CAACuC,KAAK;UAClBwC,IAAI,EAAEvF,aAAa,CAACC,IAAI,EAAEC,MAAM;QAClC,CAAC,CAAC;QACF,MAAM8G,SAAS,GAAGlC,OAAO,CAACG,YAAY,CAACC,KAAK,CAAC;QAE7CzE,WAAC,CAACkF,QAAQ,CAACqB,SAAS,CAAC,CAAC,CAAC,EAAExG,IAAI,CAAC;QAC9BC,WAAC,CAACkF,QAAQ,CAACqB,SAAS,CAAC,CAAC,CAAC,CAACzG,IAAI,EAAEC,IAAI,CAACD,IAAI,CAAC;QAExC,IAAIE,WAAC,CAACwG,kBAAkB,CAACb,MAAM,CAAC,EAAE;UAEhCY,SAAS,CAAC,CAAC,CAAC,GAAGvG,WAAC,CAACyG,gBAAgB,CAACd,MAAM,CAACe,KAAK,EAAEH,SAAS,CAAC,CAAC,CAAC,CAAC;UAE7D/G,IAAI,CAACmH,UAAU,CAACC,mBAAmB,CAACnC,KAAK,CAAC;UAG1CjF,IAAI,CAACqH,IAAI,CAAC,CAAC;QACb,CAAC,MAAM;UACLrH,IAAI,CAACoH,mBAAmB,CAACnC,KAAK,CAAC;QACjC;MACF;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}