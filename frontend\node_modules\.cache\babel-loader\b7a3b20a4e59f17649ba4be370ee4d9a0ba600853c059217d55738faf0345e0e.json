{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Advanced\\\\Advanced CRM filed all use\\\\frontend\\\\src\\\\components\\\\forms\\\\FormBuilder.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport './FormBuilder.css';\nimport { PersonFieldDefinitions } from '../../data/PersonFieldDefinitions';\nimport apiService from '../../services/apiService';\nimport formConfigService from '../../services/formConfigService';\nimport FieldConfigModal from './FieldConfigModal';\nimport FormPreview from './FormPreview';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FormBuilder = ({\n  onSave,\n  onCancel,\n  initialConfig = null\n}) => {\n  _s();\n  // State management\n  const [formName, setFormName] = useState('');\n  const [formDescription, setFormDescription] = useState('');\n  const [selectedFields, setSelectedFields] = useState([]);\n  const [currentSection, setCurrentSection] = useState('all');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showPreview, setShowPreview] = useState(false);\n  const [showFieldConfig, setShowFieldConfig] = useState(false);\n  const [configField, setConfigField] = useState(null);\n  const [errors, setErrors] = useState({});\n  const [saving, setSaving] = useState(false);\n  const [isEditMode, setIsEditMode] = useState(false);\n  const [editingFormId, setEditingFormId] = useState(null);\n\n  // Hierarchy state\n  const [selectedHierarchy, setSelectedHierarchy] = useState({\n    divisionId: '',\n    categoryId: '',\n    subCategoryId: ''\n  });\n\n  // Data state\n  const [divisions, setDivisions] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [subCategories, setSubCategories] = useState([]);\n  const [loading, setLoading] = useState({\n    divisions: false,\n    categories: false,\n    subCategories: false\n  });\n\n  // Load initial data\n  useEffect(() => {\n    loadDivisions();\n    if (initialConfig) {\n      loadInitialConfig(initialConfig);\n    }\n  }, [initialConfig]);\n  const loadInitialConfig = config => {\n    setFormName(config.name || '');\n    setFormDescription(config.description || '');\n    setSelectedFields(config.fields || []);\n    setSelectedHierarchy({\n      divisionId: config.divisionId || '',\n      categoryId: config.categoryId || '',\n      subCategoryId: config.subCategoryId || ''\n    });\n    setIsEditMode(true);\n    setEditingFormId(config.id || config.key);\n  };\n  const loadDivisions = async () => {\n    try {\n      setLoading(prev => ({\n        ...prev,\n        divisions: true\n      }));\n      const response = await apiService.getDivisions();\n      setDivisions(response.data || []);\n    } catch (error) {\n      console.error('Error loading divisions:', error);\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        divisions: false\n      }));\n    }\n  };\n  const loadCategories = async divisionId => {\n    try {\n      setLoading(prev => ({\n        ...prev,\n        categories: true\n      }));\n      const response = await apiService.getCategories(divisionId);\n      setCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading categories:', error);\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        categories: false\n      }));\n    }\n  };\n  const loadSubCategories = async categoryId => {\n    try {\n      setLoading(prev => ({\n        ...prev,\n        subCategories: true\n      }));\n      const response = await apiService.getSubCategories(categoryId);\n      setSubCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading subcategories:', error);\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        subCategories: false\n      }));\n    }\n  };\n\n  // Event handlers\n  const handleDivisionChange = e => {\n    const divisionId = e.target.value;\n    setSelectedHierarchy({\n      divisionId,\n      categoryId: '',\n      subCategoryId: ''\n    });\n    setCategories([]);\n    setSubCategories([]);\n    if (divisionId) {\n      loadCategories(divisionId);\n    }\n  };\n  const handleCategoryChange = e => {\n    const categoryId = e.target.value;\n    setSelectedHierarchy(prev => ({\n      ...prev,\n      categoryId,\n      subCategoryId: ''\n    }));\n    setSubCategories([]);\n    if (categoryId) {\n      loadSubCategories(categoryId);\n    }\n  };\n  const handleSubCategoryChange = e => {\n    const subCategoryId = e.target.value;\n    setSelectedHierarchy(prev => ({\n      ...prev,\n      subCategoryId\n    }));\n  };\n  const handleFieldToggle = field => {\n    const isSelected = selectedFields.some(f => f.key === field.key);\n    if (isSelected) {\n      setSelectedFields(selectedFields.filter(f => f.key !== field.key));\n    } else {\n      setSelectedFields([...selectedFields, {\n        ...field\n      }]);\n    }\n  };\n  const handleFieldConfig = field => {\n    setConfigField(field);\n    setShowFieldConfig(true);\n  };\n  const handleFieldConfigSave = updatedField => {\n    setSelectedFields(selectedFields.map(f => f.key === updatedField.key ? updatedField : f));\n    setShowFieldConfig(false);\n    setConfigField(null);\n  };\n  const handleFieldMove = (fromIndex, toIndex) => {\n    const newFields = [...selectedFields];\n    const [movedField] = newFields.splice(fromIndex, 1);\n    newFields.splice(toIndex, 0, movedField);\n    setSelectedFields(newFields);\n  };\n  const handleSave = async () => {\n    if (!validateForm()) return;\n    setSaving(true);\n    try {\n      const formConfig = {\n        name: formName,\n        description: formDescription,\n        fields: selectedFields,\n        divisionId: selectedHierarchy.divisionId,\n        categoryId: selectedHierarchy.categoryId,\n        subCategoryId: selectedHierarchy.subCategoryId,\n        type: selectedHierarchy.subCategoryId ? 'subcategory' : 'category',\n        associatedId: selectedHierarchy.subCategoryId || selectedHierarchy.categoryId\n      };\n      if (isEditMode) {\n        formConfig.id = editingFormId;\n        await formConfigService.updateFormConfig(formConfig);\n      } else {\n        await formConfigService.saveFormConfig(formConfig);\n      }\n      if (onSave) {\n        onSave(formConfig, isEditMode);\n      }\n    } catch (error) {\n      console.error('Error saving form:', error);\n      setErrors({\n        general: 'Failed to save form. Please try again.'\n      });\n    } finally {\n      setSaving(false);\n    }\n  };\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formName.trim()) {\n      newErrors.formName = 'Form name is required';\n    }\n    if (!selectedHierarchy.divisionId) {\n      newErrors.hierarchy = 'Division is required';\n    }\n    if (!selectedHierarchy.categoryId) {\n      newErrors.hierarchy = 'Category is required';\n    }\n    if (selectedFields.length === 0) {\n      newErrors.fields = 'At least one field must be selected';\n    }\n\n    // Skip form creation validation in edit mode\n    if (selectedHierarchy.categoryId && !isEditMode) {\n      const formValidation = formConfigService.validateFormCreation(selectedHierarchy.categoryId, selectedHierarchy.subCategoryId);\n      if (!formValidation.isValid) {\n        newErrors.formCreation = formValidation.errors.join('. ');\n      }\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  // Get available fields\n  const getAvailableFields = () => {\n    const allFields = [];\n    Object.entries(PersonFieldDefinitions).forEach(([sectionKey, sectionDef]) => {\n      sectionDef.fields.forEach(field => {\n        allFields.push({\n          ...field,\n          section: sectionKey\n        });\n      });\n    });\n    return allFields;\n  };\n  const getFilteredFields = () => {\n    const availableFields = getAvailableFields();\n    let filtered = availableFields;\n    if (currentSection !== 'all') {\n      filtered = filtered.filter(field => field.section === currentSection);\n    }\n    if (searchTerm) {\n      filtered = filtered.filter(field => field.label.toLowerCase().includes(searchTerm.toLowerCase()) || field.type.toLowerCase().includes(searchTerm.toLowerCase()));\n    }\n    return filtered;\n  };\n  const getSectionCounts = () => {\n    const availableFields = getAvailableFields();\n    const counts = {\n      all: availableFields.length\n    };\n    Object.keys(PersonFieldDefinitions).forEach(sectionKey => {\n      counts[sectionKey] = availableFields.filter(f => f.section === sectionKey).length;\n    });\n    return counts;\n  };\n  const sectionCounts = getSectionCounts();\n  const filteredFields = getFilteredFields();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"form-builder\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-builder-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Form Builder\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: () => setShowPreview(true),\n          className: \"btn btn-secondary\",\n          disabled: selectedFields.length === 0,\n          children: \"Preview Form\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: handleSave,\n          className: \"btn btn-primary\",\n          disabled: saving || errors.formCreation,\n          children: saving ? isEditMode ? 'Updating...' : 'Saving...' : isEditMode ? 'Update Form' : 'Save Form'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 11\n        }, this), onCancel && /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: onCancel,\n          className: \"btn btn-outline\",\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 278,\n      columnNumber: 7\n    }, this), errors.general && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"alert alert-error\",\n      children: errors.general\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 310,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-config-row\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-field-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Form Name *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: formName,\n          onChange: e => setFormName(e.target.value),\n          placeholder: \"Enter form name\",\n          className: errors.formName ? 'error' : ''\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 11\n        }, this), errors.formName && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"error-text\",\n          children: errors.formName\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 31\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 315,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-field-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Division *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: selectedHierarchy.divisionId || '',\n          onChange: handleDivisionChange,\n          className: errors.hierarchy ? 'error' : '',\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"\",\n            children: \"Select Division\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 13\n          }, this), divisions.map(division => /*#__PURE__*/_jsxDEV(\"option\", {\n            value: division.id,\n            children: division.name\n          }, division.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 327,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-field-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Category *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: selectedHierarchy.categoryId || '',\n          onChange: handleCategoryChange,\n          disabled: !selectedHierarchy.divisionId,\n          className: errors.hierarchy ? 'error' : '',\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"\",\n            children: \"Select Category\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 13\n          }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n            value: category.id,\n            children: category.name\n          }, category.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 343,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-field-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"SubCategory\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: selectedHierarchy.subCategoryId || '',\n          onChange: handleSubCategoryChange,\n          disabled: !selectedHierarchy.categoryId,\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"\",\n            children: \"Select SubCategory\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 13\n          }, this), subCategories.map(subCategory => /*#__PURE__*/_jsxDEV(\"option\", {\n            value: subCategory.id,\n            children: subCategory.name\n          }, subCategory.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 369,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 360,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 314,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-description-row\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-field-group full-width\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Description\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: formDescription,\n          onChange: e => setFormDescription(e.target.value),\n          placeholder: \"Enter form description\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 381,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 379,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 378,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-builder-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"search-section\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-field\",\n          children: /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Search fields...\",\n            value: searchTerm,\n            onChange: e => setSearchTerm(e.target.value),\n            className: \"search-input\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 393,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 392,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fields-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"section-tabs\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: `section-tab ${currentSection === 'all' ? 'active' : ''}`,\n            onClick: () => setCurrentSection('all'),\n            children: [\"All Fields (\", sectionCounts.all, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 407,\n            columnNumber: 13\n          }, this), Object.entries(PersonFieldDefinitions).map(([sectionKey, sectionDef]) => /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `section-tab ${currentSection === sectionKey ? 'active' : ''}`,\n            onClick: () => setCurrentSection(sectionKey),\n            children: [sectionDef.label, \" (\", sectionCounts[sectionKey] || 0, \")\"]\n          }, sectionKey, true, {\n            fileName: _jsxFileName,\n            lineNumber: 414,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 406,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"available-fields\",\n          children: filteredFields.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"empty-state\",\n            children: \"No fields found matching your search.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 427,\n            columnNumber: 15\n          }, this) : filteredFields.map(field => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `field-item ${selectedFields.some(f => f.key === field.key) ? 'selected' : ''}`,\n            onClick: () => handleFieldToggle(field),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"field-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"field-label\",\n                children: field.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 438,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"field-type\",\n                children: field.type\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 439,\n                columnNumber: 21\n              }, this), field.required && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"required-badge\",\n                children: \"Required\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 440,\n                columnNumber: 40\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 437,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"field-toggle\",\n              children: selectedFields.some(f => f.key === field.key) ? '✓' : '+'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 442,\n              columnNumber: 19\n            }, this)]\n          }, field.key, true, {\n            fileName: _jsxFileName,\n            lineNumber: 432,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 425,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 405,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"selected-fields-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: [\"Selected Fields (\", selectedFields.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 453,\n          columnNumber: 11\n        }, this), errors.fields && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-message\",\n          children: errors.fields\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 454,\n          columnNumber: 29\n        }, this), errors.requiredFields && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-message\",\n          children: errors.requiredFields\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 455,\n          columnNumber: 37\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"selected-fields\",\n          children: selectedFields.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"empty-state\",\n            children: \"No fields selected. Choose fields from the available fields panel.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 459,\n            columnNumber: 15\n          }, this) : selectedFields.map((field, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"selected-field\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"field-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"field-label\",\n                children: field.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 466,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"field-type\",\n                children: field.type\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 467,\n                columnNumber: 21\n              }, this), field.required && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"required-badge\",\n                children: \"Required\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 468,\n                columnNumber: 40\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 465,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"field-actions\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => handleFieldConfig(field),\n                className: \"btn-icon\",\n                title: \"Configure field\",\n                children: \"\\u2699\\uFE0F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 471,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => handleFieldMove(index, Math.max(0, index - 1)),\n                className: \"btn-icon\",\n                disabled: index === 0,\n                title: \"Move up\",\n                children: \"\\u2191\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 479,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => handleFieldMove(index, Math.min(selectedFields.length - 1, index + 1)),\n                className: \"btn-icon\",\n                disabled: index === selectedFields.length - 1,\n                title: \"Move down\",\n                children: \"\\u2193\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 488,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => handleFieldToggle(field),\n                className: \"btn-icon btn-danger\",\n                title: \"Remove field\",\n                children: \"\\u2715\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 497,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 19\n            }, this)]\n          }, field.key, true, {\n            fileName: _jsxFileName,\n            lineNumber: 464,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 457,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 452,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 390,\n      columnNumber: 7\n    }, this), showFieldConfig && configField && /*#__PURE__*/_jsxDEV(FieldConfigModal, {\n      field: configField,\n      onSave: handleFieldConfigSave,\n      onCancel: () => {\n        setShowFieldConfig(false);\n        setConfigField(null);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 515,\n      columnNumber: 9\n    }, this), showPreview && /*#__PURE__*/_jsxDEV(FormPreview, {\n      fields: selectedFields,\n      formName: formName,\n      onClose: () => setShowPreview(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 526,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 277,\n    columnNumber: 5\n  }, this);\n};\n_s(FormBuilder, \"u8BydGnAJT8RZAISeHN8visNBcQ=\");\n_c = FormBuilder;\nexport default FormBuilder;\nvar _c;\n$RefreshReg$(_c, \"FormBuilder\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "PersonFieldDefinitions", "apiService", "formConfigService", "FieldConfigModal", "FormPreview", "jsxDEV", "_jsxDEV", "FormBuilder", "onSave", "onCancel", "initialConfig", "_s", "formName", "setFormName", "formDescription", "setFormDescription", "<PERSON><PERSON><PERSON>s", "setSelectedFields", "currentSection", "setCurrentSection", "searchTerm", "setSearchTerm", "showPreview", "setShowPreview", "showFieldConfig", "setShowFieldConfig", "config<PERSON><PERSON>", "setConfigField", "errors", "setErrors", "saving", "setSaving", "isEditMode", "setIsEditMode", "editingFormId", "setEditingFormId", "selectedHierarchy", "setSelectedHierarchy", "divisionId", "categoryId", "subCategoryId", "divisions", "setDivisions", "categories", "setCategories", "subCategories", "setSubCategories", "loading", "setLoading", "loadDivisions", "loadInitialConfig", "config", "name", "description", "fields", "id", "key", "prev", "response", "getDivisions", "data", "error", "console", "loadCategories", "getCategories", "loadSubCategories", "getSubCategories", "handleDivisionChange", "e", "target", "value", "handleCategoryChange", "handleSubCategoryChange", "handleFieldToggle", "field", "isSelected", "some", "f", "filter", "handleFieldConfig", "handleFieldConfigSave", "updatedField", "map", "handleFieldMove", "fromIndex", "toIndex", "new<PERSON>ields", "movedField", "splice", "handleSave", "validateForm", "formConfig", "type", "associatedId", "updateFormConfig", "saveFormConfig", "general", "newErrors", "trim", "hierarchy", "length", "formValidation", "validateFormCreation", "<PERSON><PERSON><PERSON><PERSON>", "formCreation", "join", "Object", "keys", "getAvailableFields", "allFields", "entries", "for<PERSON>ach", "sectionKey", "sectionDef", "push", "section", "getFilteredFields", "availableFields", "filtered", "label", "toLowerCase", "includes", "getSectionCounts", "counts", "all", "sectionCounts", "filteredFields", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "onChange", "placeholder", "division", "category", "subCategory", "required", "requiredFields", "index", "title", "Math", "max", "min", "onClose", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/components/forms/FormBuilder.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport './FormBuilder.css';\nimport { PersonFieldDefinitions } from '../../data/PersonFieldDefinitions';\nimport apiService from '../../services/apiService';\nimport formConfigService from '../../services/formConfigService';\nimport FieldConfigModal from './FieldConfigModal';\nimport FormPreview from './FormPreview';\n\nconst FormBuilder = ({ onSave, onCancel, initialConfig = null }) => {\n  // State management\n  const [formName, setFormName] = useState('');\n  const [formDescription, setFormDescription] = useState('');\n  const [selectedFields, setSelectedFields] = useState([]);\n  const [currentSection, setCurrentSection] = useState('all');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showPreview, setShowPreview] = useState(false);\n  const [showFieldConfig, setShowFieldConfig] = useState(false);\n  const [configField, setConfigField] = useState(null);\n  const [errors, setErrors] = useState({});\n  const [saving, setSaving] = useState(false);\n  const [isEditMode, setIsEditMode] = useState(false);\n  const [editingFormId, setEditingFormId] = useState(null);\n\n  // Hierarchy state\n  const [selectedHierarchy, setSelectedHierarchy] = useState({\n    divisionId: '',\n    categoryId: '',\n    subCategoryId: ''\n  });\n\n  // Data state\n  const [divisions, setDivisions] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [subCategories, setSubCategories] = useState([]);\n  const [loading, setLoading] = useState({\n    divisions: false,\n    categories: false,\n    subCategories: false\n  });\n\n  // Load initial data\n  useEffect(() => {\n    loadDivisions();\n    if (initialConfig) {\n      loadInitialConfig(initialConfig);\n    }\n  }, [initialConfig]);\n\n  const loadInitialConfig = (config) => {\n    setFormName(config.name || '');\n    setFormDescription(config.description || '');\n    setSelectedFields(config.fields || []);\n    setSelectedHierarchy({\n      divisionId: config.divisionId || '',\n      categoryId: config.categoryId || '',\n      subCategoryId: config.subCategoryId || ''\n    });\n    setIsEditMode(true);\n    setEditingFormId(config.id || config.key);\n  };\n\n  const loadDivisions = async () => {\n    try {\n      setLoading(prev => ({ ...prev, divisions: true }));\n      const response = await apiService.getDivisions();\n      setDivisions(response.data || []);\n    } catch (error) {\n      console.error('Error loading divisions:', error);\n    } finally {\n      setLoading(prev => ({ ...prev, divisions: false }));\n    }\n  };\n\n  const loadCategories = async (divisionId) => {\n    try {\n      setLoading(prev => ({ ...prev, categories: true }));\n      const response = await apiService.getCategories(divisionId);\n      setCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading categories:', error);\n    } finally {\n      setLoading(prev => ({ ...prev, categories: false }));\n    }\n  };\n\n  const loadSubCategories = async (categoryId) => {\n    try {\n      setLoading(prev => ({ ...prev, subCategories: true }));\n      const response = await apiService.getSubCategories(categoryId);\n      setSubCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading subcategories:', error);\n    } finally {\n      setLoading(prev => ({ ...prev, subCategories: false }));\n    }\n  };\n\n  // Event handlers\n  const handleDivisionChange = (e) => {\n    const divisionId = e.target.value;\n    setSelectedHierarchy({\n      divisionId,\n      categoryId: '',\n      subCategoryId: ''\n    });\n    setCategories([]);\n    setSubCategories([]);\n    if (divisionId) {\n      loadCategories(divisionId);\n    }\n  };\n\n  const handleCategoryChange = (e) => {\n    const categoryId = e.target.value;\n    setSelectedHierarchy(prev => ({\n      ...prev,\n      categoryId,\n      subCategoryId: ''\n    }));\n    setSubCategories([]);\n    if (categoryId) {\n      loadSubCategories(categoryId);\n    }\n  };\n\n  const handleSubCategoryChange = (e) => {\n    const subCategoryId = e.target.value;\n    setSelectedHierarchy(prev => ({\n      ...prev,\n      subCategoryId\n    }));\n  };\n\n  const handleFieldToggle = (field) => {\n    const isSelected = selectedFields.some(f => f.key === field.key);\n    if (isSelected) {\n      setSelectedFields(selectedFields.filter(f => f.key !== field.key));\n    } else {\n      setSelectedFields([...selectedFields, { ...field }]);\n    }\n  };\n\n  const handleFieldConfig = (field) => {\n    setConfigField(field);\n    setShowFieldConfig(true);\n  };\n\n  const handleFieldConfigSave = (updatedField) => {\n    setSelectedFields(selectedFields.map(f => \n      f.key === updatedField.key ? updatedField : f\n    ));\n    setShowFieldConfig(false);\n    setConfigField(null);\n  };\n\n  const handleFieldMove = (fromIndex, toIndex) => {\n    const newFields = [...selectedFields];\n    const [movedField] = newFields.splice(fromIndex, 1);\n    newFields.splice(toIndex, 0, movedField);\n    setSelectedFields(newFields);\n  };\n\n  const handleSave = async () => {\n    if (!validateForm()) return;\n\n    setSaving(true);\n    try {\n      const formConfig = {\n        name: formName,\n        description: formDescription,\n        fields: selectedFields,\n        divisionId: selectedHierarchy.divisionId,\n        categoryId: selectedHierarchy.categoryId,\n        subCategoryId: selectedHierarchy.subCategoryId,\n        type: selectedHierarchy.subCategoryId ? 'subcategory' : 'category',\n        associatedId: selectedHierarchy.subCategoryId || selectedHierarchy.categoryId\n      };\n\n      if (isEditMode) {\n        formConfig.id = editingFormId;\n        await formConfigService.updateFormConfig(formConfig);\n      } else {\n        await formConfigService.saveFormConfig(formConfig);\n      }\n\n      if (onSave) {\n        onSave(formConfig, isEditMode);\n      }\n    } catch (error) {\n      console.error('Error saving form:', error);\n      setErrors({ general: 'Failed to save form. Please try again.' });\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    if (!formName.trim()) {\n      newErrors.formName = 'Form name is required';\n    }\n\n    if (!selectedHierarchy.divisionId) {\n      newErrors.hierarchy = 'Division is required';\n    }\n\n    if (!selectedHierarchy.categoryId) {\n      newErrors.hierarchy = 'Category is required';\n    }\n\n    if (selectedFields.length === 0) {\n      newErrors.fields = 'At least one field must be selected';\n    }\n\n    // Skip form creation validation in edit mode\n    if (selectedHierarchy.categoryId && !isEditMode) {\n      const formValidation = formConfigService.validateFormCreation(\n        selectedHierarchy.categoryId,\n        selectedHierarchy.subCategoryId\n      );\n\n      if (!formValidation.isValid) {\n        newErrors.formCreation = formValidation.errors.join('. ');\n      }\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  // Get available fields\n  const getAvailableFields = () => {\n    const allFields = [];\n    Object.entries(PersonFieldDefinitions).forEach(([sectionKey, sectionDef]) => {\n      sectionDef.fields.forEach(field => {\n        allFields.push({\n          ...field,\n          section: sectionKey\n        });\n      });\n    });\n    return allFields;\n  };\n\n  const getFilteredFields = () => {\n    const availableFields = getAvailableFields();\n    let filtered = availableFields;\n\n    if (currentSection !== 'all') {\n      filtered = filtered.filter(field => field.section === currentSection);\n    }\n\n    if (searchTerm) {\n      filtered = filtered.filter(field =>\n        field.label.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        field.type.toLowerCase().includes(searchTerm.toLowerCase())\n      );\n    }\n\n    return filtered;\n  };\n\n  const getSectionCounts = () => {\n    const availableFields = getAvailableFields();\n    const counts = { all: availableFields.length };\n    Object.keys(PersonFieldDefinitions).forEach(sectionKey => {\n      counts[sectionKey] = availableFields.filter(f => f.section === sectionKey).length;\n    });\n    return counts;\n  };\n\n  const sectionCounts = getSectionCounts();\n  const filteredFields = getFilteredFields();\n\n  return (\n    <div className=\"form-builder\">\n      <div className=\"form-builder-header\">\n        <h2>Form Builder</h2>\n        <div className=\"header-actions\">\n          <button\n            type=\"button\"\n            onClick={() => setShowPreview(true)}\n            className=\"btn btn-secondary\"\n            disabled={selectedFields.length === 0}\n          >\n            Preview Form\n          </button>\n          <button\n            type=\"button\"\n            onClick={handleSave}\n            className=\"btn btn-primary\"\n            disabled={saving || errors.formCreation}\n          >\n            {saving ? (isEditMode ? 'Updating...' : 'Saving...') : (isEditMode ? 'Update Form' : 'Save Form')}\n          </button>\n          {onCancel && (\n            <button\n              type=\"button\"\n              onClick={onCancel}\n              className=\"btn btn-outline\"\n            >\n              Cancel\n            </button>\n          )}\n        </div>\n      </div>\n\n      {errors.general && (\n        <div className=\"alert alert-error\">{errors.general}</div>\n      )}\n\n      {/* Compact Form Configuration Row */}\n      <div className=\"form-config-row\">\n        <div className=\"form-field-group\">\n          <label>Form Name *</label>\n          <input\n            type=\"text\"\n            value={formName}\n            onChange={(e) => setFormName(e.target.value)}\n            placeholder=\"Enter form name\"\n            className={errors.formName ? 'error' : ''}\n          />\n          {errors.formName && <span className=\"error-text\">{errors.formName}</span>}\n        </div>\n\n        <div className=\"form-field-group\">\n          <label>Division *</label>\n          <select\n            value={selectedHierarchy.divisionId || ''}\n            onChange={handleDivisionChange}\n            className={errors.hierarchy ? 'error' : ''}\n          >\n            <option value=\"\">Select Division</option>\n            {divisions.map(division => (\n              <option key={division.id} value={division.id}>\n                {division.name}\n              </option>\n            ))}\n          </select>\n        </div>\n\n        <div className=\"form-field-group\">\n          <label>Category *</label>\n          <select\n            value={selectedHierarchy.categoryId || ''}\n            onChange={handleCategoryChange}\n            disabled={!selectedHierarchy.divisionId}\n            className={errors.hierarchy ? 'error' : ''}\n          >\n            <option value=\"\">Select Category</option>\n            {categories.map(category => (\n              <option key={category.id} value={category.id}>\n                {category.name}\n              </option>\n            ))}\n          </select>\n        </div>\n\n        <div className=\"form-field-group\">\n          <label>SubCategory</label>\n          <select\n            value={selectedHierarchy.subCategoryId || ''}\n            onChange={handleSubCategoryChange}\n            disabled={!selectedHierarchy.categoryId}\n          >\n            <option value=\"\">Select SubCategory</option>\n            {subCategories.map(subCategory => (\n              <option key={subCategory.id} value={subCategory.id}>\n                {subCategory.name}\n              </option>\n            ))}\n          </select>\n        </div>\n      </div>\n\n      {/* Description Field */}\n      <div className=\"form-description-row\">\n        <div className=\"form-field-group full-width\">\n          <label>Description</label>\n          <input\n            type=\"text\"\n            value={formDescription}\n            onChange={(e) => setFormDescription(e.target.value)}\n            placeholder=\"Enter form description\"\n          />\n        </div>\n      </div>\n\n      <div className=\"form-builder-content\">\n        {/* Search Field */}\n        <div className=\"search-section\">\n          <div className=\"search-field\">\n            <input\n              type=\"text\"\n              placeholder=\"Search fields...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"search-input\"\n            />\n          </div>\n        </div>\n\n        {/* Fields Section Tabs */}\n        <div className=\"fields-section\">\n          <div className=\"section-tabs\">\n            <button\n              className={`section-tab ${currentSection === 'all' ? 'active' : ''}`}\n              onClick={() => setCurrentSection('all')}\n            >\n              All Fields ({sectionCounts.all})\n            </button>\n            {Object.entries(PersonFieldDefinitions).map(([sectionKey, sectionDef]) => (\n              <button\n                key={sectionKey}\n                className={`section-tab ${currentSection === sectionKey ? 'active' : ''}`}\n                onClick={() => setCurrentSection(sectionKey)}\n              >\n                {sectionDef.label} ({sectionCounts[sectionKey] || 0})\n              </button>\n            ))}\n          </div>\n\n          {/* Available Fields */}\n          <div className=\"available-fields\">\n            {filteredFields.length === 0 ? (\n              <div className=\"empty-state\">\n                No fields found matching your search.\n              </div>\n            ) : (\n              filteredFields.map((field) => (\n                <div\n                  key={field.key}\n                  className={`field-item ${selectedFields.some(f => f.key === field.key) ? 'selected' : ''}`}\n                  onClick={() => handleFieldToggle(field)}\n                >\n                  <div className=\"field-info\">\n                    <span className=\"field-label\">{field.label}</span>\n                    <span className=\"field-type\">{field.type}</span>\n                    {field.required && <span className=\"required-badge\">Required</span>}\n                  </div>\n                  <div className=\"field-toggle\">\n                    {selectedFields.some(f => f.key === field.key) ? '✓' : '+'}\n                  </div>\n                </div>\n              ))\n            )}\n          </div>\n        </div>\n\n        {/* Selected Fields Panel */}\n        <div className=\"selected-fields-section\">\n          <h3>Selected Fields ({selectedFields.length})</h3>\n          {errors.fields && <div className=\"error-message\">{errors.fields}</div>}\n          {errors.requiredFields && <div className=\"error-message\">{errors.requiredFields}</div>}\n\n          <div className=\"selected-fields\">\n            {selectedFields.length === 0 ? (\n              <div className=\"empty-state\">\n                No fields selected. Choose fields from the available fields panel.\n              </div>\n            ) : (\n              selectedFields.map((field, index) => (\n                <div key={field.key} className=\"selected-field\">\n                  <div className=\"field-info\">\n                    <span className=\"field-label\">{field.label}</span>\n                    <span className=\"field-type\">{field.type}</span>\n                    {field.required && <span className=\"required-badge\">Required</span>}\n                  </div>\n                  <div className=\"field-actions\">\n                    <button\n                      type=\"button\"\n                      onClick={() => handleFieldConfig(field)}\n                      className=\"btn-icon\"\n                      title=\"Configure field\"\n                    >\n                      ⚙️\n                    </button>\n                    <button\n                      type=\"button\"\n                      onClick={() => handleFieldMove(index, Math.max(0, index - 1))}\n                      className=\"btn-icon\"\n                      disabled={index === 0}\n                      title=\"Move up\"\n                    >\n                      ↑\n                    </button>\n                    <button\n                      type=\"button\"\n                      onClick={() => handleFieldMove(index, Math.min(selectedFields.length - 1, index + 1))}\n                      className=\"btn-icon\"\n                      disabled={index === selectedFields.length - 1}\n                      title=\"Move down\"\n                    >\n                      ↓\n                    </button>\n                    <button\n                      type=\"button\"\n                      onClick={() => handleFieldToggle(field)}\n                      className=\"btn-icon btn-danger\"\n                      title=\"Remove field\"\n                    >\n                      ✕\n                    </button>\n                  </div>\n                </div>\n              ))\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Modals */}\n      {showFieldConfig && configField && (\n        <FieldConfigModal\n          field={configField}\n          onSave={handleFieldConfigSave}\n          onCancel={() => {\n            setShowFieldConfig(false);\n            setConfigField(null);\n          }}\n        />\n      )}\n\n      {showPreview && (\n        <FormPreview\n          fields={selectedFields}\n          formName={formName}\n          onClose={() => setShowPreview(false)}\n        />\n      )}\n    </div>\n  );\n};\n\nexport default FormBuilder;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,mBAAmB;AAC1B,SAASC,sBAAsB,QAAQ,mCAAmC;AAC1E,OAAOC,UAAU,MAAM,2BAA2B;AAClD,OAAOC,iBAAiB,MAAM,kCAAkC;AAChE,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,WAAW,MAAM,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,WAAW,GAAGA,CAAC;EAAEC,MAAM;EAAEC,QAAQ;EAAEC,aAAa,GAAG;AAAK,CAAC,KAAK;EAAAC,EAAA;EAClE;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACgB,eAAe,EAAEC,kBAAkB,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACkB,cAAc,EAAEC,iBAAiB,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACoB,cAAc,EAAEC,iBAAiB,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACsB,UAAU,EAAEC,aAAa,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACwB,WAAW,EAAEC,cAAc,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC0B,eAAe,EAAEC,kBAAkB,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC4B,WAAW,EAAEC,cAAc,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC8B,MAAM,EAAEC,SAAS,CAAC,GAAG/B,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAACgC,MAAM,EAAEC,SAAS,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACkC,UAAU,EAAEC,aAAa,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACoC,aAAa,EAAEC,gBAAgB,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;;EAExD;EACA,MAAM,CAACsC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGvC,QAAQ,CAAC;IACzDwC,UAAU,EAAE,EAAE;IACdC,UAAU,EAAE,EAAE;IACdC,aAAa,EAAE;EACjB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC6C,UAAU,EAAEC,aAAa,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC+C,aAAa,EAAEC,gBAAgB,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACiD,OAAO,EAAEC,UAAU,CAAC,GAAGlD,QAAQ,CAAC;IACrC2C,SAAS,EAAE,KAAK;IAChBE,UAAU,EAAE,KAAK;IACjBE,aAAa,EAAE;EACjB,CAAC,CAAC;;EAEF;EACA9C,SAAS,CAAC,MAAM;IACdkD,aAAa,CAAC,CAAC;IACf,IAAIvC,aAAa,EAAE;MACjBwC,iBAAiB,CAACxC,aAAa,CAAC;IAClC;EACF,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;EAEnB,MAAMwC,iBAAiB,GAAIC,MAAM,IAAK;IACpCtC,WAAW,CAACsC,MAAM,CAACC,IAAI,IAAI,EAAE,CAAC;IAC9BrC,kBAAkB,CAACoC,MAAM,CAACE,WAAW,IAAI,EAAE,CAAC;IAC5CpC,iBAAiB,CAACkC,MAAM,CAACG,MAAM,IAAI,EAAE,CAAC;IACtCjB,oBAAoB,CAAC;MACnBC,UAAU,EAAEa,MAAM,CAACb,UAAU,IAAI,EAAE;MACnCC,UAAU,EAAEY,MAAM,CAACZ,UAAU,IAAI,EAAE;MACnCC,aAAa,EAAEW,MAAM,CAACX,aAAa,IAAI;IACzC,CAAC,CAAC;IACFP,aAAa,CAAC,IAAI,CAAC;IACnBE,gBAAgB,CAACgB,MAAM,CAACI,EAAE,IAAIJ,MAAM,CAACK,GAAG,CAAC;EAC3C,CAAC;EAED,MAAMP,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACFD,UAAU,CAACS,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEhB,SAAS,EAAE;MAAK,CAAC,CAAC,CAAC;MAClD,MAAMiB,QAAQ,GAAG,MAAMzD,UAAU,CAAC0D,YAAY,CAAC,CAAC;MAChDjB,YAAY,CAACgB,QAAQ,CAACE,IAAI,IAAI,EAAE,CAAC;IACnC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAClD,CAAC,SAAS;MACRb,UAAU,CAACS,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEhB,SAAS,EAAE;MAAM,CAAC,CAAC,CAAC;IACrD;EACF,CAAC;EAED,MAAMsB,cAAc,GAAG,MAAOzB,UAAU,IAAK;IAC3C,IAAI;MACFU,UAAU,CAACS,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEd,UAAU,EAAE;MAAK,CAAC,CAAC,CAAC;MACnD,MAAMe,QAAQ,GAAG,MAAMzD,UAAU,CAAC+D,aAAa,CAAC1B,UAAU,CAAC;MAC3DM,aAAa,CAACc,QAAQ,CAACE,IAAI,IAAI,EAAE,CAAC;IACpC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD,CAAC,SAAS;MACRb,UAAU,CAACS,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEd,UAAU,EAAE;MAAM,CAAC,CAAC,CAAC;IACtD;EACF,CAAC;EAED,MAAMsB,iBAAiB,GAAG,MAAO1B,UAAU,IAAK;IAC9C,IAAI;MACFS,UAAU,CAACS,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEZ,aAAa,EAAE;MAAK,CAAC,CAAC,CAAC;MACtD,MAAMa,QAAQ,GAAG,MAAMzD,UAAU,CAACiE,gBAAgB,CAAC3B,UAAU,CAAC;MAC9DO,gBAAgB,CAACY,QAAQ,CAACE,IAAI,IAAI,EAAE,CAAC;IACvC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD,CAAC,SAAS;MACRb,UAAU,CAACS,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEZ,aAAa,EAAE;MAAM,CAAC,CAAC,CAAC;IACzD;EACF,CAAC;;EAED;EACA,MAAMsB,oBAAoB,GAAIC,CAAC,IAAK;IAClC,MAAM9B,UAAU,GAAG8B,CAAC,CAACC,MAAM,CAACC,KAAK;IACjCjC,oBAAoB,CAAC;MACnBC,UAAU;MACVC,UAAU,EAAE,EAAE;MACdC,aAAa,EAAE;IACjB,CAAC,CAAC;IACFI,aAAa,CAAC,EAAE,CAAC;IACjBE,gBAAgB,CAAC,EAAE,CAAC;IACpB,IAAIR,UAAU,EAAE;MACdyB,cAAc,CAACzB,UAAU,CAAC;IAC5B;EACF,CAAC;EAED,MAAMiC,oBAAoB,GAAIH,CAAC,IAAK;IAClC,MAAM7B,UAAU,GAAG6B,CAAC,CAACC,MAAM,CAACC,KAAK;IACjCjC,oBAAoB,CAACoB,IAAI,KAAK;MAC5B,GAAGA,IAAI;MACPlB,UAAU;MACVC,aAAa,EAAE;IACjB,CAAC,CAAC,CAAC;IACHM,gBAAgB,CAAC,EAAE,CAAC;IACpB,IAAIP,UAAU,EAAE;MACd0B,iBAAiB,CAAC1B,UAAU,CAAC;IAC/B;EACF,CAAC;EAED,MAAMiC,uBAAuB,GAAIJ,CAAC,IAAK;IACrC,MAAM5B,aAAa,GAAG4B,CAAC,CAACC,MAAM,CAACC,KAAK;IACpCjC,oBAAoB,CAACoB,IAAI,KAAK;MAC5B,GAAGA,IAAI;MACPjB;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMiC,iBAAiB,GAAIC,KAAK,IAAK;IACnC,MAAMC,UAAU,GAAG3D,cAAc,CAAC4D,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACrB,GAAG,KAAKkB,KAAK,CAAClB,GAAG,CAAC;IAChE,IAAImB,UAAU,EAAE;MACd1D,iBAAiB,CAACD,cAAc,CAAC8D,MAAM,CAACD,CAAC,IAAIA,CAAC,CAACrB,GAAG,KAAKkB,KAAK,CAAClB,GAAG,CAAC,CAAC;IACpE,CAAC,MAAM;MACLvC,iBAAiB,CAAC,CAAC,GAAGD,cAAc,EAAE;QAAE,GAAG0D;MAAM,CAAC,CAAC,CAAC;IACtD;EACF,CAAC;EAED,MAAMK,iBAAiB,GAAIL,KAAK,IAAK;IACnC/C,cAAc,CAAC+C,KAAK,CAAC;IACrBjD,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMuD,qBAAqB,GAAIC,YAAY,IAAK;IAC9ChE,iBAAiB,CAACD,cAAc,CAACkE,GAAG,CAACL,CAAC,IACpCA,CAAC,CAACrB,GAAG,KAAKyB,YAAY,CAACzB,GAAG,GAAGyB,YAAY,GAAGJ,CAC9C,CAAC,CAAC;IACFpD,kBAAkB,CAAC,KAAK,CAAC;IACzBE,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAMwD,eAAe,GAAGA,CAACC,SAAS,EAAEC,OAAO,KAAK;IAC9C,MAAMC,SAAS,GAAG,CAAC,GAAGtE,cAAc,CAAC;IACrC,MAAM,CAACuE,UAAU,CAAC,GAAGD,SAAS,CAACE,MAAM,CAACJ,SAAS,EAAE,CAAC,CAAC;IACnDE,SAAS,CAACE,MAAM,CAACH,OAAO,EAAE,CAAC,EAAEE,UAAU,CAAC;IACxCtE,iBAAiB,CAACqE,SAAS,CAAC;EAC9B,CAAC;EAED,MAAMG,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAACC,YAAY,CAAC,CAAC,EAAE;IAErB3D,SAAS,CAAC,IAAI,CAAC;IACf,IAAI;MACF,MAAM4D,UAAU,GAAG;QACjBvC,IAAI,EAAExC,QAAQ;QACdyC,WAAW,EAAEvC,eAAe;QAC5BwC,MAAM,EAAEtC,cAAc;QACtBsB,UAAU,EAAEF,iBAAiB,CAACE,UAAU;QACxCC,UAAU,EAAEH,iBAAiB,CAACG,UAAU;QACxCC,aAAa,EAAEJ,iBAAiB,CAACI,aAAa;QAC9CoD,IAAI,EAAExD,iBAAiB,CAACI,aAAa,GAAG,aAAa,GAAG,UAAU;QAClEqD,YAAY,EAAEzD,iBAAiB,CAACI,aAAa,IAAIJ,iBAAiB,CAACG;MACrE,CAAC;MAED,IAAIP,UAAU,EAAE;QACd2D,UAAU,CAACpC,EAAE,GAAGrB,aAAa;QAC7B,MAAMhC,iBAAiB,CAAC4F,gBAAgB,CAACH,UAAU,CAAC;MACtD,CAAC,MAAM;QACL,MAAMzF,iBAAiB,CAAC6F,cAAc,CAACJ,UAAU,CAAC;MACpD;MAEA,IAAInF,MAAM,EAAE;QACVA,MAAM,CAACmF,UAAU,EAAE3D,UAAU,CAAC;MAChC;IACF,CAAC,CAAC,OAAO6B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1ChC,SAAS,CAAC;QAAEmE,OAAO,EAAE;MAAyC,CAAC,CAAC;IAClE,CAAC,SAAS;MACRjE,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;EAED,MAAM2D,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMO,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAI,CAACrF,QAAQ,CAACsF,IAAI,CAAC,CAAC,EAAE;MACpBD,SAAS,CAACrF,QAAQ,GAAG,uBAAuB;IAC9C;IAEA,IAAI,CAACwB,iBAAiB,CAACE,UAAU,EAAE;MACjC2D,SAAS,CAACE,SAAS,GAAG,sBAAsB;IAC9C;IAEA,IAAI,CAAC/D,iBAAiB,CAACG,UAAU,EAAE;MACjC0D,SAAS,CAACE,SAAS,GAAG,sBAAsB;IAC9C;IAEA,IAAInF,cAAc,CAACoF,MAAM,KAAK,CAAC,EAAE;MAC/BH,SAAS,CAAC3C,MAAM,GAAG,qCAAqC;IAC1D;;IAEA;IACA,IAAIlB,iBAAiB,CAACG,UAAU,IAAI,CAACP,UAAU,EAAE;MAC/C,MAAMqE,cAAc,GAAGnG,iBAAiB,CAACoG,oBAAoB,CAC3DlE,iBAAiB,CAACG,UAAU,EAC5BH,iBAAiB,CAACI,aACpB,CAAC;MAED,IAAI,CAAC6D,cAAc,CAACE,OAAO,EAAE;QAC3BN,SAAS,CAACO,YAAY,GAAGH,cAAc,CAACzE,MAAM,CAAC6E,IAAI,CAAC,IAAI,CAAC;MAC3D;IACF;IAEA5E,SAAS,CAACoE,SAAS,CAAC;IACpB,OAAOS,MAAM,CAACC,IAAI,CAACV,SAAS,CAAC,CAACG,MAAM,KAAK,CAAC;EAC5C,CAAC;;EAED;EACA,MAAMQ,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMC,SAAS,GAAG,EAAE;IACpBH,MAAM,CAACI,OAAO,CAAC9G,sBAAsB,CAAC,CAAC+G,OAAO,CAAC,CAAC,CAACC,UAAU,EAAEC,UAAU,CAAC,KAAK;MAC3EA,UAAU,CAAC3D,MAAM,CAACyD,OAAO,CAACrC,KAAK,IAAI;QACjCmC,SAAS,CAACK,IAAI,CAAC;UACb,GAAGxC,KAAK;UACRyC,OAAO,EAAEH;QACX,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,OAAOH,SAAS;EAClB,CAAC;EAED,MAAMO,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,eAAe,GAAGT,kBAAkB,CAAC,CAAC;IAC5C,IAAIU,QAAQ,GAAGD,eAAe;IAE9B,IAAInG,cAAc,KAAK,KAAK,EAAE;MAC5BoG,QAAQ,GAAGA,QAAQ,CAACxC,MAAM,CAACJ,KAAK,IAAIA,KAAK,CAACyC,OAAO,KAAKjG,cAAc,CAAC;IACvE;IAEA,IAAIE,UAAU,EAAE;MACdkG,QAAQ,GAAGA,QAAQ,CAACxC,MAAM,CAACJ,KAAK,IAC9BA,KAAK,CAAC6C,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrG,UAAU,CAACoG,WAAW,CAAC,CAAC,CAAC,IAC5D9C,KAAK,CAACkB,IAAI,CAAC4B,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrG,UAAU,CAACoG,WAAW,CAAC,CAAC,CAC5D,CAAC;IACH;IAEA,OAAOF,QAAQ;EACjB,CAAC;EAED,MAAMI,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAML,eAAe,GAAGT,kBAAkB,CAAC,CAAC;IAC5C,MAAMe,MAAM,GAAG;MAAEC,GAAG,EAAEP,eAAe,CAACjB;IAAO,CAAC;IAC9CM,MAAM,CAACC,IAAI,CAAC3G,sBAAsB,CAAC,CAAC+G,OAAO,CAACC,UAAU,IAAI;MACxDW,MAAM,CAACX,UAAU,CAAC,GAAGK,eAAe,CAACvC,MAAM,CAACD,CAAC,IAAIA,CAAC,CAACsC,OAAO,KAAKH,UAAU,CAAC,CAACZ,MAAM;IACnF,CAAC,CAAC;IACF,OAAOuB,MAAM;EACf,CAAC;EAED,MAAME,aAAa,GAAGH,gBAAgB,CAAC,CAAC;EACxC,MAAMI,cAAc,GAAGV,iBAAiB,CAAC,CAAC;EAE1C,oBACE9G,OAAA;IAAKyH,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC3B1H,OAAA;MAAKyH,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClC1H,OAAA;QAAA0H,QAAA,EAAI;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrB9H,OAAA;QAAKyH,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B1H,OAAA;UACEsF,IAAI,EAAC,QAAQ;UACbyC,OAAO,EAAEA,CAAA,KAAM9G,cAAc,CAAC,IAAI,CAAE;UACpCwG,SAAS,EAAC,mBAAmB;UAC7BO,QAAQ,EAAEtH,cAAc,CAACoF,MAAM,KAAK,CAAE;UAAA4B,QAAA,EACvC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT9H,OAAA;UACEsF,IAAI,EAAC,QAAQ;UACbyC,OAAO,EAAE5C,UAAW;UACpBsC,SAAS,EAAC,iBAAiB;UAC3BO,QAAQ,EAAExG,MAAM,IAAIF,MAAM,CAAC4E,YAAa;UAAAwB,QAAA,EAEvClG,MAAM,GAAIE,UAAU,GAAG,aAAa,GAAG,WAAW,GAAKA,UAAU,GAAG,aAAa,GAAG;QAAY;UAAAiG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3F,CAAC,EACR3H,QAAQ,iBACPH,OAAA;UACEsF,IAAI,EAAC,QAAQ;UACbyC,OAAO,EAAE5H,QAAS;UAClBsH,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAC5B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELxG,MAAM,CAACoE,OAAO,iBACb1F,OAAA;MAAKyH,SAAS,EAAC,mBAAmB;MAAAC,QAAA,EAAEpG,MAAM,CAACoE;IAAO;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CACzD,eAGD9H,OAAA;MAAKyH,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9B1H,OAAA;QAAKyH,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B1H,OAAA;UAAA0H,QAAA,EAAO;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC1B9H,OAAA;UACEsF,IAAI,EAAC,MAAM;UACXtB,KAAK,EAAE1D,QAAS;UAChB2H,QAAQ,EAAGnE,CAAC,IAAKvD,WAAW,CAACuD,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;UAC7CkE,WAAW,EAAC,iBAAiB;UAC7BT,SAAS,EAAEnG,MAAM,CAAChB,QAAQ,GAAG,OAAO,GAAG;QAAG;UAAAqH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC,EACDxG,MAAM,CAAChB,QAAQ,iBAAIN,OAAA;UAAMyH,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAEpG,MAAM,CAAChB;QAAQ;UAAAqH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtE,CAAC,eAEN9H,OAAA;QAAKyH,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B1H,OAAA;UAAA0H,QAAA,EAAO;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACzB9H,OAAA;UACEgE,KAAK,EAAElC,iBAAiB,CAACE,UAAU,IAAI,EAAG;UAC1CiG,QAAQ,EAAEpE,oBAAqB;UAC/B4D,SAAS,EAAEnG,MAAM,CAACuE,SAAS,GAAG,OAAO,GAAG,EAAG;UAAA6B,QAAA,gBAE3C1H,OAAA;YAAQgE,KAAK,EAAC,EAAE;YAAA0D,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACxC3F,SAAS,CAACyC,GAAG,CAACuD,QAAQ,iBACrBnI,OAAA;YAA0BgE,KAAK,EAAEmE,QAAQ,CAAClF,EAAG;YAAAyE,QAAA,EAC1CS,QAAQ,CAACrF;UAAI,GADHqF,QAAQ,CAAClF,EAAE;YAAA0E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEhB,CACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEN9H,OAAA;QAAKyH,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B1H,OAAA;UAAA0H,QAAA,EAAO;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACzB9H,OAAA;UACEgE,KAAK,EAAElC,iBAAiB,CAACG,UAAU,IAAI,EAAG;UAC1CgG,QAAQ,EAAEhE,oBAAqB;UAC/B+D,QAAQ,EAAE,CAAClG,iBAAiB,CAACE,UAAW;UACxCyF,SAAS,EAAEnG,MAAM,CAACuE,SAAS,GAAG,OAAO,GAAG,EAAG;UAAA6B,QAAA,gBAE3C1H,OAAA;YAAQgE,KAAK,EAAC,EAAE;YAAA0D,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACxCzF,UAAU,CAACuC,GAAG,CAACwD,QAAQ,iBACtBpI,OAAA;YAA0BgE,KAAK,EAAEoE,QAAQ,CAACnF,EAAG;YAAAyE,QAAA,EAC1CU,QAAQ,CAACtF;UAAI,GADHsF,QAAQ,CAACnF,EAAE;YAAA0E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEhB,CACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEN9H,OAAA;QAAKyH,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B1H,OAAA;UAAA0H,QAAA,EAAO;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC1B9H,OAAA;UACEgE,KAAK,EAAElC,iBAAiB,CAACI,aAAa,IAAI,EAAG;UAC7C+F,QAAQ,EAAE/D,uBAAwB;UAClC8D,QAAQ,EAAE,CAAClG,iBAAiB,CAACG,UAAW;UAAAyF,QAAA,gBAExC1H,OAAA;YAAQgE,KAAK,EAAC,EAAE;YAAA0D,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EAC3CvF,aAAa,CAACqC,GAAG,CAACyD,WAAW,iBAC5BrI,OAAA;YAA6BgE,KAAK,EAAEqE,WAAW,CAACpF,EAAG;YAAAyE,QAAA,EAChDW,WAAW,CAACvF;UAAI,GADNuF,WAAW,CAACpF,EAAE;YAAA0E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEnB,CACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9H,OAAA;MAAKyH,SAAS,EAAC,sBAAsB;MAAAC,QAAA,eACnC1H,OAAA;QAAKyH,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1C1H,OAAA;UAAA0H,QAAA,EAAO;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC1B9H,OAAA;UACEsF,IAAI,EAAC,MAAM;UACXtB,KAAK,EAAExD,eAAgB;UACvByH,QAAQ,EAAGnE,CAAC,IAAKrD,kBAAkB,CAACqD,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;UACpDkE,WAAW,EAAC;QAAwB;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN9H,OAAA;MAAKyH,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBAEnC1H,OAAA;QAAKyH,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC7B1H,OAAA;UAAKyH,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3B1H,OAAA;YACEsF,IAAI,EAAC,MAAM;YACX4C,WAAW,EAAC,kBAAkB;YAC9BlE,KAAK,EAAElD,UAAW;YAClBmH,QAAQ,EAAGnE,CAAC,IAAK/C,aAAa,CAAC+C,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;YAC/CyD,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN9H,OAAA;QAAKyH,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B1H,OAAA;UAAKyH,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B1H,OAAA;YACEyH,SAAS,EAAE,eAAe7G,cAAc,KAAK,KAAK,GAAG,QAAQ,GAAG,EAAE,EAAG;YACrEmH,OAAO,EAAEA,CAAA,KAAMlH,iBAAiB,CAAC,KAAK,CAAE;YAAA6G,QAAA,GACzC,cACa,EAACH,aAAa,CAACD,GAAG,EAAC,GACjC;UAAA;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACR1B,MAAM,CAACI,OAAO,CAAC9G,sBAAsB,CAAC,CAACkF,GAAG,CAAC,CAAC,CAAC8B,UAAU,EAAEC,UAAU,CAAC,kBACnE3G,OAAA;YAEEyH,SAAS,EAAE,eAAe7G,cAAc,KAAK8F,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;YAC1EqB,OAAO,EAAEA,CAAA,KAAMlH,iBAAiB,CAAC6F,UAAU,CAAE;YAAAgB,QAAA,GAE5Cf,UAAU,CAACM,KAAK,EAAC,IAAE,EAACM,aAAa,CAACb,UAAU,CAAC,IAAI,CAAC,EAAC,GACtD;UAAA,GALOA,UAAU;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKT,CACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGN9H,OAAA;UAAKyH,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAC9BF,cAAc,CAAC1B,MAAM,KAAK,CAAC,gBAC1B9F,OAAA;YAAKyH,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAE7B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,GAENN,cAAc,CAAC5C,GAAG,CAAER,KAAK,iBACvBpE,OAAA;YAEEyH,SAAS,EAAE,cAAc/G,cAAc,CAAC4D,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACrB,GAAG,KAAKkB,KAAK,CAAClB,GAAG,CAAC,GAAG,UAAU,GAAG,EAAE,EAAG;YAC3F6E,OAAO,EAAEA,CAAA,KAAM5D,iBAAiB,CAACC,KAAK,CAAE;YAAAsD,QAAA,gBAExC1H,OAAA;cAAKyH,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB1H,OAAA;gBAAMyH,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAEtD,KAAK,CAAC6C;cAAK;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClD9H,OAAA;gBAAMyH,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAEtD,KAAK,CAACkB;cAAI;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EAC/C1D,KAAK,CAACkE,QAAQ,iBAAItI,OAAA;gBAAMyH,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC,eACN9H,OAAA;cAAKyH,SAAS,EAAC,cAAc;cAAAC,QAAA,EAC1BhH,cAAc,CAAC4D,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACrB,GAAG,KAAKkB,KAAK,CAAClB,GAAG,CAAC,GAAG,GAAG,GAAG;YAAG;cAAAyE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC;UAAA,GAXD1D,KAAK,CAAClB,GAAG;YAAAyE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAYX,CACN;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN9H,OAAA;QAAKyH,SAAS,EAAC,yBAAyB;QAAAC,QAAA,gBACtC1H,OAAA;UAAA0H,QAAA,GAAI,mBAAiB,EAAChH,cAAc,CAACoF,MAAM,EAAC,GAAC;QAAA;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EACjDxG,MAAM,CAAC0B,MAAM,iBAAIhD,OAAA;UAAKyH,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAEpG,MAAM,CAAC0B;QAAM;UAAA2E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EACrExG,MAAM,CAACiH,cAAc,iBAAIvI,OAAA;UAAKyH,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAEpG,MAAM,CAACiH;QAAc;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAEtF9H,OAAA;UAAKyH,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAC7BhH,cAAc,CAACoF,MAAM,KAAK,CAAC,gBAC1B9F,OAAA;YAAKyH,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAE7B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,GAENpH,cAAc,CAACkE,GAAG,CAAC,CAACR,KAAK,EAAEoE,KAAK,kBAC9BxI,OAAA;YAAqByH,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7C1H,OAAA;cAAKyH,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB1H,OAAA;gBAAMyH,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAEtD,KAAK,CAAC6C;cAAK;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClD9H,OAAA;gBAAMyH,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAEtD,KAAK,CAACkB;cAAI;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EAC/C1D,KAAK,CAACkE,QAAQ,iBAAItI,OAAA;gBAAMyH,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC,eACN9H,OAAA;cAAKyH,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5B1H,OAAA;gBACEsF,IAAI,EAAC,QAAQ;gBACbyC,OAAO,EAAEA,CAAA,KAAMtD,iBAAiB,CAACL,KAAK,CAAE;gBACxCqD,SAAS,EAAC,UAAU;gBACpBgB,KAAK,EAAC,iBAAiB;gBAAAf,QAAA,EACxB;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT9H,OAAA;gBACEsF,IAAI,EAAC,QAAQ;gBACbyC,OAAO,EAAEA,CAAA,KAAMlD,eAAe,CAAC2D,KAAK,EAAEE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEH,KAAK,GAAG,CAAC,CAAC,CAAE;gBAC9Df,SAAS,EAAC,UAAU;gBACpBO,QAAQ,EAAEQ,KAAK,KAAK,CAAE;gBACtBC,KAAK,EAAC,SAAS;gBAAAf,QAAA,EAChB;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT9H,OAAA;gBACEsF,IAAI,EAAC,QAAQ;gBACbyC,OAAO,EAAEA,CAAA,KAAMlD,eAAe,CAAC2D,KAAK,EAAEE,IAAI,CAACE,GAAG,CAAClI,cAAc,CAACoF,MAAM,GAAG,CAAC,EAAE0C,KAAK,GAAG,CAAC,CAAC,CAAE;gBACtFf,SAAS,EAAC,UAAU;gBACpBO,QAAQ,EAAEQ,KAAK,KAAK9H,cAAc,CAACoF,MAAM,GAAG,CAAE;gBAC9C2C,KAAK,EAAC,WAAW;gBAAAf,QAAA,EAClB;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT9H,OAAA;gBACEsF,IAAI,EAAC,QAAQ;gBACbyC,OAAO,EAAEA,CAAA,KAAM5D,iBAAiB,CAACC,KAAK,CAAE;gBACxCqD,SAAS,EAAC,qBAAqB;gBAC/BgB,KAAK,EAAC,cAAc;gBAAAf,QAAA,EACrB;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA,GAzCE1D,KAAK,CAAClB,GAAG;YAAAyE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA0Cd,CACN;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL5G,eAAe,IAAIE,WAAW,iBAC7BpB,OAAA,CAACH,gBAAgB;MACfuE,KAAK,EAAEhD,WAAY;MACnBlB,MAAM,EAAEwE,qBAAsB;MAC9BvE,QAAQ,EAAEA,CAAA,KAAM;QACdgB,kBAAkB,CAAC,KAAK,CAAC;QACzBE,cAAc,CAAC,IAAI,CAAC;MACtB;IAAE;MAAAsG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACF,EAEA9G,WAAW,iBACVhB,OAAA,CAACF,WAAW;MACVkD,MAAM,EAAEtC,cAAe;MACvBJ,QAAQ,EAAEA,QAAS;MACnBuI,OAAO,EAAEA,CAAA,KAAM5H,cAAc,CAAC,KAAK;IAAE;MAAA0G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACzH,EAAA,CA7gBIJ,WAAW;AAAA6I,EAAA,GAAX7I,WAAW;AA+gBjB,eAAeA,WAAW;AAAC,IAAA6I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}