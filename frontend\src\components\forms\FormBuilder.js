import { useState, useEffect } from 'react';
import { PersonFieldDefinitions, getAllPersonFields } from '../../constants/personConstants';
import formConfigService from '../../services/formConfigService';
import apiService from '../../services/apiService';
import FieldConfigModal from './FieldConfigModal';
import FormPreview from './FormPreview';
import './FormBuilder.css';

const FormBuilder = ({ onSave, onCancel, initialConfig = null }) => {
  const [selectedHierarchy, setSelectedHierarchy] = useState({});
  const [formName, setFormName] = useState('');
  const [formDescription, setFormDescription] = useState('');
  const [availableFields, setAvailableFields] = useState([]);
  const [selectedFields, setSelectedFields] = useState([]);
  const [currentSection, setCurrentSection] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [showPreview, setShowPreview] = useState(false);
  const [showFieldConfig, setShowFieldConfig] = useState(false);
  const [configField, setConfigField] = useState(null);
  const [errors, setErrors] = useState({});
  const [saving, setSaving] = useState(false);
  const [savedForms, setSavedForms] = useState([]);
  const [showSavedForms, setShowSavedForms] = useState(false);

  // State for dropdown data
  const [divisions, setDivisions] = useState([]);
  const [categories, setCategories] = useState([]);
  const [subCategories, setSubCategories] = useState([]);
  const [loading, setLoading] = useState({
    divisions: false,
    categories: false,
    subCategories: false
  });
  const [existingFormInfo, setExistingFormInfo] = useState(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [editingFormId, setEditingFormId] = useState(null);

  useEffect(() => {
    initializeFields();
    loadSavedForms();
    if (initialConfig) {
      loadInitialConfig(initialConfig);
    }
  }, [initialConfig]);

  const initializeFields = () => {
    const allFields = getAllPersonFields();
    setAvailableFields(allFields);
  };

  // Deduplicate fields based on field key
  const deduplicateFields = (fields) => {
    const seen = new Set();
    const deduplicated = [];

    fields.forEach(field => {
      if (!seen.has(field.key)) {
        seen.add(field.key);
        deduplicated.push(field);
      }
    });

    return deduplicated;
  };

  const loadSavedForms = () => {
    const forms = formConfigService.getAllFormConfigs();
    setSavedForms(forms);
  };

  const loadInitialConfig = (config) => {
    setFormName(config.name || '');
    setFormDescription(config.description || '');

    // Deduplicate fields when loading initial config
    const fields = config.fields || [];
    const deduplicatedFields = deduplicateFields(fields);
    setSelectedFields(deduplicatedFields);

    // Set edit mode when loading existing form
    setIsEditMode(true);
    setEditingFormId(config.id || config.key);

    if (config.type === 'division' && config.associatedId) {
      // Load division info for hierarchy selector
      setSelectedHierarchy({
        divisionId: config.associatedId
      });
    } else if (config.type === 'category' && config.associatedId) {
      // Load category info for hierarchy selector
      setSelectedHierarchy({
        categoryId: config.associatedId
      });
    } else if (config.type === 'subcategory' && config.associatedId) {
      // Load subcategory info for hierarchy selector
      setSelectedHierarchy({
        subCategoryId: config.associatedId
      });
    }

    // If hierarchy is stored in config, use that
    if (config.hierarchy) {
      setSelectedHierarchy(config.hierarchy);
    }
  };

  // Load divisions on component mount
  useEffect(() => {
    loadDivisions();
  }, []);

  // Validate form creation rules when hierarchy changes
  useEffect(() => {
    if (selectedHierarchy.categoryId) {
      // Skip validation in edit mode since we're updating an existing form
      if (!isEditMode) {
        const formValidation = formConfigService.validateFormCreation(
          selectedHierarchy.categoryId,
          selectedHierarchy.subCategoryId
        );

        if (!formValidation.isValid) {
          setErrors(prev => ({
            ...prev,
            formCreation: formValidation.errors.join('. ')
          }));
        } else {
          setErrors(prev => {
            const newErrors = { ...prev };
            delete newErrors.formCreation;
            return newErrors;
          });
        }
      } else {
        // In edit mode, clear any form creation errors
        setErrors(prev => {
          const newErrors = { ...prev };
          delete newErrors.formCreation;
          return newErrors;
        });
      }

      const formInfo = formConfigService.getExistingFormInfo(
        selectedHierarchy.categoryId,
        selectedHierarchy.subCategoryId
      );
      setExistingFormInfo(formInfo);
    } else {
      setExistingFormInfo(null);
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors.formCreation;
        return newErrors;
      });
    }
  }, [selectedHierarchy.categoryId, selectedHierarchy.subCategoryId, isEditMode]);

  const loadDivisions = async () => {
    setLoading(prev => ({ ...prev, divisions: true }));
    try {
      const response = await apiService.getDivisions();
      setDivisions(response.data || []);
    } catch (error) {
      console.error('Error loading divisions:', error);
    } finally {
      setLoading(prev => ({ ...prev, divisions: false }));
    }
  };

  const loadCategories = async (divisionId) => {
    if (!divisionId) {
      setCategories([]);
      setSubCategories([]);
      return;
    }

    setLoading(prev => ({ ...prev, categories: true }));
    try {
      const response = await apiService.getCategoriesByDivision(divisionId);
      setCategories(response.data || []);
    } catch (error) {
      console.error('Error loading categories:', error);
      setCategories([]);
    } finally {
      setLoading(prev => ({ ...prev, categories: false }));
    }
  };

  const loadSubCategories = async (categoryId) => {
    if (!categoryId) {
      setSubCategories([]);
      return;
    }

    setLoading(prev => ({ ...prev, subCategories: true }));
    try {
      const response = await apiService.getSubCategoriesByCategory(categoryId);
      setSubCategories(response.data || []);
    } catch (error) {
      console.error('Error loading subcategories:', error);
      setSubCategories([]);
    } finally {
      setLoading(prev => ({ ...prev, subCategories: false }));
    }
  };

  const handleDivisionChange = (e) => {
    const divisionId = e.target.value;
    const division = divisions.find(d => d.id === parseInt(divisionId));

    setSelectedHierarchy({
      divisionId: divisionId || null,
      categoryId: null,
      subCategoryId: null,
      division: division || null,
      category: null,
      subCategory: null
    });

    setCategories([]);
    setSubCategories([]);

    if (divisionId) {
      loadCategories(divisionId);
    }
  };

  const handleCategoryChange = (e) => {
    const categoryId = e.target.value;
    const category = categories.find(c => c.id === parseInt(categoryId));

    setSelectedHierarchy(prev => ({
      ...prev,
      categoryId: categoryId || null,
      subCategoryId: null,
      category: category || null,
      subCategory: null
    }));

    setSubCategories([]);

    if (categoryId) {
      loadSubCategories(categoryId);
    }
  };

  const handleSubCategoryChange = (e) => {
    const subCategoryId = e.target.value;
    const subCategory = subCategories.find(sc => sc.id === parseInt(subCategoryId));

    setSelectedHierarchy(prev => ({
      ...prev,
      subCategoryId: subCategoryId || null,
      subCategory: subCategory || null
    }));
  };

  const handleFieldToggle = (field) => {
    const isSelected = selectedFields.some(f => f.key === field.key);

    if (isSelected) {
      // Remove all instances of this field key (in case there are duplicates)
      setSelectedFields(prev => prev.filter(f => f.key !== field.key));
    } else {
      // Add field only if it doesn't already exist
      setSelectedFields(prev => {
        const exists = prev.some(f => f.key === field.key);
        if (exists) {
          return prev;
        }
        return [...prev, { ...field }];
      });
    }
  };

  const handleFieldConfig = (field) => {
    setConfigField(field);
    setShowFieldConfig(true);
  };

  const handleFieldConfigSave = (updatedField) => {
    setSelectedFields(prev => 
      prev.map(f => f.key === updatedField.key ? updatedField : f)
    );
    setShowFieldConfig(false);
    setConfigField(null);
  };

  const handleFieldMove = (fromIndex, toIndex) => {
    const newFields = [...selectedFields];
    const [movedField] = newFields.splice(fromIndex, 1);
    newFields.splice(toIndex, 0, movedField);
    setSelectedFields(newFields);
  };

  const handleSave = async () => {
    const validation = validateForm();

    if (!validation.isValid) {
      setErrors(validation.errors);
      return;
    }

    setSaving(true);
    try {
      // Ensure complete hierarchy information is included
      const completeHierarchy = {
        ...selectedHierarchy,
        // Include division info (required)
        divisionId: selectedHierarchy.divisionId,
        division: selectedHierarchy.division,
        // Include category info (required)
        categoryId: selectedHierarchy.categoryId,
        category: selectedHierarchy.category,
        // Include subcategory info if selected (optional)
        subCategoryId: selectedHierarchy.subCategoryId || null,
        subCategory: selectedHierarchy.subCategory || null
      };

      const config = {
        name: formName,
        description: formDescription,
        fields: selectedFields,
        hierarchy: completeHierarchy,
        settings: {
          showSections: true,
          allowConditionalFields: true,
          validateOnChange: true
        }
      };

      let savedConfig;

      if (isEditMode) {
        // In edit mode, update the existing form
        // Preserve the original type and associatedId from the form being edited
        const originalForm = formConfigService.getAllFormConfigs().find(f => f.id === editingFormId || f.key === editingFormId);
        if (originalForm) {
          // Update the existing form with new data
          config.id = originalForm.id;
          config.key = originalForm.key;
          config.type = originalForm.type;
          config.associatedId = originalForm.associatedId;
          config.createdAt = originalForm.createdAt; // Preserve creation date
          config.updatedAt = new Date().toISOString(); // Update modification date

          savedConfig = formConfigService.saveFormConfig(originalForm.type, originalForm.associatedId, config);
        } else {
          throw new Error('Original form not found for editing');
        }
      } else {
        // Create new form with the most specific level selected, but include complete hierarchy
        if (selectedHierarchy.subCategoryId) {
          savedConfig = formConfigService.saveFormConfig('subcategory', selectedHierarchy.subCategoryId, config);
        } else if (selectedHierarchy.categoryId) {
          savedConfig = formConfigService.saveFormConfig('category', selectedHierarchy.categoryId, config);
        } else {
          throw new Error('Please select both division and category');
        }
      }

      // Reload saved forms list
      loadSavedForms();

      if (onSave) {
        onSave(savedConfig);
      } else {
        // Show success message for standalone usage
        const message = isEditMode ? 'Form updated successfully!' : 'Form configuration saved successfully!';
        alert(message);
        // Reset form
        setFormName('');
        setFormDescription('');
        setSelectedFields([]);
        setSelectedHierarchy({});
        setIsEditMode(false);
        setEditingFormId(null);
      }
    } catch (error) {
      console.error('Error saving form:', error);
      setErrors({ general: error.message });
    } finally {
      setSaving(false);
    }
  };

  const validateForm = () => {
    const errors = {};

    if (!formName.trim()) {
      errors.formName = 'Form name is required';
    }

    // Division and category are required, subcategory is optional
    if (!selectedHierarchy.divisionId) {
      errors.hierarchy = 'Please select a division';
    } else if (!selectedHierarchy.categoryId) {
      errors.hierarchy = 'Please select a category';
    } else if (selectedHierarchy.subCategoryId && !selectedHierarchy.categoryId) {
      errors.hierarchy = 'Cannot select subcategory without selecting category';
    }

    // Validate form creation rules (skip in edit mode)
    if (selectedHierarchy.categoryId && !isEditMode) {
      const formValidation = formConfigService.validateFormCreation(
        selectedHierarchy.categoryId,
        selectedHierarchy.subCategoryId
      );

      if (!formValidation.isValid) {
        errors.formCreation = formValidation.errors.join('. ');
      }
    }

    if (selectedFields.length === 0) {
      errors.fields = 'Please select at least one field';
    }

    // Check required fields
    const requiredFields = ['name', 'mobileNumber', 'nature'];
    const selectedFieldKeys = selectedFields.map(f => f.key);
    const missingRequired = requiredFields.filter(rf => !selectedFieldKeys.includes(rf));

    if (missingRequired.length > 0) {
      errors.requiredFields = `Required fields missing: ${missingRequired.join(', ')}`;
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  };

  const getFilteredFields = () => {
    let filtered = availableFields;

    // Filter by section
    if (currentSection !== 'all') {
      filtered = filtered.filter(field => field.section === currentSection);
    }

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(field => 
        field.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
        field.key.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    return filtered;
  };

  const getSectionCounts = () => {
    const counts = { all: availableFields.length };
    Object.keys(PersonFieldDefinitions).forEach(sectionKey => {
      counts[sectionKey] = availableFields.filter(f => f.section === sectionKey).length;
    });
    return counts;
  };

  const sectionCounts = getSectionCounts();
  const filteredFields = getFilteredFields();

  return (
    <div className="form-builder">
      <div className="form-builder-header">
        <h2>Form Builder</h2>
        <div className="header-actions">
          <button 
            type="button" 
            onClick={() => setShowPreview(true)}
            className="btn btn-secondary"
            disabled={selectedFields.length === 0}
          >
            Preview Form
          </button>
          <button
            type="button"
            onClick={handleSave}
            className="btn btn-primary"
            disabled={saving || errors.formCreation}
          >
            {saving ? (isEditMode ? 'Updating...' : 'Saving...') : (isEditMode ? 'Update Form' : 'Save Form')}
          </button>
          {onCancel && (
            <button
              type="button"
              onClick={onCancel}
              className="btn btn-outline"
            >
              Cancel
            </button>
          )}
        </div>
      </div>

      {errors.general && (
        <div className="alert alert-error">{errors.general}</div>
      )}

      {/* Validation Status */}
      {Object.keys(errors).length > 0 && (
        <div className="validation-errors">
          <h4>Please fix the following issues:</h4>
          <ul>
            {Object.entries(errors).map(([key, message]) => (
              <li key={key}>{message}</li>
            ))}
          </ul>
        </div>
      )}

      <div className="form-builder-content">
        {/* Form Configuration Panel */}
        <div className="config-panel">
          {/* Saved Forms Section */}
          <div className="config-section">
            <div className="section-header">
              <h3>Saved Forms ({savedForms.length})</h3>
              <button
                type="button"
                className="btn-link"
                onClick={() => setShowSavedForms(!showSavedForms)}
              >
                {showSavedForms ? 'Hide' : 'Show'}
              </button>
            </div>

            {showSavedForms && (
              <div className="saved-forms-list">
                {savedForms.length === 0 ? (
                  <p className="no-forms-message">No saved forms yet. Create your first form below!</p>
                ) : (
                  savedForms.map((form) => (
                    <div key={form.key} className="saved-form-item">
                      <div className="form-item-header">
                        <h4>{form.name}</h4>
                        <span className="form-type-badge">{form.type}</span>
                      </div>
                      <p className="form-description">{form.description || 'No description'}</p>
                      <div className="form-meta">
                        <span>{form.summary.fieldCount} fields</span>
                        <span>•</span>
                        <span>Updated {new Date(form.updatedAt).toLocaleDateString()}</span>
                      </div>
                      <div className="form-actions">
                        <button
                          type="button"
                          className="btn-small btn-outline"
                          onClick={() => loadInitialConfig(form)}
                        >
                          Load
                        </button>
                        <button
                          type="button"
                          className="btn-small btn-danger"
                          onClick={() => {
                            if (window.confirm('Are you sure you want to delete this form?')) {
                              formConfigService.deleteFormConfig(form.type, form.associatedId);
                              loadSavedForms();
                            }
                          }}
                        >
                          Delete
                        </button>
                      </div>
                    </div>
                  ))
                )}
              </div>
            )}
          </div>

          {/* Form Configuration Section */}
          <div className="config-section">
            <h3>Form Configuration</h3>

            <div className="form-group">
              <label>Form Name *</label>
              <input
                type="text"
                value={formName}
                onChange={(e) => setFormName(e.target.value)}
                placeholder="Enter form name"
                className={errors.formName ? 'error' : ''}
              />
              {errors.formName && <div className="error-message">{errors.formName}</div>}
            </div>

            <div className="form-group">
              <label>Description</label>
              <textarea
                value={formDescription}
                onChange={(e) => setFormDescription(e.target.value)}
                placeholder="Enter form description"
                rows="3"
              />
            </div>


          </div>

          {/* Selected Fields Panel */}
          <div className="config-section">
            <h3>Selected Fields ({selectedFields.length})</h3>
            {errors.fields && <div className="error-message">{errors.fields}</div>}
            {errors.requiredFields && <div className="error-message">{errors.requiredFields}</div>}
            
            <div className="selected-fields">
              {selectedFields.length === 0 ? (
                <div className="empty-state">
                  No fields selected. Choose fields from the available fields panel.
                </div>
              ) : (
                selectedFields.map((field, index) => (
                  <div key={field.key} className="selected-field">
                    <div className="field-info">
                      <span className="field-label">{field.label}</span>
                      <span className="field-type">{field.type}</span>
                      {field.required && <span className="required-badge">Required</span>}
                    </div>
                    <div className="field-actions">
                      <button
                        type="button"
                        onClick={() => handleFieldConfig(field)}
                        className="btn-icon"
                        title="Configure field"
                      >
                        ⚙️
                      </button>
                      <button
                        type="button"
                        onClick={() => handleFieldMove(index, Math.max(0, index - 1))}
                        className="btn-icon"
                        disabled={index === 0}
                        title="Move up"
                      >
                        ↑
                      </button>
                      <button
                        type="button"
                        onClick={() => handleFieldMove(index, Math.min(selectedFields.length - 1, index + 1))}
                        className="btn-icon"
                        disabled={index === selectedFields.length - 1}
                        title="Move down"
                      >
                        ↓
                      </button>
                      <button
                        type="button"
                        onClick={() => handleFieldToggle(field)}
                        className="btn-icon remove"
                        title="Remove field"
                      >
                        ✕
                      </button>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        </div>

        {/* Available Fields Panel */}
        <div className="fields-panel">
          <div className="fields-header">
            <h3>Available Fields</h3>
          </div>

          {/* Division/Category/SubCategory Selection */}
          <div className="hierarchy-selection" style={{
            backgroundColor: '#f8f9fa',
            border: '1px solid #e1e5e9',
            borderRadius: '8px',
            padding: '1.5rem',
            margin: '1rem 0'
          }}>
            <h4 style={{ margin: '0 0 1rem 0', color: '#495057' }}>
              Associate with Division & Category (Required) / SubCategory (Optional) *
            </h4>
            <div style={{
              fontSize: '0.875rem',
              color: '#6c757d',
              marginBottom: '1rem',
              padding: '0.75rem',
              backgroundColor: '#f8f9fa',
              borderRadius: '4px',
              border: '1px solid #e9ecef'
            }}>
              <strong>Form Creation Rules:</strong>
              <ul style={{ margin: '0.5rem 0 0 1rem', paddingLeft: '1rem' }}>
                <li>If you create a form for a category, you cannot create forms for its subcategories</li>
                <li>Each subcategory can have only one form</li>
                <li>If subcategories already have forms, you cannot create a form for the parent category</li>
              </ul>
            </div>

            <div className="hierarchy-dropdowns" style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
              gap: '1rem'
            }}>
              {/* Division Dropdown */}
              <div className="form-group">
                <label style={{
                  display: 'block',
                  marginBottom: '0.5rem',
                  fontWeight: '500',
                  color: '#495057'
                }}>
                  Division *
                </label>
                <select
                  value={selectedHierarchy.divisionId || ''}
                  onChange={handleDivisionChange}
                  disabled={loading.divisions}
                  className={errors.divisionId ? 'error' : ''}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '1px solid #ced4da',
                    borderRadius: '4px',
                    fontSize: '1rem',
                    backgroundColor: 'white'
                  }}
                >
                  <option value="">Select Division</option>
                  {divisions.map(division => (
                    <option key={division.id} value={division.id}>
                      {division.name}
                    </option>
                  ))}
                </select>
                {errors.divisionId && (
                  <div style={{ color: '#dc3545', fontSize: '0.875rem', marginTop: '0.25rem' }}>
                    {errors.divisionId}
                  </div>
                )}
              </div>

              {/* Category Dropdown */}
              <div className="form-group">
                <label style={{
                  display: 'block',
                  marginBottom: '0.5rem',
                  fontWeight: '500',
                  color: '#495057'
                }}>
                  Category *
                </label>
                <select
                  value={selectedHierarchy.categoryId || ''}
                  onChange={handleCategoryChange}
                  disabled={!selectedHierarchy.divisionId || loading.categories}
                  className={errors.categoryId ? 'error' : ''}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '1px solid #ced4da',
                    borderRadius: '4px',
                    fontSize: '1rem',
                    backgroundColor: 'white'
                  }}
                >
                  <option value="">
                    {!selectedHierarchy.divisionId ? 'Select Division first' : 'Select Category'}
                  </option>
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
                {errors.categoryId && (
                  <div style={{ color: '#dc3545', fontSize: '0.875rem', marginTop: '0.25rem' }}>
                    {errors.categoryId}
                  </div>
                )}
              </div>

              {/* SubCategory Dropdown */}
              <div className="form-group">
                <label style={{
                  display: 'block',
                  marginBottom: '0.5rem',
                  fontWeight: '500',
                  color: '#495057'
                }}>
                  SubCategory (Optional)
                </label>
                <select
                  value={selectedHierarchy.subCategoryId || ''}
                  onChange={handleSubCategoryChange}
                  disabled={!selectedHierarchy.categoryId || loading.subCategories}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '1px solid #ced4da',
                    borderRadius: '4px',
                    fontSize: '1rem',
                    backgroundColor: 'white'
                  }}
                >
                  <option value="">
                    {!selectedHierarchy.categoryId ? 'Select Category first' : 'Select SubCategory (Optional)'}
                  </option>
                  {subCategories.map(subCategory => (
                    <option key={subCategory.id} value={subCategory.id}>
                      {subCategory.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {errors.hierarchy && (
              <div style={{ color: '#dc3545', fontSize: '0.875rem', marginTop: '0.5rem' }}>
                {errors.hierarchy}
              </div>
            )}
            {errors.formCreation && (
              <div style={{ color: '#dc3545', fontSize: '0.875rem', marginTop: '0.5rem', fontWeight: 'bold' }}>
                ⚠️ {errors.formCreation}
              </div>
            )}

            {/* Existing Form Information */}
            {existingFormInfo && existingFormInfo.existingForms.length > 0 && (
              <div style={{
                marginTop: '1rem',
                padding: '0.75rem',
                backgroundColor: '#fff3cd',
                border: '1px solid #ffeaa7',
                borderRadius: '4px',
                fontSize: '0.875rem'
              }}>
                <strong>📋 Existing Forms:</strong>
                {existingFormInfo.existingForms.map((form, index) => (
                  <div key={index} style={{ marginTop: '0.5rem' }}>
                    <strong>{form.type === 'category' ? 'Category' : 'SubCategory'} Form:</strong> {form.name}
                    {form.description && <div style={{ color: '#6c757d' }}>{form.description}</div>}
                  </div>
                ))}
              </div>
            )}

            {/* Subcategories with Forms Warning */}
            {existingFormInfo && existingFormInfo.subCategoriesWithForms.length > 0 && !selectedHierarchy.subCategoryId && (
              <div style={{
                marginTop: '1rem',
                padding: '0.75rem',
                backgroundColor: '#f8d7da',
                border: '1px solid #f5c6cb',
                borderRadius: '4px',
                fontSize: '0.875rem'
              }}>
                <strong>⚠️ Warning:</strong> This category has {existingFormInfo.subCategoriesWithForms.length} subcategory form(s).
                You cannot create a form for this category.
              </div>
            )}

            {/* Success Message for Valid Selection */}
            {selectedHierarchy.categoryId && !errors.formCreation && (
              <div style={{
                marginTop: '1rem',
                padding: '0.75rem',
                backgroundColor: '#d4edda',
                border: '1px solid #c3e6cb',
                borderRadius: '4px',
                fontSize: '0.875rem'
              }}>
                <strong>✅ Valid Selection:</strong> You can create a form for this {selectedHierarchy.subCategoryId ? 'subcategory' : 'category'}.
              </div>
            )}
          </div>

          {/* Search and Filter Controls */}
          <div className="fields-controls">
            <input
              type="text"
              placeholder="Search fields..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
          </div>

          {/* Section Tabs */}
          <div className="section-tabs">
            <button
              type="button"
              onClick={() => setCurrentSection('all')}
              className={`section-tab ${currentSection === 'all' ? 'active' : ''}`}
            >
              All ({sectionCounts.all})
            </button>
            {Object.entries(PersonFieldDefinitions).map(([sectionKey, section]) => (
              <button
                key={sectionKey}
                type="button"
                onClick={() => setCurrentSection(sectionKey)}
                className={`section-tab ${currentSection === sectionKey ? 'active' : ''}`}
              >
                {section.title} ({sectionCounts[sectionKey]})
              </button>
            ))}
          </div>

          {/* Fields List */}
          <div className="fields-list">
            {filteredFields.map(field => {
              const isSelected = selectedFields.some(f => f.key === field.key);
              return (
                <div
                  key={field.key}
                  className={`field-item ${isSelected ? 'selected' : ''}`}
                  onClick={() => handleFieldToggle(field)}
                >
                  <div className="field-checkbox">
                    <input
                      type="checkbox"
                      checked={isSelected}
                      onChange={() => handleFieldToggle(field)}
                    />
                  </div>
                  <div className="field-details">
                    <div className="field-name">{field.label}</div>
                    <div className="field-meta">
                      <span className="field-type">{field.type}</span>
                      {field.required && <span className="required-badge">Required</span>}
                      {field.conditional && <span className="conditional-badge">Conditional</span>}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Modals */}
      {showFieldConfig && configField && (
        <FieldConfigModal
          field={configField}
          onSave={handleFieldConfigSave}
          onCancel={() => {
            setShowFieldConfig(false);
            setConfigField(null);
          }}
        />
      )}

      {showPreview && (
        <FormPreview
          fields={selectedFields}
          formName={formName}
          onClose={() => setShowPreview(false)}
        />
      )}
    </div>
  );
};

export default FormBuilder;
