{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Advanced\\\\Advanced CRM filed all use\\\\frontend\\\\src\\\\components\\\\Navbar.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { FiHome, FiLayers, FiLogOut, FiUser, FiBarChart2, FiGrid, FiUsers, FiUpload, FiSettings } from 'react-icons/fi';\nimport { useAuth } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Navbar = () => {\n  _s();\n  const location = useLocation();\n  const {\n    user,\n    logout\n  } = useAuth();\n  const menuItems = [{\n    path: '/',\n    label: 'Dashboard',\n    icon: FiBarChart2\n  }, {\n    path: '/divisions',\n    label: 'Division Setup',\n    icon: FiGrid\n  }, {\n    path: '/categories',\n    label: 'Categories',\n    icon: FiLayers\n  }, {\n    path: '/persons-view',\n    label: 'View Persons',\n    icon: FiUsers\n  }, {\n    path: '/persons',\n    label: 'Person Management',\n    icon: FiUser\n  }, {\n    path: '/form-builder',\n    label: 'Form Builder',\n    icon: FiSettings\n  }];\n  const handleLogout = () => {\n    logout();\n  };\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    className: \"admin-sidebar\",\n    initial: {\n      x: -250\n    },\n    animate: {\n      x: 0\n    },\n    transition: {\n      duration: 0.3\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sidebar-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"admin-profile\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"profile-avatar\",\n          children: /*#__PURE__*/_jsxDEV(FiUser, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"profile-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Admin Panel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [user === null || user === void 0 ? void 0 : user.firstName, \" \", user === null || user === void 0 ? void 0 : user.lastName]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n      className: \"sidebar-nav\",\n      children: menuItems.map((item, index) => {\n        const Icon = item.icon;\n        const isActive = location.pathname === item.path;\n        return /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            x: -20\n          },\n          animate: {\n            opacity: 1,\n            x: 0\n          },\n          transition: {\n            delay: index * 0.1\n          },\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: item.path,\n            className: `sidebar-link ${isActive ? 'active' : ''}`,\n            children: [/*#__PURE__*/_jsxDEV(Icon, {\n              className: \"sidebar-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: item.label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 15\n          }, this)\n        }, item.path, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sidebar-footer\",\n      children: /*#__PURE__*/_jsxDEV(motion.button, {\n        className: \"logout-btn\",\n        onClick: handleLogout,\n        whileHover: {\n          scale: 1.05\n        },\n        whileTap: {\n          scale: 0.95\n        },\n        children: [/*#__PURE__*/_jsxDEV(FiLogOut, {\n          className: \"sidebar-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Logout\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 25,\n    columnNumber: 5\n  }, this);\n};\n_s(Navbar, \"TOpt3E6rPwFI+KvJ+cvMOa/XJW4=\", false, function () {\n  return [useLocation, useAuth];\n});\n_c = Navbar;\nexport default Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");", "map": {"version": 3, "names": ["React", "Link", "useLocation", "motion", "FiHome", "FiLayers", "FiLogOut", "FiUser", "FiBarChart2", "<PERSON><PERSON><PERSON>", "FiUsers", "FiUpload", "FiSettings", "useAuth", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON>", "_s", "location", "user", "logout", "menuItems", "path", "label", "icon", "handleLogout", "div", "className", "initial", "x", "animate", "transition", "duration", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "firstName", "lastName", "map", "item", "index", "Icon", "isActive", "pathname", "opacity", "delay", "to", "button", "onClick", "whileHover", "scale", "whileTap", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/components/Navbar.js"], "sourcesContent": ["import React from 'react';\r\nimport { Link, useLocation } from 'react-router-dom';\r\nimport { motion } from 'framer-motion';\r\nimport { FiHome, FiLayers, FiLogOut, FiUser, FiBarChart2, FiGrid, FiUsers, FiUpload, FiSettings } from 'react-icons/fi';\r\nimport { useAuth } from '../context/AuthContext';\r\n\r\nconst Navbar = () => {\r\n  const location = useLocation();\r\n  const { user, logout } = useAuth();\r\n\r\n  const menuItems = [\r\n    { path: '/', label: 'Dashboard', icon: FiBarChart2 },\r\n    { path: '/divisions', label: 'Division Setup', icon: FiGrid },\r\n    { path: '/categories', label: 'Categories', icon: FiLayers },\r\n    { path: '/persons-view', label: 'View Persons', icon: FiUsers },\r\n    { path: '/persons', label: 'Person Management', icon: FiUser },\r\n    { path: '/form-builder', label: 'Form Builder', icon: FiSettings }\r\n  ];\r\n\r\n  const handleLogout = () => {\r\n    logout();\r\n  };\r\n\r\n  return (\r\n    <motion.div \r\n      className=\"admin-sidebar\"\r\n      initial={{ x: -250 }}\r\n      animate={{ x: 0 }}\r\n      transition={{ duration: 0.3 }}\r\n    >\r\n      <div className=\"sidebar-header\">\r\n        <div className=\"admin-profile\">\r\n          <div className=\"profile-avatar\">\r\n            <FiUser size={24} />\r\n          </div>\r\n          <div className=\"profile-info\">\r\n            <h3>Admin Panel</h3>\r\n            <p>{user?.firstName} {user?.lastName}</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <nav className=\"sidebar-nav\">\r\n        {menuItems.map((item, index) => {\r\n          const Icon = item.icon;\r\n          const isActive = location.pathname === item.path;\r\n          \r\n          return (\r\n            <motion.div\r\n              key={item.path}\r\n              initial={{ opacity: 0, x: -20 }}\r\n              animate={{ opacity: 1, x: 0 }}\r\n              transition={{ delay: index * 0.1 }}\r\n            >\r\n              <Link \r\n                to={item.path} \r\n                className={`sidebar-link ${isActive ? 'active' : ''}`}\r\n              >\r\n                <Icon className=\"sidebar-icon\" />\r\n                <span>{item.label}</span>\r\n              </Link>\r\n            </motion.div>\r\n          );\r\n        })}\r\n      </nav>\r\n\r\n      <div className=\"sidebar-footer\">\r\n        <motion.button\r\n          className=\"logout-btn\"\r\n          onClick={handleLogout}\r\n          whileHover={{ scale: 1.05 }}\r\n          whileTap={{ scale: 0.95 }}\r\n        >\r\n          <FiLogOut className=\"sidebar-icon\" />\r\n          <span>Logout</span>\r\n        </motion.button>\r\n      </div>\r\n    </motion.div>\r\n  );\r\n};\r\n\r\nexport default Navbar;"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,WAAW,EAAEC,MAAM,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,gBAAgB;AACvH,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAMC,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEiB,IAAI;IAAEC;EAAO,CAAC,GAAGP,OAAO,CAAC,CAAC;EAElC,MAAMQ,SAAS,GAAG,CAChB;IAAEC,IAAI,EAAE,GAAG;IAAEC,KAAK,EAAE,WAAW;IAAEC,IAAI,EAAEhB;EAAY,CAAC,EACpD;IAAEc,IAAI,EAAE,YAAY;IAAEC,KAAK,EAAE,gBAAgB;IAAEC,IAAI,EAAEf;EAAO,CAAC,EAC7D;IAAEa,IAAI,EAAE,aAAa;IAAEC,KAAK,EAAE,YAAY;IAAEC,IAAI,EAAEnB;EAAS,CAAC,EAC5D;IAAEiB,IAAI,EAAE,eAAe;IAAEC,KAAK,EAAE,cAAc;IAAEC,IAAI,EAAEd;EAAQ,CAAC,EAC/D;IAAEY,IAAI,EAAE,UAAU;IAAEC,KAAK,EAAE,mBAAmB;IAAEC,IAAI,EAAEjB;EAAO,CAAC,EAC9D;IAAEe,IAAI,EAAE,eAAe;IAAEC,KAAK,EAAE,cAAc;IAAEC,IAAI,EAAEZ;EAAW,CAAC,CACnE;EAED,MAAMa,YAAY,GAAGA,CAAA,KAAM;IACzBL,MAAM,CAAC,CAAC;EACV,CAAC;EAED,oBACEL,OAAA,CAACZ,MAAM,CAACuB,GAAG;IACTC,SAAS,EAAC,eAAe;IACzBC,OAAO,EAAE;MAAEC,CAAC,EAAE,CAAC;IAAI,CAAE;IACrBC,OAAO,EAAE;MAAED,CAAC,EAAE;IAAE,CAAE;IAClBE,UAAU,EAAE;MAAEC,QAAQ,EAAE;IAAI,CAAE;IAAAC,QAAA,gBAE9BlB,OAAA;MAAKY,SAAS,EAAC,gBAAgB;MAAAM,QAAA,eAC7BlB,OAAA;QAAKY,SAAS,EAAC,eAAe;QAAAM,QAAA,gBAC5BlB,OAAA;UAAKY,SAAS,EAAC,gBAAgB;UAAAM,QAAA,eAC7BlB,OAAA,CAACR,MAAM;YAAC2B,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,eACNvB,OAAA;UAAKY,SAAS,EAAC,cAAc;UAAAM,QAAA,gBAC3BlB,OAAA;YAAAkB,QAAA,EAAI;UAAW;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpBvB,OAAA;YAAAkB,QAAA,GAAId,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoB,SAAS,EAAC,GAAC,EAACpB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqB,QAAQ;UAAA;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENvB,OAAA;MAAKY,SAAS,EAAC,aAAa;MAAAM,QAAA,EACzBZ,SAAS,CAACoB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;QAC9B,MAAMC,IAAI,GAAGF,IAAI,CAAClB,IAAI;QACtB,MAAMqB,QAAQ,GAAG3B,QAAQ,CAAC4B,QAAQ,KAAKJ,IAAI,CAACpB,IAAI;QAEhD,oBACEP,OAAA,CAACZ,MAAM,CAACuB,GAAG;UAETE,OAAO,EAAE;YAAEmB,OAAO,EAAE,CAAC;YAAElB,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCC,OAAO,EAAE;YAAEiB,OAAO,EAAE,CAAC;YAAElB,CAAC,EAAE;UAAE,CAAE;UAC9BE,UAAU,EAAE;YAAEiB,KAAK,EAAEL,KAAK,GAAG;UAAI,CAAE;UAAAV,QAAA,eAEnClB,OAAA,CAACd,IAAI;YACHgD,EAAE,EAAEP,IAAI,CAACpB,IAAK;YACdK,SAAS,EAAE,gBAAgBkB,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;YAAAZ,QAAA,gBAEtDlB,OAAA,CAAC6B,IAAI;cAACjB,SAAS,EAAC;YAAc;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjCvB,OAAA;cAAAkB,QAAA,EAAOS,IAAI,CAACnB;YAAK;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB;QAAC,GAXFI,IAAI,CAACpB,IAAI;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAYJ,CAAC;MAEjB,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENvB,OAAA;MAAKY,SAAS,EAAC,gBAAgB;MAAAM,QAAA,eAC7BlB,OAAA,CAACZ,MAAM,CAAC+C,MAAM;QACZvB,SAAS,EAAC,YAAY;QACtBwB,OAAO,EAAE1B,YAAa;QACtB2B,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAK,CAAE;QAC5BC,QAAQ,EAAE;UAAED,KAAK,EAAE;QAAK,CAAE;QAAApB,QAAA,gBAE1BlB,OAAA,CAACT,QAAQ;UAACqB,SAAS,EAAC;QAAc;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrCvB,OAAA;UAAAkB,QAAA,EAAM;QAAM;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEjB,CAAC;AAACrB,EAAA,CAzEID,MAAM;EAAA,QACOd,WAAW,EACHW,OAAO;AAAA;AAAA0C,EAAA,GAF5BvC,MAAM;AA2EZ,eAAeA,MAAM;AAAC,IAAAuC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}