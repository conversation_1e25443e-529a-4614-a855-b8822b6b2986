[{"C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\context\\AuthContext.js": "3", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\Navbar.js": "4", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\ProtectedRoute.js": "5", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\CategoryManagement.js": "6", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\Dashboard.js": "7", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\DivisionSetup.js": "8", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\PersonsView.js": "9", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\PersonManagement.js": "10", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\import\\ImportPersons.js": "11", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\FormBuilder.js": "12", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\Login.js": "13", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\Pagination.js": "14", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\PersonList.js": "15", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\DynamicPersonForm.js": "16", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\FormSelectionView.js": "17", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\import\\FileUpload.js": "18", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\import\\ImportResults.js": "19", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\FieldConfigModal.js": "20", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\import\\FieldMapping.js": "21", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\FormPreview.js": "22", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\import\\ImportProgress.js": "23", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\services\\apiService.js": "24", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\services\\formConfigService.js": "25", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\constants\\personConstants.js": "26", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\HierarchicalSelector.js": "27", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\FormField.js": "28", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\import\\DivisionCategorySelection.js": "29", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\AllFormsModal.js": "30"}, {"size": 263, "mtime": 1753902733652, "results": "31", "hashOfConfig": "32"}, {"size": 2056, "mtime": 1753981799344, "results": "33", "hashOfConfig": "32"}, {"size": 2205, "mtime": 1753902733684, "results": "34", "hashOfConfig": "32"}, {"size": 2627, "mtime": 1753981799344, "results": "35", "hashOfConfig": "32"}, {"size": 480, "mtime": 1753902733668, "results": "36", "hashOfConfig": "32"}, {"size": 9616, "mtime": 1753902733668, "results": "37", "hashOfConfig": "32"}, {"size": 9249, "mtime": 1753902733668, "results": "38", "hashOfConfig": "32"}, {"size": 4195, "mtime": 1753902733668, "results": "39", "hashOfConfig": "32"}, {"size": 22852, "mtime": 1753915934729, "results": "40", "hashOfConfig": "32"}, {"size": 5838, "mtime": 1754012688901, "results": "41", "hashOfConfig": "32"}, {"size": 8156, "mtime": 1753982875438, "results": "42", "hashOfConfig": "32"}, {"size": 33689, "mtime": 1754014642284, "results": "43", "hashOfConfig": "32"}, {"size": 4164, "mtime": 1753902733668, "results": "44", "hashOfConfig": "32"}, {"size": 3257, "mtime": 1753902733668, "results": "45", "hashOfConfig": "32"}, {"size": 11009, "mtime": 1754016212788, "results": "46", "hashOfConfig": "32"}, {"size": 42156, "mtime": 1753960642253, "results": "47", "hashOfConfig": "32"}, {"size": 14452, "mtime": 1753954627628, "results": "48", "hashOfConfig": "32"}, {"size": 12054, "mtime": 1753963443321, "results": "49", "hashOfConfig": "32"}, {"size": 11255, "mtime": 1753981281797, "results": "50", "hashOfConfig": "32"}, {"size": 10100, "mtime": 1753909815406, "results": "51", "hashOfConfig": "32"}, {"size": 14987, "mtime": 1753963037736, "results": "52", "hashOfConfig": "32"}, {"size": 6753, "mtime": 1753910016943, "results": "53", "hashOfConfig": "32"}, {"size": 9504, "mtime": 1753963760972, "results": "54", "hashOfConfig": "32"}, {"size": 4518, "mtime": 1753960608168, "results": "55", "hashOfConfig": "32"}, {"size": 14697, "mtime": 1754012544766, "results": "56", "hashOfConfig": "32"}, {"size": 12511, "mtime": 1753958855389, "results": "57", "hashOfConfig": "32"}, {"size": 9084, "mtime": 1753947578656, "results": "58", "hashOfConfig": "32"}, {"size": 5304, "mtime": 1753944960474, "results": "59", "hashOfConfig": "32"}, {"size": 5037, "mtime": 1753962600675, "results": "60", "hashOfConfig": "32"}, {"size": 9208, "mtime": 1754012719848, "results": "61", "hashOfConfig": "32"}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ibwsxa", {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\context\\AuthContext.js", ["152"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\Navbar.js", ["153"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\CategoryManagement.js", ["154"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\Dashboard.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\DivisionSetup.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\PersonsView.js", ["155", "156", "157", "158"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\PersonManagement.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\import\\ImportPersons.js", ["159", "160"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\FormBuilder.js", ["161"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\Login.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\Pagination.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\PersonList.js", ["162"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\DynamicPersonForm.js", ["163", "164", "165", "166", "167", "168", "169", "170", "171"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\FormSelectionView.js", ["172", "173", "174"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\import\\FileUpload.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\import\\ImportResults.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\FieldConfigModal.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\import\\FieldMapping.js", ["175", "176"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\FormPreview.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\import\\ImportProgress.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\services\\apiService.js", ["177"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\services\\formConfigService.js", ["178", "179", "180", "181"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\constants\\personConstants.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\HierarchicalSelector.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\FormField.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\import\\DivisionCategorySelection.js", ["182", "183"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\AllFormsModal.js", ["184", "185"], [], {"ruleId": "186", "severity": 1, "message": "187", "line": 26, "column": 6, "nodeType": "188", "endLine": 26, "endColumn": 13, "suggestions": "189"}, {"ruleId": "190", "severity": 1, "message": "191", "line": 4, "column": 10, "nodeType": "192", "messageId": "193", "endLine": 4, "endColumn": 16}, {"ruleId": "190", "severity": 1, "message": "194", "line": 11, "column": 10, "nodeType": "192", "messageId": "193", "endLine": 11, "endColumn": 23}, {"ruleId": "190", "severity": 1, "message": "195", "line": 15, "column": 3, "nodeType": "192", "messageId": "193", "endLine": 15, "endColumn": 13}, {"ruleId": "190", "severity": 1, "message": "196", "line": 27, "column": 10, "nodeType": "192", "messageId": "193", "endLine": 27, "endColumn": 20}, {"ruleId": "186", "severity": 1, "message": "197", "line": 63, "column": 6, "nodeType": "188", "endLine": 63, "endColumn": 57, "suggestions": "198"}, {"ruleId": "190", "severity": 1, "message": "199", "line": 280, "column": 9, "nodeType": "192", "messageId": "193", "endLine": 280, "endColumn": 23}, {"ruleId": "190", "severity": 1, "message": "200", "line": 15, "column": 10, "nodeType": "192", "messageId": "193", "endLine": 15, "endColumn": 22}, {"ruleId": "190", "severity": 1, "message": "201", "line": 24, "column": 10, "nodeType": "192", "messageId": "193", "endLine": 24, "endColumn": 17}, {"ruleId": "186", "severity": 1, "message": "202", "line": 44, "column": 6, "nodeType": "188", "endLine": 44, "endColumn": 21, "suggestions": "203"}, {"ruleId": "186", "severity": 1, "message": "197", "line": 27, "column": 6, "nodeType": "188", "endLine": 27, "endColumn": 44, "suggestions": "204"}, {"ruleId": "190", "severity": 1, "message": "205", "line": 2, "column": 10, "nodeType": "192", "messageId": "193", "endLine": 2, "endColumn": 28}, {"ruleId": "190", "severity": 1, "message": "206", "line": 2, "column": 30, "nodeType": "192", "messageId": "193", "endLine": 2, "endColumn": 42}, {"ruleId": "190", "severity": 1, "message": "207", "line": 2, "column": 44, "nodeType": "192", "messageId": "193", "endLine": 2, "endColumn": 64}, {"ruleId": "186", "severity": 1, "message": "208", "line": 136, "column": 6, "nodeType": "188", "endLine": 136, "endColumn": 24, "suggestions": "209"}, {"ruleId": "186", "severity": 1, "message": "210", "line": 143, "column": 6, "nodeType": "188", "endLine": 143, "endColumn": 27, "suggestions": "211"}, {"ruleId": "212", "severity": 1, "message": "213", "line": 364, "column": 35, "nodeType": "214", "messageId": "215", "endLine": 364, "endColumn": 36, "suggestions": "216"}, {"ruleId": "217", "severity": 1, "message": "218", "line": 404, "column": 9, "nodeType": "219", "messageId": "220", "endLine": 435, "endColumn": 10}, {"ruleId": "212", "severity": 1, "message": "213", "line": 412, "column": 40, "nodeType": "214", "messageId": "215", "endLine": 412, "endColumn": 41, "suggestions": "221"}, {"ruleId": "212", "severity": 1, "message": "213", "line": 529, "column": 37, "nodeType": "214", "messageId": "215", "endLine": 529, "endColumn": 38, "suggestions": "222"}, {"ruleId": "190", "severity": 1, "message": "194", "line": 9, "column": 10, "nodeType": "192", "messageId": "193", "endLine": 9, "endColumn": 23}, {"ruleId": "186", "severity": 1, "message": "223", "line": 28, "column": 6, "nodeType": "188", "endLine": 28, "endColumn": 8, "suggestions": "224"}, {"ruleId": "190", "severity": 1, "message": "225", "line": 163, "column": 9, "nodeType": "192", "messageId": "193", "endLine": 163, "endColumn": 31}, {"ruleId": "186", "severity": 1, "message": "226", "line": 22, "column": 6, "nodeType": "188", "endLine": 22, "endColumn": 19, "suggestions": "227"}, {"ruleId": "186", "severity": 1, "message": "228", "line": 27, "column": 6, "nodeType": "188", "endLine": 27, "endColumn": 21, "suggestions": "229"}, {"ruleId": "230", "severity": 1, "message": "231", "line": 192, "column": 1, "nodeType": "232", "endLine": 192, "endColumn": 33}, {"ruleId": "233", "severity": 1, "message": "234", "line": 36, "column": 9, "nodeType": "235", "messageId": "236", "endLine": 36, "endColumn": 15}, {"ruleId": "237", "severity": 1, "message": "238", "line": 467, "column": 3, "nodeType": "239", "messageId": "236", "endLine": 476, "endColumn": 4}, {"ruleId": "237", "severity": 1, "message": "240", "line": 479, "column": 3, "nodeType": "239", "messageId": "236", "endLine": 495, "endColumn": 4}, {"ruleId": "230", "severity": 1, "message": "231", "line": 498, "column": 1, "nodeType": "232", "endLine": 498, "endColumn": 40}, {"ruleId": "190", "severity": 1, "message": "241", "line": 1, "column": 27, "nodeType": "192", "messageId": "193", "endLine": 1, "endColumn": 36}, {"ruleId": "190", "severity": 1, "message": "242", "line": 11, "column": 21, "nodeType": "192", "messageId": "193", "endLine": 11, "endColumn": 33}, {"ruleId": "190", "severity": 1, "message": "243", "line": 8, "column": 10, "nodeType": "192", "messageId": "193", "endLine": 8, "endColumn": 19}, {"ruleId": "190", "severity": 1, "message": "244", "line": 9, "column": 10, "nodeType": "192", "messageId": "193", "endLine": 9, "endColumn": 20}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'validateToken'. Either include it or remove the dependency array.", "ArrayExpression", ["245"], "no-unused-vars", "'FiHome' is defined but never used.", "Identifier", "unusedVar", "'subCategories' is assigned a value but never used.", "'FiBuilding' is defined but never used.", "'totalPages' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadPersons'. Either include it or remove the dependency array.", ["246"], "'getGenderLabel' is assigned a value but never used.", "'fieldMapping' is assigned a value but never used.", "'loading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadInitialConfig'. Either include it or remove the dependency array.", ["247"], ["248"], "'PersonNatureLabels' is defined but never used.", "'GenderLabels' is defined but never used.", "'WorkingProfileLabels' is defined but never used.", "React Hook useEffect has a missing dependency: 'handleCategorySelection'. Either include it or remove the dependency array.", ["249"], "React Hook useEffect has a missing dependency: 'handleSubCategorySelection'. Either include it or remove the dependency array.", ["250"], "no-useless-escape", "Unnecessary escape character: \\-.", "Literal", "unnecessaryEscape", ["251", "252"], "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", ["253", "254"], ["255", "256"], "React Hook useEffect has a missing dependency: 'loadAvailableForms'. Either include it or remove the dependency array.", ["257"], "'getFormsForSubCategory' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'autoMapFields', 'updateRequiredFieldsStatus', and 'updateUnmappedHeaders'. Either include them or remove the dependency array.", ["258"], "React Hook useEffect has missing dependencies: 'mapping' and 'updateRequiredFieldsStatus'. Either include them or remove the dependency array.", ["259"], "import/no-anonymous-default-export", "Assign instance to a variable before exporting as module default", "ExportDefaultDeclaration", "no-dupe-keys", "Duplicate key 'fields'.", "ObjectExpression", "unexpected", "no-dupe-class-members", "Duplicate name 'deleteFormConfig'.", "MethodDefinition", "Duplicate name 'clearAllFormConfigs'.", "'useEffect' is defined but never used.", "'setIsLoading' is assigned a value but never used.", "'divisions' is assigned a value but never used.", "'categories' is assigned a value but never used.", {"desc": "260", "fix": "261"}, {"desc": "262", "fix": "263"}, {"desc": "264", "fix": "265"}, {"desc": "266", "fix": "267"}, {"desc": "268", "fix": "269"}, {"desc": "270", "fix": "271"}, {"messageId": "272", "fix": "273", "desc": "274"}, {"messageId": "275", "fix": "276", "desc": "277"}, {"messageId": "272", "fix": "278", "desc": "274"}, {"messageId": "275", "fix": "279", "desc": "277"}, {"messageId": "272", "fix": "280", "desc": "274"}, {"messageId": "275", "fix": "281", "desc": "277"}, {"desc": "282", "fix": "283"}, {"desc": "284", "fix": "285"}, {"desc": "286", "fix": "287"}, "Update the dependencies array to be: [token, validateToken]", {"range": "288", "text": "289"}, "Update the dependencies array to be: [currentPage, pageSize, filters, sortBy, sortOrder, loadPersons]", {"range": "290", "text": "291"}, "Update the dependencies array to be: [initialConfig, loadInitialConfig]", {"range": "292", "text": "293"}, "Update the dependencies array to be: [loadPersons, pagination.page, pagination.pageSize]", {"range": "294", "text": "295"}, "Update the dependencies array to be: [handleCategorySelection, selectedCategory]", {"range": "296", "text": "297"}, "Update the dependencies array to be: [handleSubCategorySelection, selectedSubCategory]", {"range": "298", "text": "299"}, "removeEscape", {"range": "300", "text": "301"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "302", "text": "303"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "304", "text": "301"}, {"range": "305", "text": "303"}, {"range": "306", "text": "301"}, {"range": "307", "text": "303"}, "Update the dependencies array to be: [loadAvailableForms]", {"range": "308", "text": "309"}, "Update the dependencies array to be: [autoMapFields, fileHeaders, updateRequiredFieldsStatus, updateUnmappedHeaders]", {"range": "310", "text": "311"}, "Update the dependencies array to be: [defaultValues, mapping, updateRequiredFieldsStatus]", {"range": "312", "text": "313"}, [747, 754], "[token, validateToken]", [1595, 1646], "[currentPage, pageSize, filters, sortBy, sortOrder, loadPersons]", [1878, 1893], "[initialConfig, loadInitialConfig]", [724, 762], "[loadPersons, pagination.page, pagination.pageSize]", [4245, 4263], "[handleCategorySelection, selectedCategory]", [4435, 4456], "[handleSubCategorySelection, selectedSubCategory]", [11986, 11987], "", [11986, 11986], "\\", [13697, 13698], [13697, 13697], [19152, 19153], [19152, 19152], [1121, 1123], "[loadAvailableForms]", [961, 974], "[autoMapFields, fileHeaders, updateRequiredFieldsStatus, updateUnmappedHeaders]", [1088, 1103], "[defaultValues, mapping, updateRequiredFieldsStatus]"]