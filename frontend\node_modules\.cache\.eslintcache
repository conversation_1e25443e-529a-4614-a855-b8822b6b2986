[{"C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\context\\AuthContext.js": "3", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\Navbar.js": "4", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\ProtectedRoute.js": "5", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\CategoryManagement.js": "6", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\Dashboard.js": "7", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\DivisionSetup.js": "8", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\PersonsView.js": "9", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\PersonManagement.js": "10", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\import\\ImportPersons.js": "11", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\FormBuilder.js": "12", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\Login.js": "13", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\Pagination.js": "14", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\PersonList.js": "15", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\DynamicPersonForm.js": "16", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\FormSelectionView.js": "17", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\import\\FileUpload.js": "18", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\import\\ImportResults.js": "19", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\FieldConfigModal.js": "20", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\import\\FieldMapping.js": "21", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\FormPreview.js": "22", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\import\\ImportProgress.js": "23", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\services\\apiService.js": "24", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\services\\formConfigService.js": "25", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\constants\\personConstants.js": "26", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\HierarchicalSelector.js": "27", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\FormField.js": "28", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\import\\DivisionCategorySelection.js": "29", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\AllFormsModal.js": "30", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\PersonDetailModal.js": "31"}, {"size": 263, "mtime": 1753902733652, "results": "32", "hashOfConfig": "33"}, {"size": 2056, "mtime": 1753981799344, "results": "34", "hashOfConfig": "33"}, {"size": 2205, "mtime": 1753902733684, "results": "35", "hashOfConfig": "33"}, {"size": 2627, "mtime": 1753981799344, "results": "36", "hashOfConfig": "33"}, {"size": 480, "mtime": 1753902733668, "results": "37", "hashOfConfig": "33"}, {"size": 9616, "mtime": 1753902733668, "results": "38", "hashOfConfig": "33"}, {"size": 9249, "mtime": 1753902733668, "results": "39", "hashOfConfig": "33"}, {"size": 4195, "mtime": 1753902733668, "results": "40", "hashOfConfig": "33"}, {"size": 25123, "mtime": 1754017225000, "results": "41", "hashOfConfig": "33"}, {"size": 6215, "mtime": 1754017305623, "results": "42", "hashOfConfig": "33"}, {"size": 8156, "mtime": 1753982875438, "results": "43", "hashOfConfig": "33"}, {"size": 33689, "mtime": 1754014642284, "results": "44", "hashOfConfig": "33"}, {"size": 4164, "mtime": 1753902733668, "results": "45", "hashOfConfig": "33"}, {"size": 3257, "mtime": 1753902733668, "results": "46", "hashOfConfig": "33"}, {"size": 11009, "mtime": 1754016212788, "results": "47", "hashOfConfig": "33"}, {"size": 42156, "mtime": 1753960642253, "results": "48", "hashOfConfig": "33"}, {"size": 14452, "mtime": 1753954627628, "results": "49", "hashOfConfig": "33"}, {"size": 12054, "mtime": 1753963443321, "results": "50", "hashOfConfig": "33"}, {"size": 11255, "mtime": 1753981281797, "results": "51", "hashOfConfig": "33"}, {"size": 10100, "mtime": 1753909815406, "results": "52", "hashOfConfig": "33"}, {"size": 14987, "mtime": 1753963037736, "results": "53", "hashOfConfig": "33"}, {"size": 6753, "mtime": 1753910016943, "results": "54", "hashOfConfig": "33"}, {"size": 9504, "mtime": 1753963760972, "results": "55", "hashOfConfig": "33"}, {"size": 4518, "mtime": 1753960608168, "results": "56", "hashOfConfig": "33"}, {"size": 14697, "mtime": 1754012544766, "results": "57", "hashOfConfig": "33"}, {"size": 12511, "mtime": 1753958855389, "results": "58", "hashOfConfig": "33"}, {"size": 9084, "mtime": 1753947578656, "results": "59", "hashOfConfig": "33"}, {"size": 5304, "mtime": 1753944960474, "results": "60", "hashOfConfig": "33"}, {"size": 5037, "mtime": 1753962600675, "results": "61", "hashOfConfig": "33"}, {"size": 9208, "mtime": 1754012719848, "results": "62", "hashOfConfig": "33"}, {"size": 6411, "mtime": 1754017062574, "results": "63", "hashOfConfig": "33"}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ibwsxa", {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\context\\AuthContext.js", ["157"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\Navbar.js", ["158"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\CategoryManagement.js", ["159"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\Dashboard.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\DivisionSetup.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\PersonsView.js", ["160", "161", "162", "163", "164", "165"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\PersonManagement.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\import\\ImportPersons.js", ["166", "167"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\FormBuilder.js", ["168"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\Login.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\Pagination.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\PersonList.js", ["169"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\DynamicPersonForm.js", ["170", "171", "172", "173", "174", "175", "176", "177", "178"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\FormSelectionView.js", ["179", "180", "181"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\import\\FileUpload.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\import\\ImportResults.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\FieldConfigModal.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\import\\FieldMapping.js", ["182", "183"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\FormPreview.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\import\\ImportProgress.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\services\\apiService.js", ["184"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\services\\formConfigService.js", ["185", "186", "187", "188"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\constants\\personConstants.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\HierarchicalSelector.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\FormField.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\import\\DivisionCategorySelection.js", ["189", "190"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\AllFormsModal.js", ["191", "192"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\PersonDetailModal.js", [], [], {"ruleId": "193", "severity": 1, "message": "194", "line": 26, "column": 6, "nodeType": "195", "endLine": 26, "endColumn": 13, "suggestions": "196"}, {"ruleId": "197", "severity": 1, "message": "198", "line": 4, "column": 10, "nodeType": "199", "messageId": "200", "endLine": 4, "endColumn": 16}, {"ruleId": "197", "severity": 1, "message": "201", "line": 11, "column": 10, "nodeType": "199", "messageId": "200", "endLine": 11, "endColumn": 23}, {"ruleId": "197", "severity": 1, "message": "202", "line": 7, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 7, "endColumn": 13}, {"ruleId": "197", "severity": 1, "message": "203", "line": 16, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 16, "endColumn": 13}, {"ruleId": "197", "severity": 1, "message": "204", "line": 30, "column": 10, "nodeType": "199", "messageId": "200", "endLine": 30, "endColumn": 20}, {"ruleId": "193", "severity": 1, "message": "205", "line": 71, "column": 6, "nodeType": "195", "endLine": 71, "endColumn": 57, "suggestions": "206"}, {"ruleId": "197", "severity": 1, "message": "207", "line": 218, "column": 9, "nodeType": "199", "messageId": "200", "endLine": 218, "endColumn": 21}, {"ruleId": "197", "severity": 1, "message": "208", "line": 288, "column": 9, "nodeType": "199", "messageId": "200", "endLine": 288, "endColumn": 23}, {"ruleId": "197", "severity": 1, "message": "209", "line": 15, "column": 10, "nodeType": "199", "messageId": "200", "endLine": 15, "endColumn": 22}, {"ruleId": "197", "severity": 1, "message": "210", "line": 24, "column": 10, "nodeType": "199", "messageId": "200", "endLine": 24, "endColumn": 17}, {"ruleId": "193", "severity": 1, "message": "211", "line": 44, "column": 6, "nodeType": "195", "endLine": 44, "endColumn": 21, "suggestions": "212"}, {"ruleId": "193", "severity": 1, "message": "205", "line": 27, "column": 6, "nodeType": "195", "endLine": 27, "endColumn": 44, "suggestions": "213"}, {"ruleId": "197", "severity": 1, "message": "214", "line": 2, "column": 10, "nodeType": "199", "messageId": "200", "endLine": 2, "endColumn": 28}, {"ruleId": "197", "severity": 1, "message": "215", "line": 2, "column": 30, "nodeType": "199", "messageId": "200", "endLine": 2, "endColumn": 42}, {"ruleId": "197", "severity": 1, "message": "216", "line": 2, "column": 44, "nodeType": "199", "messageId": "200", "endLine": 2, "endColumn": 64}, {"ruleId": "193", "severity": 1, "message": "217", "line": 136, "column": 6, "nodeType": "195", "endLine": 136, "endColumn": 24, "suggestions": "218"}, {"ruleId": "193", "severity": 1, "message": "219", "line": 143, "column": 6, "nodeType": "195", "endLine": 143, "endColumn": 27, "suggestions": "220"}, {"ruleId": "221", "severity": 1, "message": "222", "line": 364, "column": 35, "nodeType": "223", "messageId": "224", "endLine": 364, "endColumn": 36, "suggestions": "225"}, {"ruleId": "226", "severity": 1, "message": "227", "line": 404, "column": 9, "nodeType": "228", "messageId": "229", "endLine": 435, "endColumn": 10}, {"ruleId": "221", "severity": 1, "message": "222", "line": 412, "column": 40, "nodeType": "223", "messageId": "224", "endLine": 412, "endColumn": 41, "suggestions": "230"}, {"ruleId": "221", "severity": 1, "message": "222", "line": 529, "column": 37, "nodeType": "223", "messageId": "224", "endLine": 529, "endColumn": 38, "suggestions": "231"}, {"ruleId": "197", "severity": 1, "message": "201", "line": 9, "column": 10, "nodeType": "199", "messageId": "200", "endLine": 9, "endColumn": 23}, {"ruleId": "193", "severity": 1, "message": "232", "line": 28, "column": 6, "nodeType": "195", "endLine": 28, "endColumn": 8, "suggestions": "233"}, {"ruleId": "197", "severity": 1, "message": "234", "line": 163, "column": 9, "nodeType": "199", "messageId": "200", "endLine": 163, "endColumn": 31}, {"ruleId": "193", "severity": 1, "message": "235", "line": 22, "column": 6, "nodeType": "195", "endLine": 22, "endColumn": 19, "suggestions": "236"}, {"ruleId": "193", "severity": 1, "message": "237", "line": 27, "column": 6, "nodeType": "195", "endLine": 27, "endColumn": 21, "suggestions": "238"}, {"ruleId": "239", "severity": 1, "message": "240", "line": 192, "column": 1, "nodeType": "241", "endLine": 192, "endColumn": 33}, {"ruleId": "242", "severity": 1, "message": "243", "line": 36, "column": 9, "nodeType": "244", "messageId": "245", "endLine": 36, "endColumn": 15}, {"ruleId": "246", "severity": 1, "message": "247", "line": 467, "column": 3, "nodeType": "248", "messageId": "245", "endLine": 476, "endColumn": 4}, {"ruleId": "246", "severity": 1, "message": "249", "line": 479, "column": 3, "nodeType": "248", "messageId": "245", "endLine": 495, "endColumn": 4}, {"ruleId": "239", "severity": 1, "message": "240", "line": 498, "column": 1, "nodeType": "241", "endLine": 498, "endColumn": 40}, {"ruleId": "197", "severity": 1, "message": "250", "line": 1, "column": 27, "nodeType": "199", "messageId": "200", "endLine": 1, "endColumn": 36}, {"ruleId": "197", "severity": 1, "message": "251", "line": 11, "column": 21, "nodeType": "199", "messageId": "200", "endLine": 11, "endColumn": 33}, {"ruleId": "197", "severity": 1, "message": "252", "line": 8, "column": 10, "nodeType": "199", "messageId": "200", "endLine": 8, "endColumn": 19}, {"ruleId": "197", "severity": 1, "message": "253", "line": 9, "column": 10, "nodeType": "199", "messageId": "200", "endLine": 9, "endColumn": 20}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'validateToken'. Either include it or remove the dependency array.", "ArrayExpression", ["254"], "no-unused-vars", "'FiHome' is defined but never used.", "Identifier", "unusedVar", "'subCategories' is assigned a value but never used.", "'FiDownload' is defined but never used.", "'FiBuilding' is defined but never used.", "'totalPages' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadPersons'. Either include it or remove the dependency array.", ["255"], "'handleExport' is assigned a value but never used.", "'getGenderLabel' is assigned a value but never used.", "'fieldMapping' is assigned a value but never used.", "'loading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadInitialConfig'. Either include it or remove the dependency array.", ["256"], ["257"], "'PersonNatureLabels' is defined but never used.", "'GenderLabels' is defined but never used.", "'WorkingProfileLabels' is defined but never used.", "React Hook useEffect has a missing dependency: 'handleCategorySelection'. Either include it or remove the dependency array.", ["258"], "React Hook useEffect has a missing dependency: 'handleSubCategorySelection'. Either include it or remove the dependency array.", ["259"], "no-useless-escape", "Unnecessary escape character: \\-.", "Literal", "unnecessaryEscape", ["260", "261"], "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", ["262", "263"], ["264", "265"], "React Hook useEffect has a missing dependency: 'loadAvailableForms'. Either include it or remove the dependency array.", ["266"], "'getFormsForSubCategory' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'autoMapFields', 'updateRequiredFieldsStatus', and 'updateUnmappedHeaders'. Either include them or remove the dependency array.", ["267"], "React Hook useEffect has missing dependencies: 'mapping' and 'updateRequiredFieldsStatus'. Either include them or remove the dependency array.", ["268"], "import/no-anonymous-default-export", "Assign instance to a variable before exporting as module default", "ExportDefaultDeclaration", "no-dupe-keys", "Duplicate key 'fields'.", "ObjectExpression", "unexpected", "no-dupe-class-members", "Duplicate name 'deleteFormConfig'.", "MethodDefinition", "Duplicate name 'clearAllFormConfigs'.", "'useEffect' is defined but never used.", "'setIsLoading' is assigned a value but never used.", "'divisions' is assigned a value but never used.", "'categories' is assigned a value but never used.", {"desc": "269", "fix": "270"}, {"desc": "271", "fix": "272"}, {"desc": "273", "fix": "274"}, {"desc": "275", "fix": "276"}, {"desc": "277", "fix": "278"}, {"desc": "279", "fix": "280"}, {"messageId": "281", "fix": "282", "desc": "283"}, {"messageId": "284", "fix": "285", "desc": "286"}, {"messageId": "281", "fix": "287", "desc": "283"}, {"messageId": "284", "fix": "288", "desc": "286"}, {"messageId": "281", "fix": "289", "desc": "283"}, {"messageId": "284", "fix": "290", "desc": "286"}, {"desc": "291", "fix": "292"}, {"desc": "293", "fix": "294"}, {"desc": "295", "fix": "296"}, "Update the dependencies array to be: [token, validateToken]", {"range": "297", "text": "298"}, "Update the dependencies array to be: [currentPage, pageSize, filters, sortBy, sortOrder, loadPersons]", {"range": "299", "text": "300"}, "Update the dependencies array to be: [initialConfig, loadInitialConfig]", {"range": "301", "text": "302"}, "Update the dependencies array to be: [loadPersons, pagination.page, pagination.pageSize]", {"range": "303", "text": "304"}, "Update the dependencies array to be: [handleCategorySelection, selectedCategory]", {"range": "305", "text": "306"}, "Update the dependencies array to be: [handleSubCategorySelection, selectedSubCategory]", {"range": "307", "text": "308"}, "removeEscape", {"range": "309", "text": "310"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "311", "text": "312"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "313", "text": "310"}, {"range": "314", "text": "312"}, {"range": "315", "text": "310"}, {"range": "316", "text": "312"}, "Update the dependencies array to be: [loadAvailableForms]", {"range": "317", "text": "318"}, "Update the dependencies array to be: [autoMapFields, fileHeaders, updateRequiredFieldsStatus, updateUnmappedHeaders]", {"range": "319", "text": "320"}, "Update the dependencies array to be: [defaultValues, mapping, updateRequiredFieldsStatus]", {"range": "321", "text": "322"}, [747, 754], "[token, validateToken]", [1929, 1980], "[currentPage, pageSize, filters, sortBy, sortOrder, loadPersons]", [1878, 1893], "[initialConfig, loadInitialConfig]", [724, 762], "[loadPersons, pagination.page, pagination.pageSize]", [4245, 4263], "[handleCategorySelection, selectedCategory]", [4435, 4456], "[handleSubCategorySelection, selectedSubCategory]", [11986, 11987], "", [11986, 11986], "\\", [13697, 13698], [13697, 13697], [19152, 19153], [19152, 19152], [1121, 1123], "[loadAvailableForms]", [961, 974], "[autoMapFields, fileHeaders, updateRequiredFieldsStatus, updateUnmappedHeaders]", [1088, 1103], "[defaultValues, mapping, updateRequiredFieldsStatus]"]