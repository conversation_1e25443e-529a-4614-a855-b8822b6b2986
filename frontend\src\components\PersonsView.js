import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import {
  FiSearch,
  FiFilter,
  FiDownload,
  FiEye,
  FiEdit,
  FiTrash2,
  FiRefreshCw,
  FiUser,
  FiMail,
  FiPhone,
  FiMapPin,
  FiBuilding,
  FiStar
} from 'react-icons/fi';
import apiService from '../services/apiService';
import Pagination from './Pagination';
import PersonDetailModal from './PersonDetailModal';
import './PersonsView.css';

const PersonsView = () => {
  const navigate = useNavigate();
  const [persons, setPersons] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [pageSize, setPageSize] = useState(20);

  // Modal states
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [selectedPerson, setSelectedPerson] = useState(null);
  const [deleteConfirm, setDeleteConfirm] = useState(null);

  // Filter states
  const [filters, setFilters] = useState({
    search: '',
    divisionId: '',
    categoryId: '',
    subCategoryId: '',
    nature: '',
    gender: '',
    workingState: '',
    district: '',
    starRating: '',
    isDeleted: false
  });

  // Dropdown data
  const [divisions, setDivisions] = useState([]);
  const [categories, setCategories] = useState([]);
  const [subCategories, setSubCategories] = useState([]);
  const [states, setStates] = useState([]);

  // UI states
  const [showFilters, setShowFilters] = useState(false);
  const [selectedPersons, setSelectedPersons] = useState([]);
  const [sortBy, setSortBy] = useState('createdAt');
  const [sortOrder, setSortOrder] = useState('desc');

  useEffect(() => {
    loadInitialData();
  }, []);

  useEffect(() => {
    loadPersons();
  }, [currentPage, pageSize, filters, sortBy, sortOrder]);

  useEffect(() => {
    if (filters.divisionId) {
      loadCategories(filters.divisionId);
    } else {
      setCategories([]);
      setSubCategories([]);
    }
  }, [filters.divisionId]);

  useEffect(() => {
    if (filters.categoryId) {
      loadSubCategories(filters.categoryId);
    } else {
      setSubCategories([]);
    }
  }, [filters.categoryId]);

  const loadInitialData = async () => {
    try {
      const [divisionsRes, statesRes] = await Promise.all([
        apiService.get('/divisions'),
        apiService.get('/states')
      ]);
      
      setDivisions(divisionsRes.data || []);
      setStates(statesRes.data || []);
    } catch (error) {
      console.error('Error loading initial data:', error);
    }
  };

  const loadCategories = async (divisionId) => {
    try {
      const response = await apiService.get(`/categories/division/${divisionId}`);
      setCategories(response.data || []);
    } catch (error) {
      console.error('Error loading categories:', error);
      setCategories([]);
    }
  };

  const loadSubCategories = async (categoryId) => {
    try {
      const response = await apiService.get(`/subcategories/category/${categoryId}`);
      setSubCategories(response.data || []);
    } catch (error) {
      console.error('Error loading subcategories:', error);
      setSubCategories([]);
    }
  };

  const loadPersons = async () => {
    try {
      setLoading(true);
      setError(null);

      const searchRequest = {
        page: currentPage,
        pageSize: pageSize,
        sortBy: sortBy,
        sortDirection: sortOrder,
        name: filters.search || null,
        divisionId: filters.divisionId ? parseInt(filters.divisionId) : null,
        categoryId: filters.categoryId ? parseInt(filters.categoryId) : null,
        subCategoryId: filters.subCategoryId ? parseInt(filters.subCategoryId) : null,
        nature: filters.nature ? parseInt(filters.nature) : null,
        gender: filters.gender ? parseInt(filters.gender) : null,
        workingState: filters.workingState || null,
        district: filters.district || null,
        minStarRating: filters.starRating ? parseInt(filters.starRating) : null,
        includeDeleted: filters.isDeleted,
        includeDivision: true,
        includeCategory: true,
        includeSubCategory: true
      };

      // Remove null values
      Object.keys(searchRequest).forEach(key => {
        if (searchRequest[key] === null || searchRequest[key] === '') {
          delete searchRequest[key];
        }
      });

      const response = await apiService.post('/persons/search', searchRequest);

      setPersons(response.data.persons || []);
      setTotalPages(response.data.totalPages || 1);
      setTotalCount(response.data.totalCount || 0);
    } catch (error) {
      console.error('Error loading persons:', error);
      setError('Failed to load persons. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
    setCurrentPage(1); // Reset to first page when filtering
  };

  const handleClearFilters = () => {
    setFilters({
      search: '',
      divisionId: '',
      categoryId: '',
      subCategoryId: '',
      nature: '',
      gender: '',
      workingState: '',
      district: '',
      starRating: '',
      isDeleted: false
    });
    setCurrentPage(1);
  };

  const handleSort = (field) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('asc');
    }
  };

  const handleSelectPerson = (personId) => {
    setSelectedPersons(prev => 
      prev.includes(personId) 
        ? prev.filter(id => id !== personId)
        : [...prev, personId]
    );
  };

  const handleSelectAll = () => {
    if (selectedPersons.length === persons.length) {
      setSelectedPersons([]);
    } else {
      setSelectedPersons(persons.map(p => p.id));
    }
  };

  const handleExport = async () => {
    try {
      const exportRequest = {
        name: filters.search || null,
        divisionId: filters.divisionId ? parseInt(filters.divisionId) : null,
        categoryId: filters.categoryId ? parseInt(filters.categoryId) : null,
        subCategoryId: filters.subCategoryId ? parseInt(filters.subCategoryId) : null,
        nature: filters.nature ? parseInt(filters.nature) : null,
        gender: filters.gender ? parseInt(filters.gender) : null,
        workingState: filters.workingState || null,
        district: filters.district || null,
        minStarRating: filters.starRating ? parseInt(filters.starRating) : null,
        includeDeleted: filters.isDeleted,
        pageSize: 10000 // Export all matching records
      };

      // Remove null values
      Object.keys(exportRequest).forEach(key => {
        if (exportRequest[key] === null || exportRequest[key] === '') {
          delete exportRequest[key];
        }
      });

      const response = await apiService.post('/persons/export', exportRequest, {
        responseType: 'blob'
      });

      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `persons_${new Date().toISOString().split('T')[0]}.xlsx`);
      document.body.appendChild(link);
      link.click();
      link.remove();
    } catch (error) {
      console.error('Error exporting persons:', error);
      alert('Failed to export persons. Please try again.');
    }
  };

  const renderStarRating = (rating) => {
    if (!rating) return <span className="no-rating">No rating</span>;
    
    return (
      <div className="star-rating">
        {[1, 2, 3, 4, 5].map(star => (
          <FiStar 
            key={star} 
            className={star <= rating ? 'star filled' : 'star'} 
          />
        ))}
      </div>
    );
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  const getNatureLabel = (nature) => {
    const natureMap = {
      1: 'Individual',
      2: 'Corporate',
      3: 'Partnership',
      4: 'Government'
    };
    return natureMap[nature] || 'Unknown';
  };

  const getGenderLabel = (gender) => {
    const genderMap = {
      1: 'Male',
      2: 'Female',
      3: 'Other'
    };
    return genderMap[gender] || 'Not specified';
  };

  // Action handlers
  const handleViewPerson = async (person) => {
    try {
      // Fetch full person details
      const response = await apiService.getPerson(person.id);
      setSelectedPerson(response.data);
      setShowDetailModal(true);
    } catch (error) {
      console.error('Error fetching person details:', error);
      setError('Failed to load person details. Please try again.');
    }
  };

  const handleEditPerson = (person) => {
    // Navigate to person management page with edit mode
    navigate('/persons', {
      state: {
        mode: 'edit',
        person: person
      }
    });
  };

  const handleDeletePerson = (person) => {
    setDeleteConfirm(person);
  };

  const confirmDelete = async () => {
    if (!deleteConfirm) return;

    try {
      await apiService.deletePerson(deleteConfirm.id);
      setDeleteConfirm(null);
      // Refresh the list
      loadPersons();
      // Show success message (you might want to add a toast notification here)
      console.log(`Person "${deleteConfirm.name}" deleted successfully`);
    } catch (error) {
      console.error('Error deleting person:', error);
      setError('Failed to delete person. Please try again.');
      setDeleteConfirm(null);
    }
  };

  const cancelDelete = () => {
    setDeleteConfirm(null);
  };

  const handleCloseDetailModal = () => {
    setShowDetailModal(false);
    setSelectedPerson(null);
  };

  const handleEditFromModal = (person) => {
    setShowDetailModal(false);
    setSelectedPerson(null);
    handleEditPerson(person);
  };

  return (
    <div className="persons-view">
      <div className="persons-header">
        <div className="header-filters">
          {/* Search */}
          <div className="filter-group">
            <label>Search</label>
            <div className="search-input">
              <FiSearch className="search-icon" />
              <input
                type="text"
                placeholder="Search by name, email, mobile..."
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
              />
            </div>
          </div>

          {/* Division */}
          <div className="filter-group">
            <label>Division</label>
            <select
              value={filters.divisionId}
              onChange={(e) => handleFilterChange('divisionId', e.target.value)}
            >
              <option value="">All Divisions</option>
              {divisions.map(division => (
                <option key={division.id} value={division.id}>
                  {division.name}
                </option>
              ))}
            </select>
          </div>

          {/* Category */}
          <div className="filter-group">
            <label>Category</label>
            <select
              value={filters.categoryId}
              onChange={(e) => handleFilterChange('categoryId', e.target.value)}
              disabled={!filters.divisionId}
            >
              <option value="">All Categories</option>
              {categories.map(category => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </select>
          </div>

          {/* Sub Category */}
          <div className="filter-group">
            <label>Sub Category</label>
            <select
              value={filters.subCategoryId}
              onChange={(e) => handleFilterChange('subCategoryId', e.target.value)}
              disabled={!filters.categoryId}
            >
              <option value="">All Sub Categories</option>
              {subCategories.map(subCategory => (
                <option key={subCategory.id} value={subCategory.id}>
                  {subCategory.name}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div className="header-actions">
          <button
            className="btn btn-outline"
            onClick={() => setShowFilters(!showFilters)}
          >
            <FiFilter />
            {showFilters ? 'Hide Filters' : 'Show Filters'}
          </button>
        </div>
      </div>

      {/* Filters Panel */}
      {showFilters && (
        <motion.div 
          className="filters-panel"
          initial={{ height: 0, opacity: 0 }}
          animate={{ height: 'auto', opacity: 1 }}
          exit={{ height: 0, opacity: 0 }}
        >
          <div className="filters-grid">
            {/* Nature */}
            <div className="filter-group">
              <label>Nature</label>
              <select
                value={filters.nature}
                onChange={(e) => handleFilterChange('nature', e.target.value)}
              >
                <option value="">All Types</option>
                <option value="1">Individual</option>
                <option value="2">Corporate</option>
                <option value="3">Partnership</option>
                <option value="4">Government</option>
              </select>
            </div>

            {/* Gender */}
            <div className="filter-group">
              <label>Gender</label>
              <select
                value={filters.gender}
                onChange={(e) => handleFilterChange('gender', e.target.value)}
              >
                <option value="">All Genders</option>
                <option value="1">Male</option>
                <option value="2">Female</option>
                <option value="3">Other</option>
              </select>
            </div>

            {/* Working State */}
            <div className="filter-group">
              <label>Working State</label>
              <select
                value={filters.workingState}
                onChange={(e) => handleFilterChange('workingState', e.target.value)}
              >
                <option value="">All States</option>
                {states.map(state => (
                  <option key={state.id} value={state.name}>
                    {state.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Star Rating */}
            <div className="filter-group">
              <label>Star Rating</label>
              <select
                value={filters.starRating}
                onChange={(e) => handleFilterChange('starRating', e.target.value)}
              >
                <option value="">All Ratings</option>
                <option value="5">5 Stars</option>
                <option value="4">4+ Stars</option>
                <option value="3">3+ Stars</option>
                <option value="2">2+ Stars</option>
                <option value="1">1+ Stars</option>
              </select>
            </div>

            {/* Clear All Filters Button */}
            <div className="filter-group">
              <label>&nbsp;</label>
              <button
                className="btn btn-outline clear-filters-btn"
                onClick={handleClearFilters}
              >
                Clear All Filters
              </button>
            </div>
          </div>

          <div className="filters-actions">
            <div className="results-info">
              {totalCount > 0 && (
                <span>Showing {persons.length} of {totalCount} results</span>
              )}
            </div>
          </div>
        </motion.div>
      )}

      {/* Results */}
      <div className="persons-content">
        {error && (
          <div className="error-message">
            <span>{error}</span>
            <button onClick={loadPersons}>Retry</button>
          </div>
        )}

        {loading ? (
          <div className="loading-state">
            <FiRefreshCw className="spinning" />
            <span>Loading persons...</span>
          </div>
        ) : persons.length === 0 ? (
          <div className="empty-state">
            <FiUser size={48} />
            <h3>No persons found</h3>
            <p>Try adjusting your filters or add some persons to get started.</p>
          </div>
        ) : (
          <>
            {/* Table Controls */}
            <div className="table-controls">
              <div className="table-info">
                <label>
                  <input
                    type="checkbox"
                    checked={selectedPersons.length === persons.length && persons.length > 0}
                    onChange={handleSelectAll}
                  />
                  {selectedPersons.length > 0 && (
                    <span>{selectedPersons.length} selected</span>
                  )}
                </label>
              </div>

              <div className="page-size-control">
                <label>
                  Show:
                  <select
                    value={pageSize}
                    onChange={(e) => setPageSize(Number(e.target.value))}
                  >
                    <option value={10}>10</option>
                    <option value={20}>20</option>
                    <option value={50}>50</option>
                    <option value={100}>100</option>
                  </select>
                  per page
                </label>
              </div>
            </div>

            {/* Persons Table */}
            <div className="persons-table-container">
              <table className="persons-table">
                <thead>
                  <tr>
                    <th>
                      <input
                        type="checkbox"
                        checked={selectedPersons.length === persons.length && persons.length > 0}
                        onChange={handleSelectAll}
                      />
                    </th>
                    <th 
                      className="sortable"
                      onClick={() => handleSort('name')}
                    >
                      Name
                      {sortBy === 'name' && (
                        <span className={`sort-indicator ${sortOrder}`}>
                          {sortOrder === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </th>
                    <th>Contact</th>
                    <th>Division/Category</th>
                    <th>Location</th>
                    <th>Nature</th>
                    <th 
                      className="sortable"
                      onClick={() => handleSort('starRating')}
                    >
                      Rating
                      {sortBy === 'starRating' && (
                        <span className={`sort-indicator ${sortOrder}`}>
                          {sortOrder === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </th>
                    <th 
                      className="sortable"
                      onClick={() => handleSort('createdAt')}
                    >
                      Created
                      {sortBy === 'createdAt' && (
                        <span className={`sort-indicator ${sortOrder}`}>
                          {sortOrder === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {persons.map(person => (
                    <motion.tr
                      key={person.id}
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className={selectedPersons.includes(person.id) ? 'selected' : ''}
                    >
                      <td>
                        <input
                          type="checkbox"
                          checked={selectedPersons.includes(person.id)}
                          onChange={() => handleSelectPerson(person.id)}
                        />
                      </td>
                      <td>
                        <div className="person-name">
                          <strong>{person.name}</strong>
                          {person.firmName && (
                            <small>{person.firmName}</small>
                          )}
                        </div>
                      </td>
                      <td>
                        <div className="contact-info">
                          <div className="contact-item">
                            <FiPhone size={12} />
                            <span>{person.mobileNumber}</span>
                          </div>
                          <div className="contact-item">
                            <FiMail size={12} />
                            <span>{person.primaryEmailId}</span>
                          </div>
                        </div>
                      </td>
                      <td>
                        <div className="hierarchy-info">
                          <div>{person.division?.name}</div>
                          <small>{person.category?.name}</small>
                          {person.subCategory && (
                            <small>{person.subCategory.name}</small>
                          )}
                        </div>
                      </td>
                      <td>
                        <div className="location-info">
                          <div className="location-item">
                            <FiMapPin size={12} />
                            <span>{person.district}, {person.workingState}</span>
                          </div>
                        </div>
                      </td>
                      <td>
                        <span className={`nature-badge nature-${person.nature}`}>
                          {getNatureLabel(person.nature)}
                        </span>
                      </td>
                      <td>
                        {renderStarRating(person.starRating)}
                      </td>
                      <td>
                        <small>{formatDate(person.createdAt)}</small>
                      </td>
                      <td>
                        <div className="action-buttons">
                          <button
                            className="btn-icon"
                            title="View Details"
                            onClick={() => handleViewPerson(person)}
                          >
                            <FiEye />
                          </button>
                          <button
                            className="btn-icon"
                            title="Edit"
                            onClick={() => handleEditPerson(person)}
                          >
                            <FiEdit />
                          </button>
                          <button
                            className="btn-icon danger"
                            title="Delete"
                            onClick={() => handleDeletePerson(person)}
                          >
                            <FiTrash2 />
                          </button>
                        </div>
                      </td>
                    </motion.tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            <Pagination
              currentPage={currentPage}
              totalItems={totalCount}
              itemsPerPage={pageSize}
              onPageChange={setCurrentPage}
            />
          </>
        )}
      </div>

      {/* Person Detail Modal */}
      {showDetailModal && selectedPerson && (
        <PersonDetailModal
          person={selectedPerson}
          onClose={handleCloseDetailModal}
          onEdit={handleEditFromModal}
        />
      )}

      {/* Delete Confirmation Modal */}
      {deleteConfirm && (
        <div className="delete-confirm-overlay">
          <div className="delete-confirm-modal">
            <div className="delete-confirm-header">
              <h3>Confirm Delete</h3>
            </div>
            <div className="delete-confirm-body">
              <p>Are you sure you want to delete <strong>"{deleteConfirm.name}"</strong>?</p>
              <p className="warning-text">This action cannot be undone.</p>
            </div>
            <div className="delete-confirm-actions">
              <button onClick={cancelDelete} className="btn-cancel">Cancel</button>
              <button onClick={confirmDelete} className="btn-delete">Delete</button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PersonsView;
