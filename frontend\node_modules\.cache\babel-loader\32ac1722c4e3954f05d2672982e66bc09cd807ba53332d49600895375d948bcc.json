{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Advanced\\\\Advanced CRM filed all use\\\\frontend\\\\src\\\\components\\\\forms\\\\FormBuilder.js\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport { PersonFieldDefinitions, getAllPersonFields } from '../../constants/personConstants';\nimport formConfigService from '../../services/formConfigService';\nimport apiService from '../../services/apiService';\nimport FieldConfigModal from './FieldConfigModal';\nimport FormPreview from './FormPreview';\nimport './FormBuilder.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FormBuilder = ({\n  onSave,\n  onCancel,\n  initialConfig = null\n}) => {\n  _s();\n  const [selectedHierarchy, setSelectedHierarchy] = useState({});\n  const [formName, setFormName] = useState('');\n  const [formDescription, setFormDescription] = useState('');\n  const [availableFields, setAvailableFields] = useState([]);\n  const [selectedFields, setSelectedFields] = useState([]);\n  const [currentSection, setCurrentSection] = useState('all');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showPreview, setShowPreview] = useState(false);\n  const [showFieldConfig, setShowFieldConfig] = useState(false);\n  const [configField, setConfigField] = useState(null);\n  const [errors, setErrors] = useState({});\n  const [saving, setSaving] = useState(false);\n  const [savedForms, setSavedForms] = useState([]);\n  const [showSavedForms, setShowSavedForms] = useState(false);\n\n  // State for dropdown data\n  const [divisions, setDivisions] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [subCategories, setSubCategories] = useState([]);\n  const [loading, setLoading] = useState({\n    divisions: false,\n    categories: false,\n    subCategories: false\n  });\n  const [existingFormInfo, setExistingFormInfo] = useState(null);\n  const [isEditMode, setIsEditMode] = useState(false);\n  const [editingFormId, setEditingFormId] = useState(null);\n  useEffect(() => {\n    initializeFields();\n    loadSavedForms();\n    if (initialConfig) {\n      loadInitialConfig(initialConfig);\n    }\n  }, [initialConfig]);\n  const initializeFields = () => {\n    const allFields = getAllPersonFields();\n    setAvailableFields(allFields);\n  };\n\n  // Deduplicate fields based on field key\n  const deduplicateFields = fields => {\n    const seen = new Set();\n    const deduplicated = [];\n    fields.forEach(field => {\n      if (!seen.has(field.key)) {\n        seen.add(field.key);\n        deduplicated.push(field);\n      }\n    });\n    return deduplicated;\n  };\n  const loadSavedForms = () => {\n    const forms = formConfigService.getAllFormConfigs();\n    setSavedForms(forms);\n  };\n  const loadInitialConfig = config => {\n    setFormName(config.name || '');\n    setFormDescription(config.description || '');\n\n    // Deduplicate fields when loading initial config\n    const fields = config.fields || [];\n    const deduplicatedFields = deduplicateFields(fields);\n    setSelectedFields(deduplicatedFields);\n\n    // Set edit mode when loading existing form\n    setIsEditMode(true);\n    setEditingFormId(config.id || config.key);\n    if (config.type === 'division' && config.associatedId) {\n      // Load division info for hierarchy selector\n      setSelectedHierarchy({\n        divisionId: config.associatedId\n      });\n    } else if (config.type === 'category' && config.associatedId) {\n      // Load category info for hierarchy selector\n      setSelectedHierarchy({\n        categoryId: config.associatedId\n      });\n    } else if (config.type === 'subcategory' && config.associatedId) {\n      // Load subcategory info for hierarchy selector\n      setSelectedHierarchy({\n        subCategoryId: config.associatedId\n      });\n    }\n\n    // If hierarchy is stored in config, use that\n    if (config.hierarchy) {\n      setSelectedHierarchy(config.hierarchy);\n    }\n  };\n\n  // Load divisions on component mount\n  useEffect(() => {\n    loadDivisions();\n  }, []);\n\n  // Validate form creation rules when hierarchy changes\n  useEffect(() => {\n    if (selectedHierarchy.categoryId) {\n      // Skip validation in edit mode since we're updating an existing form\n      if (!isEditMode) {\n        const formValidation = formConfigService.validateFormCreation(selectedHierarchy.categoryId, selectedHierarchy.subCategoryId);\n        if (!formValidation.isValid) {\n          setErrors(prev => ({\n            ...prev,\n            formCreation: formValidation.errors.join('. ')\n          }));\n        } else {\n          setErrors(prev => {\n            const newErrors = {\n              ...prev\n            };\n            delete newErrors.formCreation;\n            return newErrors;\n          });\n        }\n      } else {\n        // In edit mode, clear any form creation errors\n        setErrors(prev => {\n          const newErrors = {\n            ...prev\n          };\n          delete newErrors.formCreation;\n          return newErrors;\n        });\n      }\n      const formInfo = formConfigService.getExistingFormInfo(selectedHierarchy.categoryId, selectedHierarchy.subCategoryId);\n      setExistingFormInfo(formInfo);\n    } else {\n      setExistingFormInfo(null);\n      setErrors(prev => {\n        const newErrors = {\n          ...prev\n        };\n        delete newErrors.formCreation;\n        return newErrors;\n      });\n    }\n  }, [selectedHierarchy.categoryId, selectedHierarchy.subCategoryId, isEditMode]);\n  const loadDivisions = async () => {\n    setLoading(prev => ({\n      ...prev,\n      divisions: true\n    }));\n    try {\n      const response = await apiService.getDivisions();\n      setDivisions(response.data || []);\n    } catch (error) {\n      console.error('Error loading divisions:', error);\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        divisions: false\n      }));\n    }\n  };\n  const loadCategories = async divisionId => {\n    if (!divisionId) {\n      setCategories([]);\n      setSubCategories([]);\n      return;\n    }\n    setLoading(prev => ({\n      ...prev,\n      categories: true\n    }));\n    try {\n      const response = await apiService.getCategoriesByDivision(divisionId);\n      setCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading categories:', error);\n      setCategories([]);\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        categories: false\n      }));\n    }\n  };\n  const loadSubCategories = async categoryId => {\n    if (!categoryId) {\n      setSubCategories([]);\n      return;\n    }\n    setLoading(prev => ({\n      ...prev,\n      subCategories: true\n    }));\n    try {\n      const response = await apiService.getSubCategoriesByCategory(categoryId);\n      setSubCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading subcategories:', error);\n      setSubCategories([]);\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        subCategories: false\n      }));\n    }\n  };\n  const handleDivisionChange = e => {\n    const divisionId = e.target.value;\n    const division = divisions.find(d => d.id === parseInt(divisionId));\n    setSelectedHierarchy({\n      divisionId: divisionId || null,\n      categoryId: null,\n      subCategoryId: null,\n      division: division || null,\n      category: null,\n      subCategory: null\n    });\n    setCategories([]);\n    setSubCategories([]);\n    if (divisionId) {\n      loadCategories(divisionId);\n    }\n  };\n  const handleCategoryChange = e => {\n    const categoryId = e.target.value;\n    const category = categories.find(c => c.id === parseInt(categoryId));\n    setSelectedHierarchy(prev => ({\n      ...prev,\n      categoryId: categoryId || null,\n      subCategoryId: null,\n      category: category || null,\n      subCategory: null\n    }));\n    setSubCategories([]);\n    if (categoryId) {\n      loadSubCategories(categoryId);\n    }\n  };\n  const handleSubCategoryChange = e => {\n    const subCategoryId = e.target.value;\n    const subCategory = subCategories.find(sc => sc.id === parseInt(subCategoryId));\n    setSelectedHierarchy(prev => ({\n      ...prev,\n      subCategoryId: subCategoryId || null,\n      subCategory: subCategory || null\n    }));\n  };\n  const handleFieldToggle = field => {\n    const isSelected = selectedFields.some(f => f.key === field.key);\n    if (isSelected) {\n      // Remove all instances of this field key (in case there are duplicates)\n      setSelectedFields(prev => prev.filter(f => f.key !== field.key));\n    } else {\n      // Add field only if it doesn't already exist\n      setSelectedFields(prev => {\n        const exists = prev.some(f => f.key === field.key);\n        if (exists) {\n          return prev;\n        }\n        return [...prev, {\n          ...field\n        }];\n      });\n    }\n  };\n  const handleFieldConfig = field => {\n    setConfigField(field);\n    setShowFieldConfig(true);\n  };\n  const handleFieldConfigSave = updatedField => {\n    setSelectedFields(prev => prev.map(f => f.key === updatedField.key ? updatedField : f));\n    setShowFieldConfig(false);\n    setConfigField(null);\n  };\n  const handleFieldMove = (fromIndex, toIndex) => {\n    const newFields = [...selectedFields];\n    const [movedField] = newFields.splice(fromIndex, 1);\n    newFields.splice(toIndex, 0, movedField);\n    setSelectedFields(newFields);\n  };\n  const handleSave = async () => {\n    const validation = validateForm();\n    if (!validation.isValid) {\n      setErrors(validation.errors);\n      return;\n    }\n    setSaving(true);\n    try {\n      // Ensure complete hierarchy information is included\n      const completeHierarchy = {\n        ...selectedHierarchy,\n        // Include division info (required)\n        divisionId: selectedHierarchy.divisionId,\n        division: selectedHierarchy.division,\n        // Include category info (required)\n        categoryId: selectedHierarchy.categoryId,\n        category: selectedHierarchy.category,\n        // Include subcategory info if selected (optional)\n        subCategoryId: selectedHierarchy.subCategoryId || null,\n        subCategory: selectedHierarchy.subCategory || null\n      };\n      const config = {\n        name: formName,\n        description: formDescription,\n        fields: selectedFields,\n        hierarchy: completeHierarchy,\n        settings: {\n          showSections: true,\n          allowConditionalFields: true,\n          validateOnChange: true\n        }\n      };\n      let savedConfig;\n      if (isEditMode) {\n        // In edit mode, update the existing form\n        // Preserve the original type and associatedId from the form being edited\n        const originalForm = formConfigService.getAllFormConfigs().find(f => f.id === editingFormId || f.key === editingFormId);\n        if (originalForm) {\n          // Update the existing form with new data\n          config.id = originalForm.id;\n          config.key = originalForm.key;\n          config.type = originalForm.type;\n          config.associatedId = originalForm.associatedId;\n          config.createdAt = originalForm.createdAt; // Preserve creation date\n          config.updatedAt = new Date().toISOString(); // Update modification date\n\n          savedConfig = formConfigService.saveFormConfig(originalForm.type, originalForm.associatedId, config);\n        } else {\n          throw new Error('Original form not found for editing');\n        }\n      } else {\n        // Create new form with the most specific level selected, but include complete hierarchy\n        if (selectedHierarchy.subCategoryId) {\n          savedConfig = formConfigService.saveFormConfig('subcategory', selectedHierarchy.subCategoryId, config);\n        } else if (selectedHierarchy.categoryId) {\n          savedConfig = formConfigService.saveFormConfig('category', selectedHierarchy.categoryId, config);\n        } else {\n          throw new Error('Please select both division and category');\n        }\n      }\n\n      // Reload saved forms list\n      loadSavedForms();\n      if (onSave) {\n        onSave(savedConfig);\n      } else {\n        // Show success message for standalone usage\n        const message = isEditMode ? 'Form updated successfully!' : 'Form configuration saved successfully!';\n        alert(message);\n        // Reset form\n        setFormName('');\n        setFormDescription('');\n        setSelectedFields([]);\n        setSelectedHierarchy({});\n        setIsEditMode(false);\n        setEditingFormId(null);\n      }\n    } catch (error) {\n      console.error('Error saving form:', error);\n      setErrors({\n        general: error.message\n      });\n    } finally {\n      setSaving(false);\n    }\n  };\n  const validateForm = () => {\n    const errors = {};\n    if (!formName.trim()) {\n      errors.formName = 'Form name is required';\n    }\n\n    // Division and category are required, subcategory is optional\n    if (!selectedHierarchy.divisionId) {\n      errors.hierarchy = 'Please select a division';\n    } else if (!selectedHierarchy.categoryId) {\n      errors.hierarchy = 'Please select a category';\n    } else if (selectedHierarchy.subCategoryId && !selectedHierarchy.categoryId) {\n      errors.hierarchy = 'Cannot select subcategory without selecting category';\n    }\n\n    // Validate form creation rules\n    if (selectedHierarchy.categoryId) {\n      const formValidation = formConfigService.validateFormCreation(selectedHierarchy.categoryId, selectedHierarchy.subCategoryId);\n      if (!formValidation.isValid) {\n        errors.formCreation = formValidation.errors.join('. ');\n      }\n    }\n    if (selectedFields.length === 0) {\n      errors.fields = 'Please select at least one field';\n    }\n\n    // Check required fields\n    const requiredFields = ['name', 'mobileNumber', 'nature'];\n    const selectedFieldKeys = selectedFields.map(f => f.key);\n    const missingRequired = requiredFields.filter(rf => !selectedFieldKeys.includes(rf));\n    if (missingRequired.length > 0) {\n      errors.requiredFields = `Required fields missing: ${missingRequired.join(', ')}`;\n    }\n    return {\n      isValid: Object.keys(errors).length === 0,\n      errors\n    };\n  };\n  const getFilteredFields = () => {\n    let filtered = availableFields;\n\n    // Filter by section\n    if (currentSection !== 'all') {\n      filtered = filtered.filter(field => field.section === currentSection);\n    }\n\n    // Filter by search term\n    if (searchTerm) {\n      filtered = filtered.filter(field => field.label.toLowerCase().includes(searchTerm.toLowerCase()) || field.key.toLowerCase().includes(searchTerm.toLowerCase()));\n    }\n    return filtered;\n  };\n  const getSectionCounts = () => {\n    const counts = {\n      all: availableFields.length\n    };\n    Object.keys(PersonFieldDefinitions).forEach(sectionKey => {\n      counts[sectionKey] = availableFields.filter(f => f.section === sectionKey).length;\n    });\n    return counts;\n  };\n  const sectionCounts = getSectionCounts();\n  const filteredFields = getFilteredFields();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"form-builder\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-builder-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Form Builder\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 467,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: () => setShowPreview(true),\n          className: \"btn btn-secondary\",\n          disabled: selectedFields.length === 0,\n          children: \"Preview Form\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 469,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: handleSave,\n          className: \"btn btn-primary\",\n          disabled: saving || errors.formCreation,\n          children: saving ? isEditMode ? 'Updating...' : 'Saving...' : isEditMode ? 'Update Form' : 'Save Form'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 477,\n          columnNumber: 11\n        }, this), onCancel && /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: onCancel,\n          className: \"btn btn-outline\",\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 486,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 468,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 466,\n      columnNumber: 7\n    }, this), errors.general && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"alert alert-error\",\n      children: errors.general\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 498,\n      columnNumber: 9\n    }, this), Object.keys(errors).length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"validation-errors\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        children: \"Please fix the following issues:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 504,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n        children: Object.entries(errors).map(([key, message]) => /*#__PURE__*/_jsxDEV(\"li\", {\n          children: message\n        }, key, false, {\n          fileName: _jsxFileName,\n          lineNumber: 507,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 505,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 503,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-builder-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"config-panel\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"config-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: [\"Saved Forms (\", savedForms.length, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 519,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"btn-link\",\n              onClick: () => setShowSavedForms(!showSavedForms),\n              children: showSavedForms ? 'Hide' : 'Show'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 520,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 518,\n            columnNumber: 13\n          }, this), showSavedForms && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"saved-forms-list\",\n            children: savedForms.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"no-forms-message\",\n              children: \"No saved forms yet. Create your first form below!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 532,\n              columnNumber: 19\n            }, this) : savedForms.map(form => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"saved-form-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-item-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: form.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 537,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"form-type-badge\",\n                  children: form.type\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 538,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 536,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"form-description\",\n                children: form.description || 'No description'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 540,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-meta\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [form.summary.fieldCount, \" fields\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 542,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u2022\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 543,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"Updated \", new Date(form.updatedAt).toLocaleDateString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 544,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 541,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-actions\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  className: \"btn-small btn-outline\",\n                  onClick: () => loadInitialConfig(form),\n                  children: \"Load\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 547,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  className: \"btn-small btn-danger\",\n                  onClick: () => {\n                    if (window.confirm('Are you sure you want to delete this form?')) {\n                      formConfigService.deleteFormConfig(form.type, form.associatedId);\n                      loadSavedForms();\n                    }\n                  },\n                  children: \"Delete\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 554,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 546,\n                columnNumber: 23\n              }, this)]\n            }, form.key, true, {\n              fileName: _jsxFileName,\n              lineNumber: 535,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 530,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 517,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"config-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Form Configuration\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 576,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Form Name *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 579,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: formName,\n              onChange: e => setFormName(e.target.value),\n              placeholder: \"Enter form name\",\n              className: errors.formName ? 'error' : ''\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 580,\n              columnNumber: 15\n            }, this), errors.formName && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"error-message\",\n              children: errors.formName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 587,\n              columnNumber: 35\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 578,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Description\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 591,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              value: formDescription,\n              onChange: e => setFormDescription(e.target.value),\n              placeholder: \"Enter form description\",\n              rows: \"3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 592,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 590,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 575,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"config-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: [\"Selected Fields (\", selectedFields.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 605,\n            columnNumber: 13\n          }, this), errors.fields && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.fields\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 606,\n            columnNumber: 31\n          }, this), errors.requiredFields && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.requiredFields\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 607,\n            columnNumber: 39\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"selected-fields\",\n            children: selectedFields.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"empty-state\",\n              children: \"No fields selected. Choose fields from the available fields panel.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 611,\n              columnNumber: 17\n            }, this) : selectedFields.map((field, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"selected-field\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"field-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"field-label\",\n                  children: field.label\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 618,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"field-type\",\n                  children: field.type\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 619,\n                  columnNumber: 23\n                }, this), field.required && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"required-badge\",\n                  children: \"Required\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 620,\n                  columnNumber: 42\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 617,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"field-actions\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => handleFieldConfig(field),\n                  className: \"btn-icon\",\n                  title: \"Configure field\",\n                  children: \"\\u2699\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 623,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => handleFieldMove(index, Math.max(0, index - 1)),\n                  className: \"btn-icon\",\n                  disabled: index === 0,\n                  title: \"Move up\",\n                  children: \"\\u2191\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 631,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => handleFieldMove(index, Math.min(selectedFields.length - 1, index + 1)),\n                  className: \"btn-icon\",\n                  disabled: index === selectedFields.length - 1,\n                  title: \"Move down\",\n                  children: \"\\u2193\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 640,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => handleFieldToggle(field),\n                  className: \"btn-icon remove\",\n                  title: \"Remove field\",\n                  children: \"\\u2715\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 649,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 622,\n                columnNumber: 21\n              }, this)]\n            }, field.key, true, {\n              fileName: _jsxFileName,\n              lineNumber: 616,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 609,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 604,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 515,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fields-panel\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"fields-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Available Fields\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 668,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 667,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hierarchy-selection\",\n          style: {\n            backgroundColor: '#f8f9fa',\n            border: '1px solid #e1e5e9',\n            borderRadius: '8px',\n            padding: '1.5rem',\n            margin: '1rem 0'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              margin: '0 0 1rem 0',\n              color: '#495057'\n            },\n            children: \"Associate with Division & Category (Required) / SubCategory (Optional) *\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 679,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '0.875rem',\n              color: '#6c757d',\n              marginBottom: '1rem',\n              padding: '0.75rem',\n              backgroundColor: '#f8f9fa',\n              borderRadius: '4px',\n              border: '1px solid #e9ecef'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Form Creation Rules:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 691,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              style: {\n                margin: '0.5rem 0 0 1rem',\n                paddingLeft: '1rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"If you create a form for a category, you cannot create forms for its subcategories\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 693,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Each subcategory can have only one form\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 694,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"If subcategories already have forms, you cannot create a form for the parent category\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 695,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 692,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 682,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hierarchy-dropdowns\",\n            style: {\n              display: 'grid',\n              gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n              gap: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: 'block',\n                  marginBottom: '0.5rem',\n                  fontWeight: '500',\n                  color: '#495057'\n                },\n                children: \"Division *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 706,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: selectedHierarchy.divisionId || '',\n                onChange: handleDivisionChange,\n                disabled: loading.divisions,\n                className: errors.divisionId ? 'error' : '',\n                style: {\n                  width: '100%',\n                  padding: '0.75rem',\n                  border: '1px solid #ced4da',\n                  borderRadius: '4px',\n                  fontSize: '1rem',\n                  backgroundColor: 'white'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select Division\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 728,\n                  columnNumber: 19\n                }, this), divisions.map(division => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: division.id,\n                  children: division.name\n                }, division.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 730,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 714,\n                columnNumber: 17\n              }, this), errors.divisionId && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: '#dc3545',\n                  fontSize: '0.875rem',\n                  marginTop: '0.25rem'\n                },\n                children: errors.divisionId\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 736,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 705,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: 'block',\n                  marginBottom: '0.5rem',\n                  fontWeight: '500',\n                  color: '#495057'\n                },\n                children: \"Category *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 744,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: selectedHierarchy.categoryId || '',\n                onChange: handleCategoryChange,\n                disabled: !selectedHierarchy.divisionId || loading.categories,\n                className: errors.categoryId ? 'error' : '',\n                style: {\n                  width: '100%',\n                  padding: '0.75rem',\n                  border: '1px solid #ced4da',\n                  borderRadius: '4px',\n                  fontSize: '1rem',\n                  backgroundColor: 'white'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: !selectedHierarchy.divisionId ? 'Select Division first' : 'Select Category'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 766,\n                  columnNumber: 19\n                }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: category.id,\n                  children: category.name\n                }, category.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 770,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 752,\n                columnNumber: 17\n              }, this), errors.categoryId && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: '#dc3545',\n                  fontSize: '0.875rem',\n                  marginTop: '0.25rem'\n                },\n                children: errors.categoryId\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 776,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 743,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: 'block',\n                  marginBottom: '0.5rem',\n                  fontWeight: '500',\n                  color: '#495057'\n                },\n                children: \"SubCategory (Optional)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 784,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: selectedHierarchy.subCategoryId || '',\n                onChange: handleSubCategoryChange,\n                disabled: !selectedHierarchy.categoryId || loading.subCategories,\n                style: {\n                  width: '100%',\n                  padding: '0.75rem',\n                  border: '1px solid #ced4da',\n                  borderRadius: '4px',\n                  fontSize: '1rem',\n                  backgroundColor: 'white'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: !selectedHierarchy.categoryId ? 'Select Category first' : 'Select SubCategory (Optional)'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 805,\n                  columnNumber: 19\n                }, this), subCategories.map(subCategory => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: subCategory.id,\n                  children: subCategory.name\n                }, subCategory.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 809,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 792,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 783,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 699,\n            columnNumber: 13\n          }, this), errors.hierarchy && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#dc3545',\n              fontSize: '0.875rem',\n              marginTop: '0.5rem'\n            },\n            children: errors.hierarchy\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 818,\n            columnNumber: 15\n          }, this), errors.formCreation && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#dc3545',\n              fontSize: '0.875rem',\n              marginTop: '0.5rem',\n              fontWeight: 'bold'\n            },\n            children: [\"\\u26A0\\uFE0F \", errors.formCreation]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 823,\n            columnNumber: 15\n          }, this), existingFormInfo && existingFormInfo.existingForms.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: '1rem',\n              padding: '0.75rem',\n              backgroundColor: '#fff3cd',\n              border: '1px solid #ffeaa7',\n              borderRadius: '4px',\n              fontSize: '0.875rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\uD83D\\uDCCB Existing Forms:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 838,\n              columnNumber: 17\n            }, this), existingFormInfo.existingForms.map((form, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: '0.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: [form.type === 'category' ? 'Category' : 'SubCategory', \" Form:\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 841,\n                columnNumber: 21\n              }, this), \" \", form.name, form.description && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: '#6c757d'\n                },\n                children: form.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 842,\n                columnNumber: 42\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 840,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 830,\n            columnNumber: 15\n          }, this), existingFormInfo && existingFormInfo.subCategoriesWithForms.length > 0 && !selectedHierarchy.subCategoryId && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: '1rem',\n              padding: '0.75rem',\n              backgroundColor: '#f8d7da',\n              border: '1px solid #f5c6cb',\n              borderRadius: '4px',\n              fontSize: '0.875rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u26A0\\uFE0F Warning:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 858,\n              columnNumber: 17\n            }, this), \" This category has \", existingFormInfo.subCategoriesWithForms.length, \" subcategory form(s). You cannot create a form for this category.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 850,\n            columnNumber: 15\n          }, this), selectedHierarchy.categoryId && !errors.formCreation && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: '1rem',\n              padding: '0.75rem',\n              backgroundColor: '#d4edda',\n              border: '1px solid #c3e6cb',\n              borderRadius: '4px',\n              fontSize: '0.875rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u2705 Valid Selection:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 873,\n              columnNumber: 17\n            }, this), \" You can create a form for this \", selectedHierarchy.subCategoryId ? 'subcategory' : 'category', \".\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 865,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 672,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"fields-controls\",\n          children: /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Search fields...\",\n            value: searchTerm,\n            onChange: e => setSearchTerm(e.target.value),\n            className: \"search-input\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 880,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 879,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"section-tabs\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: () => setCurrentSection('all'),\n            className: `section-tab ${currentSection === 'all' ? 'active' : ''}`,\n            children: [\"All (\", sectionCounts.all, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 891,\n            columnNumber: 13\n          }, this), Object.entries(PersonFieldDefinitions).map(([sectionKey, section]) => /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: () => setCurrentSection(sectionKey),\n            className: `section-tab ${currentSection === sectionKey ? 'active' : ''}`,\n            children: [section.title, \" (\", sectionCounts[sectionKey], \")\"]\n          }, sectionKey, true, {\n            fileName: _jsxFileName,\n            lineNumber: 899,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 890,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"fields-list\",\n          children: filteredFields.map(field => {\n            const isSelected = selectedFields.some(f => f.key === field.key);\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `field-item ${isSelected ? 'selected' : ''}`,\n              onClick: () => handleFieldToggle(field),\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"field-checkbox\",\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  checked: isSelected,\n                  onChange: () => handleFieldToggle(field)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 921,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 920,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"field-details\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"field-name\",\n                  children: field.label\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 928,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"field-meta\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"field-type\",\n                    children: field.type\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 930,\n                    columnNumber: 23\n                  }, this), field.required && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"required-badge\",\n                    children: \"Required\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 931,\n                    columnNumber: 42\n                  }, this), field.conditional && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"conditional-badge\",\n                    children: \"Conditional\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 932,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 929,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 927,\n                columnNumber: 19\n              }, this)]\n            }, field.key, true, {\n              fileName: _jsxFileName,\n              lineNumber: 915,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 911,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 666,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 513,\n      columnNumber: 7\n    }, this), showFieldConfig && configField && /*#__PURE__*/_jsxDEV(FieldConfigModal, {\n      field: configField,\n      onSave: handleFieldConfigSave,\n      onCancel: () => {\n        setShowFieldConfig(false);\n        setConfigField(null);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 944,\n      columnNumber: 9\n    }, this), showPreview && /*#__PURE__*/_jsxDEV(FormPreview, {\n      fields: selectedFields,\n      formName: formName,\n      onClose: () => setShowPreview(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 955,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 465,\n    columnNumber: 5\n  }, this);\n};\n_s(FormBuilder, \"jfce3Lbxi6yhjacIq5CYJDbtBJk=\");\n_c = FormBuilder;\nexport default FormBuilder;\nvar _c;\n$RefreshReg$(_c, \"FormBuilder\");", "map": {"version": 3, "names": ["useState", "useEffect", "PersonFieldDefinitions", "<PERSON><PERSON><PERSON><PERSON><PERSON>F<PERSON>s", "formConfigService", "apiService", "FieldConfigModal", "FormPreview", "jsxDEV", "_jsxDEV", "FormBuilder", "onSave", "onCancel", "initialConfig", "_s", "selectedHierarchy", "setSelectedHierarchy", "formName", "setFormName", "formDescription", "setFormDescription", "availableFields", "setAvailableFields", "<PERSON><PERSON><PERSON>s", "setSelectedFields", "currentSection", "setCurrentSection", "searchTerm", "setSearchTerm", "showPreview", "setShowPreview", "showFieldConfig", "setShowFieldConfig", "config<PERSON><PERSON>", "setConfigField", "errors", "setErrors", "saving", "setSaving", "savedForms", "setSavedForms", "showSavedForms", "setShowSavedForms", "divisions", "setDivisions", "categories", "setCategories", "subCategories", "setSubCategories", "loading", "setLoading", "existingFormInfo", "setExistingFormInfo", "isEditMode", "setIsEditMode", "editingFormId", "setEditingFormId", "initializeFields", "loadSavedForms", "loadInitialConfig", "allFields", "deduplicateFields", "fields", "seen", "Set", "deduplicated", "for<PERSON>ach", "field", "has", "key", "add", "push", "forms", "getAllFormConfigs", "config", "name", "description", "deduplicatedFields", "id", "type", "associatedId", "divisionId", "categoryId", "subCategoryId", "hierarchy", "loadDivisions", "formValidation", "validateFormCreation", "<PERSON><PERSON><PERSON><PERSON>", "prev", "formCreation", "join", "newErrors", "formInfo", "getExistingFormInfo", "response", "getDivisions", "data", "error", "console", "loadCategories", "getCategoriesByDivision", "loadSubCategories", "getSubCategoriesByCategory", "handleDivisionChange", "e", "target", "value", "division", "find", "d", "parseInt", "category", "subCategory", "handleCategoryChange", "c", "handleSubCategoryChange", "sc", "handleFieldToggle", "isSelected", "some", "f", "filter", "exists", "handleFieldConfig", "handleFieldConfigSave", "updatedField", "map", "handleFieldMove", "fromIndex", "toIndex", "new<PERSON>ields", "movedField", "splice", "handleSave", "validation", "validateForm", "completeHierarchy", "settings", "showSections", "allowConditionalFields", "validateOnChange", "savedConfig", "originalForm", "createdAt", "updatedAt", "Date", "toISOString", "saveFormConfig", "Error", "message", "alert", "general", "trim", "length", "requiredFields", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "missingRequired", "rf", "includes", "Object", "keys", "getFilteredFields", "filtered", "section", "label", "toLowerCase", "getSectionCounts", "counts", "all", "sectionKey", "sectionCounts", "filteredFields", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "entries", "form", "summary", "fieldCount", "toLocaleDateString", "window", "confirm", "deleteFormConfig", "onChange", "placeholder", "rows", "index", "required", "title", "Math", "max", "min", "style", "backgroundColor", "border", "borderRadius", "padding", "margin", "color", "fontSize", "marginBottom", "paddingLeft", "display", "gridTemplateColumns", "gap", "fontWeight", "width", "marginTop", "existingForms", "subCategoriesWithForms", "checked", "conditional", "onClose", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/components/forms/FormBuilder.js"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { PersonFieldDefinitions, getAllPersonFields } from '../../constants/personConstants';\nimport formConfigService from '../../services/formConfigService';\nimport apiService from '../../services/apiService';\nimport FieldConfigModal from './FieldConfigModal';\nimport FormPreview from './FormPreview';\nimport './FormBuilder.css';\n\nconst FormBuilder = ({ onSave, onCancel, initialConfig = null }) => {\n  const [selectedHierarchy, setSelectedHierarchy] = useState({});\n  const [formName, setFormName] = useState('');\n  const [formDescription, setFormDescription] = useState('');\n  const [availableFields, setAvailableFields] = useState([]);\n  const [selectedFields, setSelectedFields] = useState([]);\n  const [currentSection, setCurrentSection] = useState('all');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showPreview, setShowPreview] = useState(false);\n  const [showFieldConfig, setShowFieldConfig] = useState(false);\n  const [configField, setConfigField] = useState(null);\n  const [errors, setErrors] = useState({});\n  const [saving, setSaving] = useState(false);\n  const [savedForms, setSavedForms] = useState([]);\n  const [showSavedForms, setShowSavedForms] = useState(false);\n\n  // State for dropdown data\n  const [divisions, setDivisions] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [subCategories, setSubCategories] = useState([]);\n  const [loading, setLoading] = useState({\n    divisions: false,\n    categories: false,\n    subCategories: false\n  });\n  const [existingFormInfo, setExistingFormInfo] = useState(null);\n  const [isEditMode, setIsEditMode] = useState(false);\n  const [editingFormId, setEditingFormId] = useState(null);\n\n  useEffect(() => {\n    initializeFields();\n    loadSavedForms();\n    if (initialConfig) {\n      loadInitialConfig(initialConfig);\n    }\n  }, [initialConfig]);\n\n  const initializeFields = () => {\n    const allFields = getAllPersonFields();\n    setAvailableFields(allFields);\n  };\n\n  // Deduplicate fields based on field key\n  const deduplicateFields = (fields) => {\n    const seen = new Set();\n    const deduplicated = [];\n\n    fields.forEach(field => {\n      if (!seen.has(field.key)) {\n        seen.add(field.key);\n        deduplicated.push(field);\n      }\n    });\n\n    return deduplicated;\n  };\n\n  const loadSavedForms = () => {\n    const forms = formConfigService.getAllFormConfigs();\n    setSavedForms(forms);\n  };\n\n  const loadInitialConfig = (config) => {\n    setFormName(config.name || '');\n    setFormDescription(config.description || '');\n\n    // Deduplicate fields when loading initial config\n    const fields = config.fields || [];\n    const deduplicatedFields = deduplicateFields(fields);\n    setSelectedFields(deduplicatedFields);\n\n    // Set edit mode when loading existing form\n    setIsEditMode(true);\n    setEditingFormId(config.id || config.key);\n\n    if (config.type === 'division' && config.associatedId) {\n      // Load division info for hierarchy selector\n      setSelectedHierarchy({\n        divisionId: config.associatedId\n      });\n    } else if (config.type === 'category' && config.associatedId) {\n      // Load category info for hierarchy selector\n      setSelectedHierarchy({\n        categoryId: config.associatedId\n      });\n    } else if (config.type === 'subcategory' && config.associatedId) {\n      // Load subcategory info for hierarchy selector\n      setSelectedHierarchy({\n        subCategoryId: config.associatedId\n      });\n    }\n\n    // If hierarchy is stored in config, use that\n    if (config.hierarchy) {\n      setSelectedHierarchy(config.hierarchy);\n    }\n  };\n\n  // Load divisions on component mount\n  useEffect(() => {\n    loadDivisions();\n  }, []);\n\n  // Validate form creation rules when hierarchy changes\n  useEffect(() => {\n    if (selectedHierarchy.categoryId) {\n      // Skip validation in edit mode since we're updating an existing form\n      if (!isEditMode) {\n        const formValidation = formConfigService.validateFormCreation(\n          selectedHierarchy.categoryId,\n          selectedHierarchy.subCategoryId\n        );\n\n        if (!formValidation.isValid) {\n          setErrors(prev => ({\n            ...prev,\n            formCreation: formValidation.errors.join('. ')\n          }));\n        } else {\n          setErrors(prev => {\n            const newErrors = { ...prev };\n            delete newErrors.formCreation;\n            return newErrors;\n          });\n        }\n      } else {\n        // In edit mode, clear any form creation errors\n        setErrors(prev => {\n          const newErrors = { ...prev };\n          delete newErrors.formCreation;\n          return newErrors;\n        });\n      }\n\n      const formInfo = formConfigService.getExistingFormInfo(\n        selectedHierarchy.categoryId,\n        selectedHierarchy.subCategoryId\n      );\n      setExistingFormInfo(formInfo);\n    } else {\n      setExistingFormInfo(null);\n      setErrors(prev => {\n        const newErrors = { ...prev };\n        delete newErrors.formCreation;\n        return newErrors;\n      });\n    }\n  }, [selectedHierarchy.categoryId, selectedHierarchy.subCategoryId, isEditMode]);\n\n  const loadDivisions = async () => {\n    setLoading(prev => ({ ...prev, divisions: true }));\n    try {\n      const response = await apiService.getDivisions();\n      setDivisions(response.data || []);\n    } catch (error) {\n      console.error('Error loading divisions:', error);\n    } finally {\n      setLoading(prev => ({ ...prev, divisions: false }));\n    }\n  };\n\n  const loadCategories = async (divisionId) => {\n    if (!divisionId) {\n      setCategories([]);\n      setSubCategories([]);\n      return;\n    }\n\n    setLoading(prev => ({ ...prev, categories: true }));\n    try {\n      const response = await apiService.getCategoriesByDivision(divisionId);\n      setCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading categories:', error);\n      setCategories([]);\n    } finally {\n      setLoading(prev => ({ ...prev, categories: false }));\n    }\n  };\n\n  const loadSubCategories = async (categoryId) => {\n    if (!categoryId) {\n      setSubCategories([]);\n      return;\n    }\n\n    setLoading(prev => ({ ...prev, subCategories: true }));\n    try {\n      const response = await apiService.getSubCategoriesByCategory(categoryId);\n      setSubCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading subcategories:', error);\n      setSubCategories([]);\n    } finally {\n      setLoading(prev => ({ ...prev, subCategories: false }));\n    }\n  };\n\n  const handleDivisionChange = (e) => {\n    const divisionId = e.target.value;\n    const division = divisions.find(d => d.id === parseInt(divisionId));\n\n    setSelectedHierarchy({\n      divisionId: divisionId || null,\n      categoryId: null,\n      subCategoryId: null,\n      division: division || null,\n      category: null,\n      subCategory: null\n    });\n\n    setCategories([]);\n    setSubCategories([]);\n\n    if (divisionId) {\n      loadCategories(divisionId);\n    }\n  };\n\n  const handleCategoryChange = (e) => {\n    const categoryId = e.target.value;\n    const category = categories.find(c => c.id === parseInt(categoryId));\n\n    setSelectedHierarchy(prev => ({\n      ...prev,\n      categoryId: categoryId || null,\n      subCategoryId: null,\n      category: category || null,\n      subCategory: null\n    }));\n\n    setSubCategories([]);\n\n    if (categoryId) {\n      loadSubCategories(categoryId);\n    }\n  };\n\n  const handleSubCategoryChange = (e) => {\n    const subCategoryId = e.target.value;\n    const subCategory = subCategories.find(sc => sc.id === parseInt(subCategoryId));\n\n    setSelectedHierarchy(prev => ({\n      ...prev,\n      subCategoryId: subCategoryId || null,\n      subCategory: subCategory || null\n    }));\n  };\n\n  const handleFieldToggle = (field) => {\n    const isSelected = selectedFields.some(f => f.key === field.key);\n\n    if (isSelected) {\n      // Remove all instances of this field key (in case there are duplicates)\n      setSelectedFields(prev => prev.filter(f => f.key !== field.key));\n    } else {\n      // Add field only if it doesn't already exist\n      setSelectedFields(prev => {\n        const exists = prev.some(f => f.key === field.key);\n        if (exists) {\n          return prev;\n        }\n        return [...prev, { ...field }];\n      });\n    }\n  };\n\n  const handleFieldConfig = (field) => {\n    setConfigField(field);\n    setShowFieldConfig(true);\n  };\n\n  const handleFieldConfigSave = (updatedField) => {\n    setSelectedFields(prev => \n      prev.map(f => f.key === updatedField.key ? updatedField : f)\n    );\n    setShowFieldConfig(false);\n    setConfigField(null);\n  };\n\n  const handleFieldMove = (fromIndex, toIndex) => {\n    const newFields = [...selectedFields];\n    const [movedField] = newFields.splice(fromIndex, 1);\n    newFields.splice(toIndex, 0, movedField);\n    setSelectedFields(newFields);\n  };\n\n  const handleSave = async () => {\n    const validation = validateForm();\n\n    if (!validation.isValid) {\n      setErrors(validation.errors);\n      return;\n    }\n\n    setSaving(true);\n    try {\n      // Ensure complete hierarchy information is included\n      const completeHierarchy = {\n        ...selectedHierarchy,\n        // Include division info (required)\n        divisionId: selectedHierarchy.divisionId,\n        division: selectedHierarchy.division,\n        // Include category info (required)\n        categoryId: selectedHierarchy.categoryId,\n        category: selectedHierarchy.category,\n        // Include subcategory info if selected (optional)\n        subCategoryId: selectedHierarchy.subCategoryId || null,\n        subCategory: selectedHierarchy.subCategory || null\n      };\n\n      const config = {\n        name: formName,\n        description: formDescription,\n        fields: selectedFields,\n        hierarchy: completeHierarchy,\n        settings: {\n          showSections: true,\n          allowConditionalFields: true,\n          validateOnChange: true\n        }\n      };\n\n      let savedConfig;\n\n      if (isEditMode) {\n        // In edit mode, update the existing form\n        // Preserve the original type and associatedId from the form being edited\n        const originalForm = formConfigService.getAllFormConfigs().find(f => f.id === editingFormId || f.key === editingFormId);\n        if (originalForm) {\n          // Update the existing form with new data\n          config.id = originalForm.id;\n          config.key = originalForm.key;\n          config.type = originalForm.type;\n          config.associatedId = originalForm.associatedId;\n          config.createdAt = originalForm.createdAt; // Preserve creation date\n          config.updatedAt = new Date().toISOString(); // Update modification date\n\n          savedConfig = formConfigService.saveFormConfig(originalForm.type, originalForm.associatedId, config);\n        } else {\n          throw new Error('Original form not found for editing');\n        }\n      } else {\n        // Create new form with the most specific level selected, but include complete hierarchy\n        if (selectedHierarchy.subCategoryId) {\n          savedConfig = formConfigService.saveFormConfig('subcategory', selectedHierarchy.subCategoryId, config);\n        } else if (selectedHierarchy.categoryId) {\n          savedConfig = formConfigService.saveFormConfig('category', selectedHierarchy.categoryId, config);\n        } else {\n          throw new Error('Please select both division and category');\n        }\n      }\n\n      // Reload saved forms list\n      loadSavedForms();\n\n      if (onSave) {\n        onSave(savedConfig);\n      } else {\n        // Show success message for standalone usage\n        const message = isEditMode ? 'Form updated successfully!' : 'Form configuration saved successfully!';\n        alert(message);\n        // Reset form\n        setFormName('');\n        setFormDescription('');\n        setSelectedFields([]);\n        setSelectedHierarchy({});\n        setIsEditMode(false);\n        setEditingFormId(null);\n      }\n    } catch (error) {\n      console.error('Error saving form:', error);\n      setErrors({ general: error.message });\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  const validateForm = () => {\n    const errors = {};\n\n    if (!formName.trim()) {\n      errors.formName = 'Form name is required';\n    }\n\n    // Division and category are required, subcategory is optional\n    if (!selectedHierarchy.divisionId) {\n      errors.hierarchy = 'Please select a division';\n    } else if (!selectedHierarchy.categoryId) {\n      errors.hierarchy = 'Please select a category';\n    } else if (selectedHierarchy.subCategoryId && !selectedHierarchy.categoryId) {\n      errors.hierarchy = 'Cannot select subcategory without selecting category';\n    }\n\n    // Validate form creation rules\n    if (selectedHierarchy.categoryId) {\n      const formValidation = formConfigService.validateFormCreation(\n        selectedHierarchy.categoryId,\n        selectedHierarchy.subCategoryId\n      );\n\n      if (!formValidation.isValid) {\n        errors.formCreation = formValidation.errors.join('. ');\n      }\n    }\n\n    if (selectedFields.length === 0) {\n      errors.fields = 'Please select at least one field';\n    }\n\n    // Check required fields\n    const requiredFields = ['name', 'mobileNumber', 'nature'];\n    const selectedFieldKeys = selectedFields.map(f => f.key);\n    const missingRequired = requiredFields.filter(rf => !selectedFieldKeys.includes(rf));\n\n    if (missingRequired.length > 0) {\n      errors.requiredFields = `Required fields missing: ${missingRequired.join(', ')}`;\n    }\n\n    return {\n      isValid: Object.keys(errors).length === 0,\n      errors\n    };\n  };\n\n  const getFilteredFields = () => {\n    let filtered = availableFields;\n\n    // Filter by section\n    if (currentSection !== 'all') {\n      filtered = filtered.filter(field => field.section === currentSection);\n    }\n\n    // Filter by search term\n    if (searchTerm) {\n      filtered = filtered.filter(field => \n        field.label.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        field.key.toLowerCase().includes(searchTerm.toLowerCase())\n      );\n    }\n\n    return filtered;\n  };\n\n  const getSectionCounts = () => {\n    const counts = { all: availableFields.length };\n    Object.keys(PersonFieldDefinitions).forEach(sectionKey => {\n      counts[sectionKey] = availableFields.filter(f => f.section === sectionKey).length;\n    });\n    return counts;\n  };\n\n  const sectionCounts = getSectionCounts();\n  const filteredFields = getFilteredFields();\n\n  return (\n    <div className=\"form-builder\">\n      <div className=\"form-builder-header\">\n        <h2>Form Builder</h2>\n        <div className=\"header-actions\">\n          <button \n            type=\"button\" \n            onClick={() => setShowPreview(true)}\n            className=\"btn btn-secondary\"\n            disabled={selectedFields.length === 0}\n          >\n            Preview Form\n          </button>\n          <button\n            type=\"button\"\n            onClick={handleSave}\n            className=\"btn btn-primary\"\n            disabled={saving || errors.formCreation}\n          >\n            {saving ? (isEditMode ? 'Updating...' : 'Saving...') : (isEditMode ? 'Update Form' : 'Save Form')}\n          </button>\n          {onCancel && (\n            <button\n              type=\"button\"\n              onClick={onCancel}\n              className=\"btn btn-outline\"\n            >\n              Cancel\n            </button>\n          )}\n        </div>\n      </div>\n\n      {errors.general && (\n        <div className=\"alert alert-error\">{errors.general}</div>\n      )}\n\n      {/* Validation Status */}\n      {Object.keys(errors).length > 0 && (\n        <div className=\"validation-errors\">\n          <h4>Please fix the following issues:</h4>\n          <ul>\n            {Object.entries(errors).map(([key, message]) => (\n              <li key={key}>{message}</li>\n            ))}\n          </ul>\n        </div>\n      )}\n\n      <div className=\"form-builder-content\">\n        {/* Form Configuration Panel */}\n        <div className=\"config-panel\">\n          {/* Saved Forms Section */}\n          <div className=\"config-section\">\n            <div className=\"section-header\">\n              <h3>Saved Forms ({savedForms.length})</h3>\n              <button\n                type=\"button\"\n                className=\"btn-link\"\n                onClick={() => setShowSavedForms(!showSavedForms)}\n              >\n                {showSavedForms ? 'Hide' : 'Show'}\n              </button>\n            </div>\n\n            {showSavedForms && (\n              <div className=\"saved-forms-list\">\n                {savedForms.length === 0 ? (\n                  <p className=\"no-forms-message\">No saved forms yet. Create your first form below!</p>\n                ) : (\n                  savedForms.map((form) => (\n                    <div key={form.key} className=\"saved-form-item\">\n                      <div className=\"form-item-header\">\n                        <h4>{form.name}</h4>\n                        <span className=\"form-type-badge\">{form.type}</span>\n                      </div>\n                      <p className=\"form-description\">{form.description || 'No description'}</p>\n                      <div className=\"form-meta\">\n                        <span>{form.summary.fieldCount} fields</span>\n                        <span>•</span>\n                        <span>Updated {new Date(form.updatedAt).toLocaleDateString()}</span>\n                      </div>\n                      <div className=\"form-actions\">\n                        <button\n                          type=\"button\"\n                          className=\"btn-small btn-outline\"\n                          onClick={() => loadInitialConfig(form)}\n                        >\n                          Load\n                        </button>\n                        <button\n                          type=\"button\"\n                          className=\"btn-small btn-danger\"\n                          onClick={() => {\n                            if (window.confirm('Are you sure you want to delete this form?')) {\n                              formConfigService.deleteFormConfig(form.type, form.associatedId);\n                              loadSavedForms();\n                            }\n                          }}\n                        >\n                          Delete\n                        </button>\n                      </div>\n                    </div>\n                  ))\n                )}\n              </div>\n            )}\n          </div>\n\n          {/* Form Configuration Section */}\n          <div className=\"config-section\">\n            <h3>Form Configuration</h3>\n\n            <div className=\"form-group\">\n              <label>Form Name *</label>\n              <input\n                type=\"text\"\n                value={formName}\n                onChange={(e) => setFormName(e.target.value)}\n                placeholder=\"Enter form name\"\n                className={errors.formName ? 'error' : ''}\n              />\n              {errors.formName && <div className=\"error-message\">{errors.formName}</div>}\n            </div>\n\n            <div className=\"form-group\">\n              <label>Description</label>\n              <textarea\n                value={formDescription}\n                onChange={(e) => setFormDescription(e.target.value)}\n                placeholder=\"Enter form description\"\n                rows=\"3\"\n              />\n            </div>\n\n\n          </div>\n\n          {/* Selected Fields Panel */}\n          <div className=\"config-section\">\n            <h3>Selected Fields ({selectedFields.length})</h3>\n            {errors.fields && <div className=\"error-message\">{errors.fields}</div>}\n            {errors.requiredFields && <div className=\"error-message\">{errors.requiredFields}</div>}\n            \n            <div className=\"selected-fields\">\n              {selectedFields.length === 0 ? (\n                <div className=\"empty-state\">\n                  No fields selected. Choose fields from the available fields panel.\n                </div>\n              ) : (\n                selectedFields.map((field, index) => (\n                  <div key={field.key} className=\"selected-field\">\n                    <div className=\"field-info\">\n                      <span className=\"field-label\">{field.label}</span>\n                      <span className=\"field-type\">{field.type}</span>\n                      {field.required && <span className=\"required-badge\">Required</span>}\n                    </div>\n                    <div className=\"field-actions\">\n                      <button\n                        type=\"button\"\n                        onClick={() => handleFieldConfig(field)}\n                        className=\"btn-icon\"\n                        title=\"Configure field\"\n                      >\n                        ⚙️\n                      </button>\n                      <button\n                        type=\"button\"\n                        onClick={() => handleFieldMove(index, Math.max(0, index - 1))}\n                        className=\"btn-icon\"\n                        disabled={index === 0}\n                        title=\"Move up\"\n                      >\n                        ↑\n                      </button>\n                      <button\n                        type=\"button\"\n                        onClick={() => handleFieldMove(index, Math.min(selectedFields.length - 1, index + 1))}\n                        className=\"btn-icon\"\n                        disabled={index === selectedFields.length - 1}\n                        title=\"Move down\"\n                      >\n                        ↓\n                      </button>\n                      <button\n                        type=\"button\"\n                        onClick={() => handleFieldToggle(field)}\n                        className=\"btn-icon remove\"\n                        title=\"Remove field\"\n                      >\n                        ✕\n                      </button>\n                    </div>\n                  </div>\n                ))\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* Available Fields Panel */}\n        <div className=\"fields-panel\">\n          <div className=\"fields-header\">\n            <h3>Available Fields</h3>\n          </div>\n\n          {/* Division/Category/SubCategory Selection */}\n          <div className=\"hierarchy-selection\" style={{\n            backgroundColor: '#f8f9fa',\n            border: '1px solid #e1e5e9',\n            borderRadius: '8px',\n            padding: '1.5rem',\n            margin: '1rem 0'\n          }}>\n            <h4 style={{ margin: '0 0 1rem 0', color: '#495057' }}>\n              Associate with Division & Category (Required) / SubCategory (Optional) *\n            </h4>\n            <div style={{\n              fontSize: '0.875rem',\n              color: '#6c757d',\n              marginBottom: '1rem',\n              padding: '0.75rem',\n              backgroundColor: '#f8f9fa',\n              borderRadius: '4px',\n              border: '1px solid #e9ecef'\n            }}>\n              <strong>Form Creation Rules:</strong>\n              <ul style={{ margin: '0.5rem 0 0 1rem', paddingLeft: '1rem' }}>\n                <li>If you create a form for a category, you cannot create forms for its subcategories</li>\n                <li>Each subcategory can have only one form</li>\n                <li>If subcategories already have forms, you cannot create a form for the parent category</li>\n              </ul>\n            </div>\n\n            <div className=\"hierarchy-dropdowns\" style={{\n              display: 'grid',\n              gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n              gap: '1rem'\n            }}>\n              {/* Division Dropdown */}\n              <div className=\"form-group\">\n                <label style={{\n                  display: 'block',\n                  marginBottom: '0.5rem',\n                  fontWeight: '500',\n                  color: '#495057'\n                }}>\n                  Division *\n                </label>\n                <select\n                  value={selectedHierarchy.divisionId || ''}\n                  onChange={handleDivisionChange}\n                  disabled={loading.divisions}\n                  className={errors.divisionId ? 'error' : ''}\n                  style={{\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #ced4da',\n                    borderRadius: '4px',\n                    fontSize: '1rem',\n                    backgroundColor: 'white'\n                  }}\n                >\n                  <option value=\"\">Select Division</option>\n                  {divisions.map(division => (\n                    <option key={division.id} value={division.id}>\n                      {division.name}\n                    </option>\n                  ))}\n                </select>\n                {errors.divisionId && (\n                  <div style={{ color: '#dc3545', fontSize: '0.875rem', marginTop: '0.25rem' }}>\n                    {errors.divisionId}\n                  </div>\n                )}\n              </div>\n\n              {/* Category Dropdown */}\n              <div className=\"form-group\">\n                <label style={{\n                  display: 'block',\n                  marginBottom: '0.5rem',\n                  fontWeight: '500',\n                  color: '#495057'\n                }}>\n                  Category *\n                </label>\n                <select\n                  value={selectedHierarchy.categoryId || ''}\n                  onChange={handleCategoryChange}\n                  disabled={!selectedHierarchy.divisionId || loading.categories}\n                  className={errors.categoryId ? 'error' : ''}\n                  style={{\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #ced4da',\n                    borderRadius: '4px',\n                    fontSize: '1rem',\n                    backgroundColor: 'white'\n                  }}\n                >\n                  <option value=\"\">\n                    {!selectedHierarchy.divisionId ? 'Select Division first' : 'Select Category'}\n                  </option>\n                  {categories.map(category => (\n                    <option key={category.id} value={category.id}>\n                      {category.name}\n                    </option>\n                  ))}\n                </select>\n                {errors.categoryId && (\n                  <div style={{ color: '#dc3545', fontSize: '0.875rem', marginTop: '0.25rem' }}>\n                    {errors.categoryId}\n                  </div>\n                )}\n              </div>\n\n              {/* SubCategory Dropdown */}\n              <div className=\"form-group\">\n                <label style={{\n                  display: 'block',\n                  marginBottom: '0.5rem',\n                  fontWeight: '500',\n                  color: '#495057'\n                }}>\n                  SubCategory (Optional)\n                </label>\n                <select\n                  value={selectedHierarchy.subCategoryId || ''}\n                  onChange={handleSubCategoryChange}\n                  disabled={!selectedHierarchy.categoryId || loading.subCategories}\n                  style={{\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #ced4da',\n                    borderRadius: '4px',\n                    fontSize: '1rem',\n                    backgroundColor: 'white'\n                  }}\n                >\n                  <option value=\"\">\n                    {!selectedHierarchy.categoryId ? 'Select Category first' : 'Select SubCategory (Optional)'}\n                  </option>\n                  {subCategories.map(subCategory => (\n                    <option key={subCategory.id} value={subCategory.id}>\n                      {subCategory.name}\n                    </option>\n                  ))}\n                </select>\n              </div>\n            </div>\n\n            {errors.hierarchy && (\n              <div style={{ color: '#dc3545', fontSize: '0.875rem', marginTop: '0.5rem' }}>\n                {errors.hierarchy}\n              </div>\n            )}\n            {errors.formCreation && (\n              <div style={{ color: '#dc3545', fontSize: '0.875rem', marginTop: '0.5rem', fontWeight: 'bold' }}>\n                ⚠️ {errors.formCreation}\n              </div>\n            )}\n\n            {/* Existing Form Information */}\n            {existingFormInfo && existingFormInfo.existingForms.length > 0 && (\n              <div style={{\n                marginTop: '1rem',\n                padding: '0.75rem',\n                backgroundColor: '#fff3cd',\n                border: '1px solid #ffeaa7',\n                borderRadius: '4px',\n                fontSize: '0.875rem'\n              }}>\n                <strong>📋 Existing Forms:</strong>\n                {existingFormInfo.existingForms.map((form, index) => (\n                  <div key={index} style={{ marginTop: '0.5rem' }}>\n                    <strong>{form.type === 'category' ? 'Category' : 'SubCategory'} Form:</strong> {form.name}\n                    {form.description && <div style={{ color: '#6c757d' }}>{form.description}</div>}\n                  </div>\n                ))}\n              </div>\n            )}\n\n            {/* Subcategories with Forms Warning */}\n            {existingFormInfo && existingFormInfo.subCategoriesWithForms.length > 0 && !selectedHierarchy.subCategoryId && (\n              <div style={{\n                marginTop: '1rem',\n                padding: '0.75rem',\n                backgroundColor: '#f8d7da',\n                border: '1px solid #f5c6cb',\n                borderRadius: '4px',\n                fontSize: '0.875rem'\n              }}>\n                <strong>⚠️ Warning:</strong> This category has {existingFormInfo.subCategoriesWithForms.length} subcategory form(s).\n                You cannot create a form for this category.\n              </div>\n            )}\n\n            {/* Success Message for Valid Selection */}\n            {selectedHierarchy.categoryId && !errors.formCreation && (\n              <div style={{\n                marginTop: '1rem',\n                padding: '0.75rem',\n                backgroundColor: '#d4edda',\n                border: '1px solid #c3e6cb',\n                borderRadius: '4px',\n                fontSize: '0.875rem'\n              }}>\n                <strong>✅ Valid Selection:</strong> You can create a form for this {selectedHierarchy.subCategoryId ? 'subcategory' : 'category'}.\n              </div>\n            )}\n          </div>\n\n          {/* Search and Filter Controls */}\n          <div className=\"fields-controls\">\n            <input\n              type=\"text\"\n              placeholder=\"Search fields...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"search-input\"\n            />\n          </div>\n\n          {/* Section Tabs */}\n          <div className=\"section-tabs\">\n            <button\n              type=\"button\"\n              onClick={() => setCurrentSection('all')}\n              className={`section-tab ${currentSection === 'all' ? 'active' : ''}`}\n            >\n              All ({sectionCounts.all})\n            </button>\n            {Object.entries(PersonFieldDefinitions).map(([sectionKey, section]) => (\n              <button\n                key={sectionKey}\n                type=\"button\"\n                onClick={() => setCurrentSection(sectionKey)}\n                className={`section-tab ${currentSection === sectionKey ? 'active' : ''}`}\n              >\n                {section.title} ({sectionCounts[sectionKey]})\n              </button>\n            ))}\n          </div>\n\n          {/* Fields List */}\n          <div className=\"fields-list\">\n            {filteredFields.map(field => {\n              const isSelected = selectedFields.some(f => f.key === field.key);\n              return (\n                <div\n                  key={field.key}\n                  className={`field-item ${isSelected ? 'selected' : ''}`}\n                  onClick={() => handleFieldToggle(field)}\n                >\n                  <div className=\"field-checkbox\">\n                    <input\n                      type=\"checkbox\"\n                      checked={isSelected}\n                      onChange={() => handleFieldToggle(field)}\n                    />\n                  </div>\n                  <div className=\"field-details\">\n                    <div className=\"field-name\">{field.label}</div>\n                    <div className=\"field-meta\">\n                      <span className=\"field-type\">{field.type}</span>\n                      {field.required && <span className=\"required-badge\">Required</span>}\n                      {field.conditional && <span className=\"conditional-badge\">Conditional</span>}\n                    </div>\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n        </div>\n      </div>\n\n      {/* Modals */}\n      {showFieldConfig && configField && (\n        <FieldConfigModal\n          field={configField}\n          onSave={handleFieldConfigSave}\n          onCancel={() => {\n            setShowFieldConfig(false);\n            setConfigField(null);\n          }}\n        />\n      )}\n\n      {showPreview && (\n        <FormPreview\n          fields={selectedFields}\n          formName={formName}\n          onClose={() => setShowPreview(false)}\n        />\n      )}\n    </div>\n  );\n};\n\nexport default FormBuilder;\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,sBAAsB,EAAEC,kBAAkB,QAAQ,iCAAiC;AAC5F,OAAOC,iBAAiB,MAAM,kCAAkC;AAChE,OAAOC,UAAU,MAAM,2BAA2B;AAClD,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAMC,WAAW,GAAGA,CAAC;EAAEC,MAAM;EAAEC,QAAQ;EAAEC,aAAa,GAAG;AAAK,CAAC,KAAK;EAAAC,EAAA;EAClE,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC9D,MAAM,CAACiB,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACmB,eAAe,EAAEC,kBAAkB,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACqB,eAAe,EAAEC,kBAAkB,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACuB,cAAc,EAAEC,iBAAiB,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACyB,cAAc,EAAEC,iBAAiB,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC+B,eAAe,EAAEC,kBAAkB,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACiC,WAAW,EAAEC,cAAc,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACmC,MAAM,EAAEC,SAAS,CAAC,GAAGpC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAACqC,MAAM,EAAEC,SAAS,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACuC,UAAU,EAAEC,aAAa,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACyC,cAAc,EAAEC,iBAAiB,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;;EAE3D;EACA,MAAM,CAAC2C,SAAS,EAAEC,YAAY,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC6C,UAAU,EAAEC,aAAa,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC+C,aAAa,EAAEC,gBAAgB,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACiD,OAAO,EAAEC,UAAU,CAAC,GAAGlD,QAAQ,CAAC;IACrC2C,SAAS,EAAE,KAAK;IAChBE,UAAU,EAAE,KAAK;IACjBE,aAAa,EAAE;EACjB,CAAC,CAAC;EACF,MAAM,CAACI,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpD,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACqD,UAAU,EAAEC,aAAa,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACuD,aAAa,EAAEC,gBAAgB,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;EAExDC,SAAS,CAAC,MAAM;IACdwD,gBAAgB,CAAC,CAAC;IAClBC,cAAc,CAAC,CAAC;IAChB,IAAI7C,aAAa,EAAE;MACjB8C,iBAAiB,CAAC9C,aAAa,CAAC;IAClC;EACF,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;EAEnB,MAAM4C,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMG,SAAS,GAAGzD,kBAAkB,CAAC,CAAC;IACtCmB,kBAAkB,CAACsC,SAAS,CAAC;EAC/B,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAIC,MAAM,IAAK;IACpC,MAAMC,IAAI,GAAG,IAAIC,GAAG,CAAC,CAAC;IACtB,MAAMC,YAAY,GAAG,EAAE;IAEvBH,MAAM,CAACI,OAAO,CAACC,KAAK,IAAI;MACtB,IAAI,CAACJ,IAAI,CAACK,GAAG,CAACD,KAAK,CAACE,GAAG,CAAC,EAAE;QACxBN,IAAI,CAACO,GAAG,CAACH,KAAK,CAACE,GAAG,CAAC;QACnBJ,YAAY,CAACM,IAAI,CAACJ,KAAK,CAAC;MAC1B;IACF,CAAC,CAAC;IAEF,OAAOF,YAAY;EACrB,CAAC;EAED,MAAMP,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMc,KAAK,GAAGpE,iBAAiB,CAACqE,iBAAiB,CAAC,CAAC;IACnDjC,aAAa,CAACgC,KAAK,CAAC;EACtB,CAAC;EAED,MAAMb,iBAAiB,GAAIe,MAAM,IAAK;IACpCxD,WAAW,CAACwD,MAAM,CAACC,IAAI,IAAI,EAAE,CAAC;IAC9BvD,kBAAkB,CAACsD,MAAM,CAACE,WAAW,IAAI,EAAE,CAAC;;IAE5C;IACA,MAAMd,MAAM,GAAGY,MAAM,CAACZ,MAAM,IAAI,EAAE;IAClC,MAAMe,kBAAkB,GAAGhB,iBAAiB,CAACC,MAAM,CAAC;IACpDtC,iBAAiB,CAACqD,kBAAkB,CAAC;;IAErC;IACAvB,aAAa,CAAC,IAAI,CAAC;IACnBE,gBAAgB,CAACkB,MAAM,CAACI,EAAE,IAAIJ,MAAM,CAACL,GAAG,CAAC;IAEzC,IAAIK,MAAM,CAACK,IAAI,KAAK,UAAU,IAAIL,MAAM,CAACM,YAAY,EAAE;MACrD;MACAhE,oBAAoB,CAAC;QACnBiE,UAAU,EAAEP,MAAM,CAACM;MACrB,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIN,MAAM,CAACK,IAAI,KAAK,UAAU,IAAIL,MAAM,CAACM,YAAY,EAAE;MAC5D;MACAhE,oBAAoB,CAAC;QACnBkE,UAAU,EAAER,MAAM,CAACM;MACrB,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIN,MAAM,CAACK,IAAI,KAAK,aAAa,IAAIL,MAAM,CAACM,YAAY,EAAE;MAC/D;MACAhE,oBAAoB,CAAC;QACnBmE,aAAa,EAAET,MAAM,CAACM;MACxB,CAAC,CAAC;IACJ;;IAEA;IACA,IAAIN,MAAM,CAACU,SAAS,EAAE;MACpBpE,oBAAoB,CAAC0D,MAAM,CAACU,SAAS,CAAC;IACxC;EACF,CAAC;;EAED;EACAnF,SAAS,CAAC,MAAM;IACdoF,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACApF,SAAS,CAAC,MAAM;IACd,IAAIc,iBAAiB,CAACmE,UAAU,EAAE;MAChC;MACA,IAAI,CAAC7B,UAAU,EAAE;QACf,MAAMiC,cAAc,GAAGlF,iBAAiB,CAACmF,oBAAoB,CAC3DxE,iBAAiB,CAACmE,UAAU,EAC5BnE,iBAAiB,CAACoE,aACpB,CAAC;QAED,IAAI,CAACG,cAAc,CAACE,OAAO,EAAE;UAC3BpD,SAAS,CAACqD,IAAI,KAAK;YACjB,GAAGA,IAAI;YACPC,YAAY,EAAEJ,cAAc,CAACnD,MAAM,CAACwD,IAAI,CAAC,IAAI;UAC/C,CAAC,CAAC,CAAC;QACL,CAAC,MAAM;UACLvD,SAAS,CAACqD,IAAI,IAAI;YAChB,MAAMG,SAAS,GAAG;cAAE,GAAGH;YAAK,CAAC;YAC7B,OAAOG,SAAS,CAACF,YAAY;YAC7B,OAAOE,SAAS;UAClB,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACL;QACAxD,SAAS,CAACqD,IAAI,IAAI;UAChB,MAAMG,SAAS,GAAG;YAAE,GAAGH;UAAK,CAAC;UAC7B,OAAOG,SAAS,CAACF,YAAY;UAC7B,OAAOE,SAAS;QAClB,CAAC,CAAC;MACJ;MAEA,MAAMC,QAAQ,GAAGzF,iBAAiB,CAAC0F,mBAAmB,CACpD/E,iBAAiB,CAACmE,UAAU,EAC5BnE,iBAAiB,CAACoE,aACpB,CAAC;MACD/B,mBAAmB,CAACyC,QAAQ,CAAC;IAC/B,CAAC,MAAM;MACLzC,mBAAmB,CAAC,IAAI,CAAC;MACzBhB,SAAS,CAACqD,IAAI,IAAI;QAChB,MAAMG,SAAS,GAAG;UAAE,GAAGH;QAAK,CAAC;QAC7B,OAAOG,SAAS,CAACF,YAAY;QAC7B,OAAOE,SAAS;MAClB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAAC7E,iBAAiB,CAACmE,UAAU,EAAEnE,iBAAiB,CAACoE,aAAa,EAAE9B,UAAU,CAAC,CAAC;EAE/E,MAAMgC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChCnC,UAAU,CAACuC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE9C,SAAS,EAAE;IAAK,CAAC,CAAC,CAAC;IAClD,IAAI;MACF,MAAMoD,QAAQ,GAAG,MAAM1F,UAAU,CAAC2F,YAAY,CAAC,CAAC;MAChDpD,YAAY,CAACmD,QAAQ,CAACE,IAAI,IAAI,EAAE,CAAC;IACnC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAClD,CAAC,SAAS;MACRhD,UAAU,CAACuC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE9C,SAAS,EAAE;MAAM,CAAC,CAAC,CAAC;IACrD;EACF,CAAC;EAED,MAAMyD,cAAc,GAAG,MAAOnB,UAAU,IAAK;IAC3C,IAAI,CAACA,UAAU,EAAE;MACfnC,aAAa,CAAC,EAAE,CAAC;MACjBE,gBAAgB,CAAC,EAAE,CAAC;MACpB;IACF;IAEAE,UAAU,CAACuC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE5C,UAAU,EAAE;IAAK,CAAC,CAAC,CAAC;IACnD,IAAI;MACF,MAAMkD,QAAQ,GAAG,MAAM1F,UAAU,CAACgG,uBAAuB,CAACpB,UAAU,CAAC;MACrEnC,aAAa,CAACiD,QAAQ,CAACE,IAAI,IAAI,EAAE,CAAC;IACpC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDpD,aAAa,CAAC,EAAE,CAAC;IACnB,CAAC,SAAS;MACRI,UAAU,CAACuC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE5C,UAAU,EAAE;MAAM,CAAC,CAAC,CAAC;IACtD;EACF,CAAC;EAED,MAAMyD,iBAAiB,GAAG,MAAOpB,UAAU,IAAK;IAC9C,IAAI,CAACA,UAAU,EAAE;MACflC,gBAAgB,CAAC,EAAE,CAAC;MACpB;IACF;IAEAE,UAAU,CAACuC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE1C,aAAa,EAAE;IAAK,CAAC,CAAC,CAAC;IACtD,IAAI;MACF,MAAMgD,QAAQ,GAAG,MAAM1F,UAAU,CAACkG,0BAA0B,CAACrB,UAAU,CAAC;MACxElC,gBAAgB,CAAC+C,QAAQ,CAACE,IAAI,IAAI,EAAE,CAAC;IACvC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDlD,gBAAgB,CAAC,EAAE,CAAC;IACtB,CAAC,SAAS;MACRE,UAAU,CAACuC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE1C,aAAa,EAAE;MAAM,CAAC,CAAC,CAAC;IACzD;EACF,CAAC;EAED,MAAMyD,oBAAoB,GAAIC,CAAC,IAAK;IAClC,MAAMxB,UAAU,GAAGwB,CAAC,CAACC,MAAM,CAACC,KAAK;IACjC,MAAMC,QAAQ,GAAGjE,SAAS,CAACkE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAChC,EAAE,KAAKiC,QAAQ,CAAC9B,UAAU,CAAC,CAAC;IAEnEjE,oBAAoB,CAAC;MACnBiE,UAAU,EAAEA,UAAU,IAAI,IAAI;MAC9BC,UAAU,EAAE,IAAI;MAChBC,aAAa,EAAE,IAAI;MACnByB,QAAQ,EAAEA,QAAQ,IAAI,IAAI;MAC1BI,QAAQ,EAAE,IAAI;MACdC,WAAW,EAAE;IACf,CAAC,CAAC;IAEFnE,aAAa,CAAC,EAAE,CAAC;IACjBE,gBAAgB,CAAC,EAAE,CAAC;IAEpB,IAAIiC,UAAU,EAAE;MACdmB,cAAc,CAACnB,UAAU,CAAC;IAC5B;EACF,CAAC;EAED,MAAMiC,oBAAoB,GAAIT,CAAC,IAAK;IAClC,MAAMvB,UAAU,GAAGuB,CAAC,CAACC,MAAM,CAACC,KAAK;IACjC,MAAMK,QAAQ,GAAGnE,UAAU,CAACgE,IAAI,CAACM,CAAC,IAAIA,CAAC,CAACrC,EAAE,KAAKiC,QAAQ,CAAC7B,UAAU,CAAC,CAAC;IAEpElE,oBAAoB,CAACyE,IAAI,KAAK;MAC5B,GAAGA,IAAI;MACPP,UAAU,EAAEA,UAAU,IAAI,IAAI;MAC9BC,aAAa,EAAE,IAAI;MACnB6B,QAAQ,EAAEA,QAAQ,IAAI,IAAI;MAC1BC,WAAW,EAAE;IACf,CAAC,CAAC,CAAC;IAEHjE,gBAAgB,CAAC,EAAE,CAAC;IAEpB,IAAIkC,UAAU,EAAE;MACdoB,iBAAiB,CAACpB,UAAU,CAAC;IAC/B;EACF,CAAC;EAED,MAAMkC,uBAAuB,GAAIX,CAAC,IAAK;IACrC,MAAMtB,aAAa,GAAGsB,CAAC,CAACC,MAAM,CAACC,KAAK;IACpC,MAAMM,WAAW,GAAGlE,aAAa,CAAC8D,IAAI,CAACQ,EAAE,IAAIA,EAAE,CAACvC,EAAE,KAAKiC,QAAQ,CAAC5B,aAAa,CAAC,CAAC;IAE/EnE,oBAAoB,CAACyE,IAAI,KAAK;MAC5B,GAAGA,IAAI;MACPN,aAAa,EAAEA,aAAa,IAAI,IAAI;MACpC8B,WAAW,EAAEA,WAAW,IAAI;IAC9B,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMK,iBAAiB,GAAInD,KAAK,IAAK;IACnC,MAAMoD,UAAU,GAAGhG,cAAc,CAACiG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACpD,GAAG,KAAKF,KAAK,CAACE,GAAG,CAAC;IAEhE,IAAIkD,UAAU,EAAE;MACd;MACA/F,iBAAiB,CAACiE,IAAI,IAAIA,IAAI,CAACiC,MAAM,CAACD,CAAC,IAAIA,CAAC,CAACpD,GAAG,KAAKF,KAAK,CAACE,GAAG,CAAC,CAAC;IAClE,CAAC,MAAM;MACL;MACA7C,iBAAiB,CAACiE,IAAI,IAAI;QACxB,MAAMkC,MAAM,GAAGlC,IAAI,CAAC+B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACpD,GAAG,KAAKF,KAAK,CAACE,GAAG,CAAC;QAClD,IAAIsD,MAAM,EAAE;UACV,OAAOlC,IAAI;QACb;QACA,OAAO,CAAC,GAAGA,IAAI,EAAE;UAAE,GAAGtB;QAAM,CAAC,CAAC;MAChC,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMyD,iBAAiB,GAAIzD,KAAK,IAAK;IACnCjC,cAAc,CAACiC,KAAK,CAAC;IACrBnC,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAM6F,qBAAqB,GAAIC,YAAY,IAAK;IAC9CtG,iBAAiB,CAACiE,IAAI,IACpBA,IAAI,CAACsC,GAAG,CAACN,CAAC,IAAIA,CAAC,CAACpD,GAAG,KAAKyD,YAAY,CAACzD,GAAG,GAAGyD,YAAY,GAAGL,CAAC,CAC7D,CAAC;IACDzF,kBAAkB,CAAC,KAAK,CAAC;IACzBE,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAM8F,eAAe,GAAGA,CAACC,SAAS,EAAEC,OAAO,KAAK;IAC9C,MAAMC,SAAS,GAAG,CAAC,GAAG5G,cAAc,CAAC;IACrC,MAAM,CAAC6G,UAAU,CAAC,GAAGD,SAAS,CAACE,MAAM,CAACJ,SAAS,EAAE,CAAC,CAAC;IACnDE,SAAS,CAACE,MAAM,CAACH,OAAO,EAAE,CAAC,EAAEE,UAAU,CAAC;IACxC5G,iBAAiB,CAAC2G,SAAS,CAAC;EAC9B,CAAC;EAED,MAAMG,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,MAAMC,UAAU,GAAGC,YAAY,CAAC,CAAC;IAEjC,IAAI,CAACD,UAAU,CAAC/C,OAAO,EAAE;MACvBpD,SAAS,CAACmG,UAAU,CAACpG,MAAM,CAAC;MAC5B;IACF;IAEAG,SAAS,CAAC,IAAI,CAAC;IACf,IAAI;MACF;MACA,MAAMmG,iBAAiB,GAAG;QACxB,GAAG1H,iBAAiB;QACpB;QACAkE,UAAU,EAAElE,iBAAiB,CAACkE,UAAU;QACxC2B,QAAQ,EAAE7F,iBAAiB,CAAC6F,QAAQ;QACpC;QACA1B,UAAU,EAAEnE,iBAAiB,CAACmE,UAAU;QACxC8B,QAAQ,EAAEjG,iBAAiB,CAACiG,QAAQ;QACpC;QACA7B,aAAa,EAAEpE,iBAAiB,CAACoE,aAAa,IAAI,IAAI;QACtD8B,WAAW,EAAElG,iBAAiB,CAACkG,WAAW,IAAI;MAChD,CAAC;MAED,MAAMvC,MAAM,GAAG;QACbC,IAAI,EAAE1D,QAAQ;QACd2D,WAAW,EAAEzD,eAAe;QAC5B2C,MAAM,EAAEvC,cAAc;QACtB6D,SAAS,EAAEqD,iBAAiB;QAC5BC,QAAQ,EAAE;UACRC,YAAY,EAAE,IAAI;UAClBC,sBAAsB,EAAE,IAAI;UAC5BC,gBAAgB,EAAE;QACpB;MACF,CAAC;MAED,IAAIC,WAAW;MAEf,IAAIzF,UAAU,EAAE;QACd;QACA;QACA,MAAM0F,YAAY,GAAG3I,iBAAiB,CAACqE,iBAAiB,CAAC,CAAC,CAACoC,IAAI,CAACY,CAAC,IAAIA,CAAC,CAAC3C,EAAE,KAAKvB,aAAa,IAAIkE,CAAC,CAACpD,GAAG,KAAKd,aAAa,CAAC;QACvH,IAAIwF,YAAY,EAAE;UAChB;UACArE,MAAM,CAACI,EAAE,GAAGiE,YAAY,CAACjE,EAAE;UAC3BJ,MAAM,CAACL,GAAG,GAAG0E,YAAY,CAAC1E,GAAG;UAC7BK,MAAM,CAACK,IAAI,GAAGgE,YAAY,CAAChE,IAAI;UAC/BL,MAAM,CAACM,YAAY,GAAG+D,YAAY,CAAC/D,YAAY;UAC/CN,MAAM,CAACsE,SAAS,GAAGD,YAAY,CAACC,SAAS,CAAC,CAAC;UAC3CtE,MAAM,CAACuE,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC,CAAC;;UAE7CL,WAAW,GAAG1I,iBAAiB,CAACgJ,cAAc,CAACL,YAAY,CAAChE,IAAI,EAAEgE,YAAY,CAAC/D,YAAY,EAAEN,MAAM,CAAC;QACtG,CAAC,MAAM;UACL,MAAM,IAAI2E,KAAK,CAAC,qCAAqC,CAAC;QACxD;MACF,CAAC,MAAM;QACL;QACA,IAAItI,iBAAiB,CAACoE,aAAa,EAAE;UACnC2D,WAAW,GAAG1I,iBAAiB,CAACgJ,cAAc,CAAC,aAAa,EAAErI,iBAAiB,CAACoE,aAAa,EAAET,MAAM,CAAC;QACxG,CAAC,MAAM,IAAI3D,iBAAiB,CAACmE,UAAU,EAAE;UACvC4D,WAAW,GAAG1I,iBAAiB,CAACgJ,cAAc,CAAC,UAAU,EAAErI,iBAAiB,CAACmE,UAAU,EAAER,MAAM,CAAC;QAClG,CAAC,MAAM;UACL,MAAM,IAAI2E,KAAK,CAAC,0CAA0C,CAAC;QAC7D;MACF;;MAEA;MACA3F,cAAc,CAAC,CAAC;MAEhB,IAAI/C,MAAM,EAAE;QACVA,MAAM,CAACmI,WAAW,CAAC;MACrB,CAAC,MAAM;QACL;QACA,MAAMQ,OAAO,GAAGjG,UAAU,GAAG,4BAA4B,GAAG,wCAAwC;QACpGkG,KAAK,CAACD,OAAO,CAAC;QACd;QACApI,WAAW,CAAC,EAAE,CAAC;QACfE,kBAAkB,CAAC,EAAE,CAAC;QACtBI,iBAAiB,CAAC,EAAE,CAAC;QACrBR,oBAAoB,CAAC,CAAC,CAAC,CAAC;QACxBsC,aAAa,CAAC,KAAK,CAAC;QACpBE,gBAAgB,CAAC,IAAI,CAAC;MACxB;IACF,CAAC,CAAC,OAAO0C,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C9D,SAAS,CAAC;QAAEoH,OAAO,EAAEtD,KAAK,CAACoD;MAAQ,CAAC,CAAC;IACvC,CAAC,SAAS;MACRhH,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;EAED,MAAMkG,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMrG,MAAM,GAAG,CAAC,CAAC;IAEjB,IAAI,CAAClB,QAAQ,CAACwI,IAAI,CAAC,CAAC,EAAE;MACpBtH,MAAM,CAAClB,QAAQ,GAAG,uBAAuB;IAC3C;;IAEA;IACA,IAAI,CAACF,iBAAiB,CAACkE,UAAU,EAAE;MACjC9C,MAAM,CAACiD,SAAS,GAAG,0BAA0B;IAC/C,CAAC,MAAM,IAAI,CAACrE,iBAAiB,CAACmE,UAAU,EAAE;MACxC/C,MAAM,CAACiD,SAAS,GAAG,0BAA0B;IAC/C,CAAC,MAAM,IAAIrE,iBAAiB,CAACoE,aAAa,IAAI,CAACpE,iBAAiB,CAACmE,UAAU,EAAE;MAC3E/C,MAAM,CAACiD,SAAS,GAAG,sDAAsD;IAC3E;;IAEA;IACA,IAAIrE,iBAAiB,CAACmE,UAAU,EAAE;MAChC,MAAMI,cAAc,GAAGlF,iBAAiB,CAACmF,oBAAoB,CAC3DxE,iBAAiB,CAACmE,UAAU,EAC5BnE,iBAAiB,CAACoE,aACpB,CAAC;MAED,IAAI,CAACG,cAAc,CAACE,OAAO,EAAE;QAC3BrD,MAAM,CAACuD,YAAY,GAAGJ,cAAc,CAACnD,MAAM,CAACwD,IAAI,CAAC,IAAI,CAAC;MACxD;IACF;IAEA,IAAIpE,cAAc,CAACmI,MAAM,KAAK,CAAC,EAAE;MAC/BvH,MAAM,CAAC2B,MAAM,GAAG,kCAAkC;IACpD;;IAEA;IACA,MAAM6F,cAAc,GAAG,CAAC,MAAM,EAAE,cAAc,EAAE,QAAQ,CAAC;IACzD,MAAMC,iBAAiB,GAAGrI,cAAc,CAACwG,GAAG,CAACN,CAAC,IAAIA,CAAC,CAACpD,GAAG,CAAC;IACxD,MAAMwF,eAAe,GAAGF,cAAc,CAACjC,MAAM,CAACoC,EAAE,IAAI,CAACF,iBAAiB,CAACG,QAAQ,CAACD,EAAE,CAAC,CAAC;IAEpF,IAAID,eAAe,CAACH,MAAM,GAAG,CAAC,EAAE;MAC9BvH,MAAM,CAACwH,cAAc,GAAG,4BAA4BE,eAAe,CAAClE,IAAI,CAAC,IAAI,CAAC,EAAE;IAClF;IAEA,OAAO;MACLH,OAAO,EAAEwE,MAAM,CAACC,IAAI,CAAC9H,MAAM,CAAC,CAACuH,MAAM,KAAK,CAAC;MACzCvH;IACF,CAAC;EACH,CAAC;EAED,MAAM+H,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAIC,QAAQ,GAAG9I,eAAe;;IAE9B;IACA,IAAII,cAAc,KAAK,KAAK,EAAE;MAC5B0I,QAAQ,GAAGA,QAAQ,CAACzC,MAAM,CAACvD,KAAK,IAAIA,KAAK,CAACiG,OAAO,KAAK3I,cAAc,CAAC;IACvE;;IAEA;IACA,IAAIE,UAAU,EAAE;MACdwI,QAAQ,GAAGA,QAAQ,CAACzC,MAAM,CAACvD,KAAK,IAC9BA,KAAK,CAACkG,KAAK,CAACC,WAAW,CAAC,CAAC,CAACP,QAAQ,CAACpI,UAAU,CAAC2I,WAAW,CAAC,CAAC,CAAC,IAC5DnG,KAAK,CAACE,GAAG,CAACiG,WAAW,CAAC,CAAC,CAACP,QAAQ,CAACpI,UAAU,CAAC2I,WAAW,CAAC,CAAC,CAC3D,CAAC;IACH;IAEA,OAAOH,QAAQ;EACjB,CAAC;EAED,MAAMI,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMC,MAAM,GAAG;MAAEC,GAAG,EAAEpJ,eAAe,CAACqI;IAAO,CAAC;IAC9CM,MAAM,CAACC,IAAI,CAAC/J,sBAAsB,CAAC,CAACgE,OAAO,CAACwG,UAAU,IAAI;MACxDF,MAAM,CAACE,UAAU,CAAC,GAAGrJ,eAAe,CAACqG,MAAM,CAACD,CAAC,IAAIA,CAAC,CAAC2C,OAAO,KAAKM,UAAU,CAAC,CAAChB,MAAM;IACnF,CAAC,CAAC;IACF,OAAOc,MAAM;EACf,CAAC;EAED,MAAMG,aAAa,GAAGJ,gBAAgB,CAAC,CAAC;EACxC,MAAMK,cAAc,GAAGV,iBAAiB,CAAC,CAAC;EAE1C,oBACEzJ,OAAA;IAAKoK,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC3BrK,OAAA;MAAKoK,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClCrK,OAAA;QAAAqK,QAAA,EAAI;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrBzK,OAAA;QAAKoK,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BrK,OAAA;UACEsE,IAAI,EAAC,QAAQ;UACboG,OAAO,EAAEA,CAAA,KAAMrJ,cAAc,CAAC,IAAI,CAAE;UACpC+I,SAAS,EAAC,mBAAmB;UAC7BO,QAAQ,EAAE7J,cAAc,CAACmI,MAAM,KAAK,CAAE;UAAAoB,QAAA,EACvC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTzK,OAAA;UACEsE,IAAI,EAAC,QAAQ;UACboG,OAAO,EAAE7C,UAAW;UACpBuC,SAAS,EAAC,iBAAiB;UAC3BO,QAAQ,EAAE/I,MAAM,IAAIF,MAAM,CAACuD,YAAa;UAAAoF,QAAA,EAEvCzI,MAAM,GAAIgB,UAAU,GAAG,aAAa,GAAG,WAAW,GAAKA,UAAU,GAAG,aAAa,GAAG;QAAY;UAAA0H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3F,CAAC,EACRtK,QAAQ,iBACPH,OAAA;UACEsE,IAAI,EAAC,QAAQ;UACboG,OAAO,EAAEvK,QAAS;UAClBiK,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAC5B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAEL/I,MAAM,CAACqH,OAAO,iBACb/I,OAAA;MAAKoK,SAAS,EAAC,mBAAmB;MAAAC,QAAA,EAAE3I,MAAM,CAACqH;IAAO;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CACzD,EAGAlB,MAAM,CAACC,IAAI,CAAC9H,MAAM,CAAC,CAACuH,MAAM,GAAG,CAAC,iBAC7BjJ,OAAA;MAAKoK,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCrK,OAAA;QAAAqK,QAAA,EAAI;MAAgC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACzCzK,OAAA;QAAAqK,QAAA,EACGd,MAAM,CAACqB,OAAO,CAAClJ,MAAM,CAAC,CAAC4F,GAAG,CAAC,CAAC,CAAC1D,GAAG,EAAEiF,OAAO,CAAC,kBACzC7I,OAAA;UAAAqK,QAAA,EAAexB;QAAO,GAAbjF,GAAG;UAAA0G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAC5B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CACN,eAEDzK,OAAA;MAAKoK,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBAEnCrK,OAAA;QAAKoK,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAE3BrK,OAAA;UAAKoK,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BrK,OAAA;YAAKoK,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BrK,OAAA;cAAAqK,QAAA,GAAI,eAAa,EAACvI,UAAU,CAACmH,MAAM,EAAC,GAAC;YAAA;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1CzK,OAAA;cACEsE,IAAI,EAAC,QAAQ;cACb8F,SAAS,EAAC,UAAU;cACpBM,OAAO,EAAEA,CAAA,KAAMzI,iBAAiB,CAAC,CAACD,cAAc,CAAE;cAAAqI,QAAA,EAEjDrI,cAAc,GAAG,MAAM,GAAG;YAAM;cAAAsI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAELzI,cAAc,iBACbhC,OAAA;YAAKoK,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAC9BvI,UAAU,CAACmH,MAAM,KAAK,CAAC,gBACtBjJ,OAAA;cAAGoK,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAiD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,GAErF3I,UAAU,CAACwF,GAAG,CAAEuD,IAAI,iBAClB7K,OAAA;cAAoBoK,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC7CrK,OAAA;gBAAKoK,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/BrK,OAAA;kBAAAqK,QAAA,EAAKQ,IAAI,CAAC3G;gBAAI;kBAAAoG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACpBzK,OAAA;kBAAMoK,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAEQ,IAAI,CAACvG;gBAAI;kBAAAgG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC,eACNzK,OAAA;gBAAGoK,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAEQ,IAAI,CAAC1G,WAAW,IAAI;cAAgB;gBAAAmG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1EzK,OAAA;gBAAKoK,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBrK,OAAA;kBAAAqK,QAAA,GAAOQ,IAAI,CAACC,OAAO,CAACC,UAAU,EAAC,SAAO;gBAAA;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC7CzK,OAAA;kBAAAqK,QAAA,EAAM;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACdzK,OAAA;kBAAAqK,QAAA,GAAM,UAAQ,EAAC,IAAI5B,IAAI,CAACoC,IAAI,CAACrC,SAAS,CAAC,CAACwC,kBAAkB,CAAC,CAAC;gBAAA;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC,eACNzK,OAAA;gBAAKoK,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3BrK,OAAA;kBACEsE,IAAI,EAAC,QAAQ;kBACb8F,SAAS,EAAC,uBAAuB;kBACjCM,OAAO,EAAEA,CAAA,KAAMxH,iBAAiB,CAAC2H,IAAI,CAAE;kBAAAR,QAAA,EACxC;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTzK,OAAA;kBACEsE,IAAI,EAAC,QAAQ;kBACb8F,SAAS,EAAC,sBAAsB;kBAChCM,OAAO,EAAEA,CAAA,KAAM;oBACb,IAAIO,MAAM,CAACC,OAAO,CAAC,4CAA4C,CAAC,EAAE;sBAChEvL,iBAAiB,CAACwL,gBAAgB,CAACN,IAAI,CAACvG,IAAI,EAAEuG,IAAI,CAACtG,YAAY,CAAC;sBAChEtB,cAAc,CAAC,CAAC;oBAClB;kBACF,CAAE;kBAAAoH,QAAA,EACH;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA,GA/BEI,IAAI,CAACjH,GAAG;cAAA0G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgCb,CACN;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNzK,OAAA;UAAKoK,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BrK,OAAA;YAAAqK,QAAA,EAAI;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAE3BzK,OAAA;YAAKoK,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBrK,OAAA;cAAAqK,QAAA,EAAO;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1BzK,OAAA;cACEsE,IAAI,EAAC,MAAM;cACX4B,KAAK,EAAE1F,QAAS;cAChB4K,QAAQ,EAAGpF,CAAC,IAAKvF,WAAW,CAACuF,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;cAC7CmF,WAAW,EAAC,iBAAiB;cAC7BjB,SAAS,EAAE1I,MAAM,CAAClB,QAAQ,GAAG,OAAO,GAAG;YAAG;cAAA8J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,EACD/I,MAAM,CAAClB,QAAQ,iBAAIR,OAAA;cAAKoK,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAE3I,MAAM,CAAClB;YAAQ;cAAA8J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC,eAENzK,OAAA;YAAKoK,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBrK,OAAA;cAAAqK,QAAA,EAAO;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1BzK,OAAA;cACEkG,KAAK,EAAExF,eAAgB;cACvB0K,QAAQ,EAAGpF,CAAC,IAAKrF,kBAAkB,CAACqF,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;cACpDmF,WAAW,EAAC,wBAAwB;cACpCC,IAAI,EAAC;YAAG;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGH,CAAC,eAGNzK,OAAA;UAAKoK,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BrK,OAAA;YAAAqK,QAAA,GAAI,mBAAiB,EAACvJ,cAAc,CAACmI,MAAM,EAAC,GAAC;UAAA;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACjD/I,MAAM,CAAC2B,MAAM,iBAAIrD,OAAA;YAAKoK,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAE3I,MAAM,CAAC2B;UAAM;YAAAiH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EACrE/I,MAAM,CAACwH,cAAc,iBAAIlJ,OAAA;YAAKoK,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAE3I,MAAM,CAACwH;UAAc;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAEtFzK,OAAA;YAAKoK,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAC7BvJ,cAAc,CAACmI,MAAM,KAAK,CAAC,gBAC1BjJ,OAAA;cAAKoK,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAE7B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,GAEN3J,cAAc,CAACwG,GAAG,CAAC,CAAC5D,KAAK,EAAE6H,KAAK,kBAC9BvL,OAAA;cAAqBoK,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7CrK,OAAA;gBAAKoK,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBrK,OAAA;kBAAMoK,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAE3G,KAAK,CAACkG;gBAAK;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClDzK,OAAA;kBAAMoK,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAE3G,KAAK,CAACY;gBAAI;kBAAAgG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EAC/C/G,KAAK,CAAC8H,QAAQ,iBAAIxL,OAAA;kBAAMoK,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CAAC,eACNzK,OAAA;gBAAKoK,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5BrK,OAAA;kBACEsE,IAAI,EAAC,QAAQ;kBACboG,OAAO,EAAEA,CAAA,KAAMvD,iBAAiB,CAACzD,KAAK,CAAE;kBACxC0G,SAAS,EAAC,UAAU;kBACpBqB,KAAK,EAAC,iBAAiB;kBAAApB,QAAA,EACxB;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTzK,OAAA;kBACEsE,IAAI,EAAC,QAAQ;kBACboG,OAAO,EAAEA,CAAA,KAAMnD,eAAe,CAACgE,KAAK,EAAEG,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEJ,KAAK,GAAG,CAAC,CAAC,CAAE;kBAC9DnB,SAAS,EAAC,UAAU;kBACpBO,QAAQ,EAAEY,KAAK,KAAK,CAAE;kBACtBE,KAAK,EAAC,SAAS;kBAAApB,QAAA,EAChB;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTzK,OAAA;kBACEsE,IAAI,EAAC,QAAQ;kBACboG,OAAO,EAAEA,CAAA,KAAMnD,eAAe,CAACgE,KAAK,EAAEG,IAAI,CAACE,GAAG,CAAC9K,cAAc,CAACmI,MAAM,GAAG,CAAC,EAAEsC,KAAK,GAAG,CAAC,CAAC,CAAE;kBACtFnB,SAAS,EAAC,UAAU;kBACpBO,QAAQ,EAAEY,KAAK,KAAKzK,cAAc,CAACmI,MAAM,GAAG,CAAE;kBAC9CwC,KAAK,EAAC,WAAW;kBAAApB,QAAA,EAClB;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTzK,OAAA;kBACEsE,IAAI,EAAC,QAAQ;kBACboG,OAAO,EAAEA,CAAA,KAAM7D,iBAAiB,CAACnD,KAAK,CAAE;kBACxC0G,SAAS,EAAC,iBAAiB;kBAC3BqB,KAAK,EAAC,cAAc;kBAAApB,QAAA,EACrB;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA,GAzCE/G,KAAK,CAACE,GAAG;cAAA0G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA0Cd,CACN;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNzK,OAAA;QAAKoK,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BrK,OAAA;UAAKoK,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5BrK,OAAA;YAAAqK,QAAA,EAAI;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eAGNzK,OAAA;UAAKoK,SAAS,EAAC,qBAAqB;UAACyB,KAAK,EAAE;YAC1CC,eAAe,EAAE,SAAS;YAC1BC,MAAM,EAAE,mBAAmB;YAC3BC,YAAY,EAAE,KAAK;YACnBC,OAAO,EAAE,QAAQ;YACjBC,MAAM,EAAE;UACV,CAAE;UAAA7B,QAAA,gBACArK,OAAA;YAAI6L,KAAK,EAAE;cAAEK,MAAM,EAAE,YAAY;cAAEC,KAAK,EAAE;YAAU,CAAE;YAAA9B,QAAA,EAAC;UAEvD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLzK,OAAA;YAAK6L,KAAK,EAAE;cACVO,QAAQ,EAAE,UAAU;cACpBD,KAAK,EAAE,SAAS;cAChBE,YAAY,EAAE,MAAM;cACpBJ,OAAO,EAAE,SAAS;cAClBH,eAAe,EAAE,SAAS;cAC1BE,YAAY,EAAE,KAAK;cACnBD,MAAM,EAAE;YACV,CAAE;YAAA1B,QAAA,gBACArK,OAAA;cAAAqK,QAAA,EAAQ;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACrCzK,OAAA;cAAI6L,KAAK,EAAE;gBAAEK,MAAM,EAAE,iBAAiB;gBAAEI,WAAW,EAAE;cAAO,CAAE;cAAAjC,QAAA,gBAC5DrK,OAAA;gBAAAqK,QAAA,EAAI;cAAkF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3FzK,OAAA;gBAAAqK,QAAA,EAAI;cAAuC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChDzK,OAAA;gBAAAqK,QAAA,EAAI;cAAqF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5F,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAENzK,OAAA;YAAKoK,SAAS,EAAC,qBAAqB;YAACyB,KAAK,EAAE;cAC1CU,OAAO,EAAE,MAAM;cACfC,mBAAmB,EAAE,sCAAsC;cAC3DC,GAAG,EAAE;YACP,CAAE;YAAApC,QAAA,gBAEArK,OAAA;cAAKoK,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBrK,OAAA;gBAAO6L,KAAK,EAAE;kBACZU,OAAO,EAAE,OAAO;kBAChBF,YAAY,EAAE,QAAQ;kBACtBK,UAAU,EAAE,KAAK;kBACjBP,KAAK,EAAE;gBACT,CAAE;gBAAA9B,QAAA,EAAC;cAEH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRzK,OAAA;gBACEkG,KAAK,EAAE5F,iBAAiB,CAACkE,UAAU,IAAI,EAAG;gBAC1C4G,QAAQ,EAAErF,oBAAqB;gBAC/B4E,QAAQ,EAAEnI,OAAO,CAACN,SAAU;gBAC5BkI,SAAS,EAAE1I,MAAM,CAAC8C,UAAU,GAAG,OAAO,GAAG,EAAG;gBAC5CqH,KAAK,EAAE;kBACLc,KAAK,EAAE,MAAM;kBACbV,OAAO,EAAE,SAAS;kBAClBF,MAAM,EAAE,mBAAmB;kBAC3BC,YAAY,EAAE,KAAK;kBACnBI,QAAQ,EAAE,MAAM;kBAChBN,eAAe,EAAE;gBACnB,CAAE;gBAAAzB,QAAA,gBAEFrK,OAAA;kBAAQkG,KAAK,EAAC,EAAE;kBAAAmE,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACxCvI,SAAS,CAACoF,GAAG,CAACnB,QAAQ,iBACrBnG,OAAA;kBAA0BkG,KAAK,EAAEC,QAAQ,CAAC9B,EAAG;kBAAAgG,QAAA,EAC1ClE,QAAQ,CAACjC;gBAAI,GADHiC,QAAQ,CAAC9B,EAAE;kBAAAiG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEhB,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,EACR/I,MAAM,CAAC8C,UAAU,iBAChBxE,OAAA;gBAAK6L,KAAK,EAAE;kBAAEM,KAAK,EAAE,SAAS;kBAAEC,QAAQ,EAAE,UAAU;kBAAEQ,SAAS,EAAE;gBAAU,CAAE;gBAAAvC,QAAA,EAC1E3I,MAAM,CAAC8C;cAAU;gBAAA8F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGNzK,OAAA;cAAKoK,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBrK,OAAA;gBAAO6L,KAAK,EAAE;kBACZU,OAAO,EAAE,OAAO;kBAChBF,YAAY,EAAE,QAAQ;kBACtBK,UAAU,EAAE,KAAK;kBACjBP,KAAK,EAAE;gBACT,CAAE;gBAAA9B,QAAA,EAAC;cAEH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRzK,OAAA;gBACEkG,KAAK,EAAE5F,iBAAiB,CAACmE,UAAU,IAAI,EAAG;gBAC1C2G,QAAQ,EAAE3E,oBAAqB;gBAC/BkE,QAAQ,EAAE,CAACrK,iBAAiB,CAACkE,UAAU,IAAIhC,OAAO,CAACJ,UAAW;gBAC9DgI,SAAS,EAAE1I,MAAM,CAAC+C,UAAU,GAAG,OAAO,GAAG,EAAG;gBAC5CoH,KAAK,EAAE;kBACLc,KAAK,EAAE,MAAM;kBACbV,OAAO,EAAE,SAAS;kBAClBF,MAAM,EAAE,mBAAmB;kBAC3BC,YAAY,EAAE,KAAK;kBACnBI,QAAQ,EAAE,MAAM;kBAChBN,eAAe,EAAE;gBACnB,CAAE;gBAAAzB,QAAA,gBAEFrK,OAAA;kBAAQkG,KAAK,EAAC,EAAE;kBAAAmE,QAAA,EACb,CAAC/J,iBAAiB,CAACkE,UAAU,GAAG,uBAAuB,GAAG;gBAAiB;kBAAA8F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtE,CAAC,EACRrI,UAAU,CAACkF,GAAG,CAACf,QAAQ,iBACtBvG,OAAA;kBAA0BkG,KAAK,EAAEK,QAAQ,CAAClC,EAAG;kBAAAgG,QAAA,EAC1C9D,QAAQ,CAACrC;gBAAI,GADHqC,QAAQ,CAAClC,EAAE;kBAAAiG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEhB,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,EACR/I,MAAM,CAAC+C,UAAU,iBAChBzE,OAAA;gBAAK6L,KAAK,EAAE;kBAAEM,KAAK,EAAE,SAAS;kBAAEC,QAAQ,EAAE,UAAU;kBAAEQ,SAAS,EAAE;gBAAU,CAAE;gBAAAvC,QAAA,EAC1E3I,MAAM,CAAC+C;cAAU;gBAAA6F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGNzK,OAAA;cAAKoK,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBrK,OAAA;gBAAO6L,KAAK,EAAE;kBACZU,OAAO,EAAE,OAAO;kBAChBF,YAAY,EAAE,QAAQ;kBACtBK,UAAU,EAAE,KAAK;kBACjBP,KAAK,EAAE;gBACT,CAAE;gBAAA9B,QAAA,EAAC;cAEH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRzK,OAAA;gBACEkG,KAAK,EAAE5F,iBAAiB,CAACoE,aAAa,IAAI,EAAG;gBAC7C0G,QAAQ,EAAEzE,uBAAwB;gBAClCgE,QAAQ,EAAE,CAACrK,iBAAiB,CAACmE,UAAU,IAAIjC,OAAO,CAACF,aAAc;gBACjEuJ,KAAK,EAAE;kBACLc,KAAK,EAAE,MAAM;kBACbV,OAAO,EAAE,SAAS;kBAClBF,MAAM,EAAE,mBAAmB;kBAC3BC,YAAY,EAAE,KAAK;kBACnBI,QAAQ,EAAE,MAAM;kBAChBN,eAAe,EAAE;gBACnB,CAAE;gBAAAzB,QAAA,gBAEFrK,OAAA;kBAAQkG,KAAK,EAAC,EAAE;kBAAAmE,QAAA,EACb,CAAC/J,iBAAiB,CAACmE,UAAU,GAAG,uBAAuB,GAAG;gBAA+B;kBAAA6F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpF,CAAC,EACRnI,aAAa,CAACgF,GAAG,CAACd,WAAW,iBAC5BxG,OAAA;kBAA6BkG,KAAK,EAAEM,WAAW,CAACnC,EAAG;kBAAAgG,QAAA,EAChD7D,WAAW,CAACtC;gBAAI,GADNsC,WAAW,CAACnC,EAAE;kBAAAiG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEnB,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAEL/I,MAAM,CAACiD,SAAS,iBACf3E,OAAA;YAAK6L,KAAK,EAAE;cAAEM,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE,UAAU;cAAEQ,SAAS,EAAE;YAAS,CAAE;YAAAvC,QAAA,EACzE3I,MAAM,CAACiD;UAAS;YAAA2F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CACN,EACA/I,MAAM,CAACuD,YAAY,iBAClBjF,OAAA;YAAK6L,KAAK,EAAE;cAAEM,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE,UAAU;cAAEQ,SAAS,EAAE,QAAQ;cAAEF,UAAU,EAAE;YAAO,CAAE;YAAArC,QAAA,GAAC,eAC5F,EAAC3I,MAAM,CAACuD,YAAY;UAAA;YAAAqF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CACN,EAGA/H,gBAAgB,IAAIA,gBAAgB,CAACmK,aAAa,CAAC5D,MAAM,GAAG,CAAC,iBAC5DjJ,OAAA;YAAK6L,KAAK,EAAE;cACVe,SAAS,EAAE,MAAM;cACjBX,OAAO,EAAE,SAAS;cAClBH,eAAe,EAAE,SAAS;cAC1BC,MAAM,EAAE,mBAAmB;cAC3BC,YAAY,EAAE,KAAK;cACnBI,QAAQ,EAAE;YACZ,CAAE;YAAA/B,QAAA,gBACArK,OAAA;cAAAqK,QAAA,EAAQ;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAClC/H,gBAAgB,CAACmK,aAAa,CAACvF,GAAG,CAAC,CAACuD,IAAI,EAAEU,KAAK,kBAC9CvL,OAAA;cAAiB6L,KAAK,EAAE;gBAAEe,SAAS,EAAE;cAAS,CAAE;cAAAvC,QAAA,gBAC9CrK,OAAA;gBAAAqK,QAAA,GAASQ,IAAI,CAACvG,IAAI,KAAK,UAAU,GAAG,UAAU,GAAG,aAAa,EAAC,QAAM;cAAA;gBAAAgG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACI,IAAI,CAAC3G,IAAI,EACxF2G,IAAI,CAAC1G,WAAW,iBAAInE,OAAA;gBAAK6L,KAAK,EAAE;kBAAEM,KAAK,EAAE;gBAAU,CAAE;gBAAA9B,QAAA,EAAEQ,IAAI,CAAC1G;cAAW;gBAAAmG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,GAFvEc,KAAK;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGV,CACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN,EAGA/H,gBAAgB,IAAIA,gBAAgB,CAACoK,sBAAsB,CAAC7D,MAAM,GAAG,CAAC,IAAI,CAAC3I,iBAAiB,CAACoE,aAAa,iBACzG1E,OAAA;YAAK6L,KAAK,EAAE;cACVe,SAAS,EAAE,MAAM;cACjBX,OAAO,EAAE,SAAS;cAClBH,eAAe,EAAE,SAAS;cAC1BC,MAAM,EAAE,mBAAmB;cAC3BC,YAAY,EAAE,KAAK;cACnBI,QAAQ,EAAE;YACZ,CAAE;YAAA/B,QAAA,gBACArK,OAAA;cAAAqK,QAAA,EAAQ;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,uBAAmB,EAAC/H,gBAAgB,CAACoK,sBAAsB,CAAC7D,MAAM,EAAC,mEAEjG;UAAA;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN,EAGAnK,iBAAiB,CAACmE,UAAU,IAAI,CAAC/C,MAAM,CAACuD,YAAY,iBACnDjF,OAAA;YAAK6L,KAAK,EAAE;cACVe,SAAS,EAAE,MAAM;cACjBX,OAAO,EAAE,SAAS;cAClBH,eAAe,EAAE,SAAS;cAC1BC,MAAM,EAAE,mBAAmB;cAC3BC,YAAY,EAAE,KAAK;cACnBI,QAAQ,EAAE;YACZ,CAAE;YAAA/B,QAAA,gBACArK,OAAA;cAAAqK,QAAA,EAAQ;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,oCAAgC,EAACnK,iBAAiB,CAACoE,aAAa,GAAG,aAAa,GAAG,UAAU,EAAC,GACnI;UAAA;YAAA4F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNzK,OAAA;UAAKoK,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BrK,OAAA;YACEsE,IAAI,EAAC,MAAM;YACX+G,WAAW,EAAC,kBAAkB;YAC9BnF,KAAK,EAAEhF,UAAW;YAClBkK,QAAQ,EAAGpF,CAAC,IAAK7E,aAAa,CAAC6E,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;YAC/CkE,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNzK,OAAA;UAAKoK,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BrK,OAAA;YACEsE,IAAI,EAAC,QAAQ;YACboG,OAAO,EAAEA,CAAA,KAAMzJ,iBAAiB,CAAC,KAAK,CAAE;YACxCmJ,SAAS,EAAE,eAAepJ,cAAc,KAAK,KAAK,GAAG,QAAQ,GAAG,EAAE,EAAG;YAAAqJ,QAAA,GACtE,OACM,EAACH,aAAa,CAACF,GAAG,EAAC,GAC1B;UAAA;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACRlB,MAAM,CAACqB,OAAO,CAACnL,sBAAsB,CAAC,CAAC6H,GAAG,CAAC,CAAC,CAAC2C,UAAU,EAAEN,OAAO,CAAC,kBAChE3J,OAAA;YAEEsE,IAAI,EAAC,QAAQ;YACboG,OAAO,EAAEA,CAAA,KAAMzJ,iBAAiB,CAACgJ,UAAU,CAAE;YAC7CG,SAAS,EAAE,eAAepJ,cAAc,KAAKiJ,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;YAAAI,QAAA,GAEzEV,OAAO,CAAC8B,KAAK,EAAC,IAAE,EAACvB,aAAa,CAACD,UAAU,CAAC,EAAC,GAC9C;UAAA,GANOA,UAAU;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMT,CACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNzK,OAAA;UAAKoK,SAAS,EAAC,aAAa;UAAAC,QAAA,EACzBF,cAAc,CAAC7C,GAAG,CAAC5D,KAAK,IAAI;YAC3B,MAAMoD,UAAU,GAAGhG,cAAc,CAACiG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACpD,GAAG,KAAKF,KAAK,CAACE,GAAG,CAAC;YAChE,oBACE5D,OAAA;cAEEoK,SAAS,EAAE,cAActD,UAAU,GAAG,UAAU,GAAG,EAAE,EAAG;cACxD4D,OAAO,EAAEA,CAAA,KAAM7D,iBAAiB,CAACnD,KAAK,CAAE;cAAA2G,QAAA,gBAExCrK,OAAA;gBAAKoK,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,eAC7BrK,OAAA;kBACEsE,IAAI,EAAC,UAAU;kBACfyI,OAAO,EAAEjG,UAAW;kBACpBsE,QAAQ,EAAEA,CAAA,KAAMvE,iBAAiB,CAACnD,KAAK;gBAAE;kBAAA4G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNzK,OAAA;gBAAKoK,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5BrK,OAAA;kBAAKoK,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAE3G,KAAK,CAACkG;gBAAK;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC/CzK,OAAA;kBAAKoK,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBrK,OAAA;oBAAMoK,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAE3G,KAAK,CAACY;kBAAI;oBAAAgG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EAC/C/G,KAAK,CAAC8H,QAAQ,iBAAIxL,OAAA;oBAAMoK,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EAClE/G,KAAK,CAACsJ,WAAW,iBAAIhN,OAAA;oBAAMoK,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,GAlBD/G,KAAK,CAACE,GAAG;cAAA0G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmBX,CAAC;UAEV,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLnJ,eAAe,IAAIE,WAAW,iBAC7BxB,OAAA,CAACH,gBAAgB;MACf6D,KAAK,EAAElC,WAAY;MACnBtB,MAAM,EAAEkH,qBAAsB;MAC9BjH,QAAQ,EAAEA,CAAA,KAAM;QACdoB,kBAAkB,CAAC,KAAK,CAAC;QACzBE,cAAc,CAAC,IAAI,CAAC;MACtB;IAAE;MAAA6I,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACF,EAEArJ,WAAW,iBACVpB,OAAA,CAACF,WAAW;MACVuD,MAAM,EAAEvC,cAAe;MACvBN,QAAQ,EAAEA,QAAS;MACnByM,OAAO,EAAEA,CAAA,KAAM5L,cAAc,CAAC,KAAK;IAAE;MAAAiJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACpK,EAAA,CA17BIJ,WAAW;AAAAiN,EAAA,GAAXjN,WAAW;AA47BjB,eAAeA,WAAW;AAAC,IAAAiN,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}