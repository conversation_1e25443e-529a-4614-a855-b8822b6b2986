{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Advanced\\\\Advanced CRM filed all use\\\\frontend\\\\src\\\\components\\\\forms\\\\AllFormsModal.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport formConfigService from '../../services/formConfigService';\nimport apiService from '../../services/apiService';\nimport './AllFormsModal.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AllFormsModal = ({\n  onClose,\n  onEditForm,\n  onDeleteForm,\n  isInline = false\n}) => {\n  _s();\n  const [forms, setForms] = useState([]);\n  const [divisions, setDivisions] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [deleteConfirm, setDeleteConfirm] = useState(null);\n  useEffect(() => {\n    loadData();\n  }, []);\n  const loadData = async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      // Load divisions and categories\n      const [divisionsResponse, categoriesResponse] = await Promise.all([apiService.getDivisions(), apiService.getCategories()]);\n      setDivisions(divisionsResponse.data || []);\n      setCategories(categoriesResponse.data || []);\n\n      // Load all forms from localStorage\n      console.log('Checking localStorage for forms...');\n      console.log('All localStorage keys:', Object.keys(localStorage));\n      console.log('Form-related keys:', Object.keys(localStorage).filter(key => key.includes('crm_form_config')));\n      const allForms = formConfigService.getAllFormConfigs();\n      console.log('Loaded forms from localStorage:', allForms);\n\n      // If no forms exist, create a sample form for testing\n      if (allForms.length === 0) {\n        var _divisionsResponse$da, _divisionsResponse$da2, _categoriesResponse$d, _categoriesResponse$d2;\n        console.log('No forms found, creating sample form for testing...');\n        const sampleForm = {\n          name: 'Sample Employee Form',\n          description: 'A sample form for testing',\n          fields: [{\n            name: 'firstName',\n            label: 'First Name',\n            type: 'text',\n            required: true\n          }, {\n            name: 'lastName',\n            label: 'Last Name',\n            type: 'text',\n            required: true\n          }, {\n            name: 'email',\n            label: 'Email',\n            type: 'email',\n            required: true\n          }],\n          hierarchy: {\n            divisionId: ((_divisionsResponse$da = divisionsResponse.data) === null || _divisionsResponse$da === void 0 ? void 0 : (_divisionsResponse$da2 = _divisionsResponse$da[0]) === null || _divisionsResponse$da2 === void 0 ? void 0 : _divisionsResponse$da2.id) || 1,\n            categoryId: ((_categoriesResponse$d = categoriesResponse.data) === null || _categoriesResponse$d === void 0 ? void 0 : (_categoriesResponse$d2 = _categoriesResponse$d[0]) === null || _categoriesResponse$d2 === void 0 ? void 0 : _categoriesResponse$d2.id) || 1\n          },\n          type: 'category',\n          createdAt: new Date().toISOString(),\n          updatedAt: new Date().toISOString()\n        };\n        try {\n          var _categoriesResponse$d3, _categoriesResponse$d4;\n          formConfigService.saveFormConfig('category', ((_categoriesResponse$d3 = categoriesResponse.data) === null || _categoriesResponse$d3 === void 0 ? void 0 : (_categoriesResponse$d4 = _categoriesResponse$d3[0]) === null || _categoriesResponse$d4 === void 0 ? void 0 : _categoriesResponse$d4.id) || 1, sampleForm);\n          console.log('Sample form created successfully');\n          // Reload forms after creating sample\n          const updatedForms = formConfigService.getAllFormConfigs();\n          console.log('Updated forms list:', updatedForms);\n          setForms(updatedForms.map(form => {\n            var _divisionsResponse$da3, _divisionsResponse$da4, _categoriesResponse$d5, _categoriesResponse$d6, _form$hierarchy3, _form$hierarchy4;\n            return {\n              ...form,\n              divisionName: ((_divisionsResponse$da3 = divisionsResponse.data) === null || _divisionsResponse$da3 === void 0 ? void 0 : (_divisionsResponse$da4 = _divisionsResponse$da3.find(d => {\n                var _form$hierarchy;\n                return d.id === ((_form$hierarchy = form.hierarchy) === null || _form$hierarchy === void 0 ? void 0 : _form$hierarchy.divisionId);\n              })) === null || _divisionsResponse$da4 === void 0 ? void 0 : _divisionsResponse$da4.name) || 'General',\n              categoryName: ((_categoriesResponse$d5 = categoriesResponse.data) === null || _categoriesResponse$d5 === void 0 ? void 0 : (_categoriesResponse$d6 = _categoriesResponse$d5.find(c => {\n                var _form$hierarchy2;\n                return c.id === ((_form$hierarchy2 = form.hierarchy) === null || _form$hierarchy2 === void 0 ? void 0 : _form$hierarchy2.categoryId);\n              })) === null || _categoriesResponse$d6 === void 0 ? void 0 : _categoriesResponse$d6.name) || 'Default',\n              divisionId: ((_form$hierarchy3 = form.hierarchy) === null || _form$hierarchy3 === void 0 ? void 0 : _form$hierarchy3.divisionId) || null,\n              categoryId: ((_form$hierarchy4 = form.hierarchy) === null || _form$hierarchy4 === void 0 ? void 0 : _form$hierarchy4.categoryId) || null\n            };\n          }));\n          setLoading(false);\n          return;\n        } catch (error) {\n          console.error('Error creating sample form:', error);\n        }\n      }\n\n      // Enrich forms with division and category information\n      const enrichedForms = allForms.map(form => {\n        var _form$hierarchy5, _form$hierarchy6, _form$hierarchy7, _form$hierarchy8;\n        let divisionName = 'General';\n        let categoryName = 'Default';\n        if ((_form$hierarchy5 = form.hierarchy) !== null && _form$hierarchy5 !== void 0 && _form$hierarchy5.divisionId) {\n          var _divisionsResponse$da5;\n          const division = (_divisionsResponse$da5 = divisionsResponse.data) === null || _divisionsResponse$da5 === void 0 ? void 0 : _divisionsResponse$da5.find(d => d.id === form.hierarchy.divisionId);\n          if (division) {\n            divisionName = division.name;\n          }\n        }\n        if ((_form$hierarchy6 = form.hierarchy) !== null && _form$hierarchy6 !== void 0 && _form$hierarchy6.categoryId) {\n          var _categoriesResponse$d7;\n          const category = (_categoriesResponse$d7 = categoriesResponse.data) === null || _categoriesResponse$d7 === void 0 ? void 0 : _categoriesResponse$d7.find(c => c.id === form.hierarchy.categoryId);\n          if (category) {\n            categoryName = category.name;\n          }\n        }\n        return {\n          ...form,\n          divisionName,\n          categoryName,\n          divisionId: ((_form$hierarchy7 = form.hierarchy) === null || _form$hierarchy7 === void 0 ? void 0 : _form$hierarchy7.divisionId) || null,\n          categoryId: ((_form$hierarchy8 = form.hierarchy) === null || _form$hierarchy8 === void 0 ? void 0 : _form$hierarchy8.categoryId) || null\n        };\n      });\n      console.log('Enriched forms:', enrichedForms);\n      setForms(enrichedForms);\n    } catch (err) {\n      console.error('Error loading forms data:', err);\n      setError('Failed to load forms data. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleDeleteForm = form => {\n    setDeleteConfirm(form);\n  };\n  const confirmDelete = () => {\n    if (deleteConfirm) {\n      try {\n        formConfigService.deleteFormConfig(deleteConfirm.type, deleteConfirm.associatedId);\n        setForms(forms.filter(f => f.id !== deleteConfirm.id));\n        setDeleteConfirm(null);\n        if (onDeleteForm) {\n          onDeleteForm(deleteConfirm);\n        }\n      } catch (err) {\n        console.error('Error deleting form:', err);\n        setError('Failed to delete form. Please try again.');\n      }\n    }\n  };\n  const cancelDelete = () => {\n    setDeleteConfirm(null);\n  };\n  const handleEditForm = form => {\n    if (onEditForm) {\n      onEditForm(form);\n    }\n    // Don't call onClose() here - let the parent component handle navigation\n    // onClose() was causing the component to return to person list instead of form builder\n  };\n\n  // Group forms by division and then by category\n  const groupedForms = forms.reduce((acc, form) => {\n    const divisionKey = form.divisionId || 'general';\n    const categoryKey = form.categoryId || 'default';\n    if (!acc[divisionKey]) {\n      acc[divisionKey] = {\n        name: form.divisionName,\n        categories: {}\n      };\n    }\n    if (!acc[divisionKey].categories[categoryKey]) {\n      acc[divisionKey].categories[categoryKey] = {\n        name: form.categoryName,\n        forms: []\n      };\n    }\n    acc[divisionKey].categories[categoryKey].forms.push(form);\n    return acc;\n  }, {});\n  const formatDate = dateString => {\n    if (!dateString) return 'N/A';\n    return new Date(dateString).toLocaleDateString();\n  };\n  if (loading) {\n    const content = /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"All Forms\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onClose,\n          className: \"close-button\",\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-body\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-state\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Loading forms...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 7\n    }, this);\n    return isInline ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"all-forms-inline\",\n      children: content\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 7\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"all-forms-modal\",\n      children: content\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 7\n    }, this);\n  }\n  const content = /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"modal-content\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"All Forms\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onClose,\n        className: \"close-button\",\n        children: \"\\xD7\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-body\",\n      children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-message\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"error-icon\",\n          children: \"\\u26A0\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 11\n      }, this), forms.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"empty-state\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"empty-icon\",\n          children: \"\\uD83D\\uDCDD\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"No Forms Found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"No custom forms have been created yet. Use the Form Builder to create your first form.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"forms-container\",\n        children: Object.entries(groupedForms).map(([divisionKey, division]) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"division-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"division-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: [\"\\uD83C\\uDFE2 \", division.name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"forms-count\",\n              children: [Object.values(division.categories).reduce((total, cat) => total + cat.forms.length, 0), \" form(s)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 17\n          }, this), Object.entries(division.categories).map(([categoryKey, category]) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"category-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"category-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: [\"\\uD83D\\uDCC2 \", category.name]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"category-forms-count\",\n                children: [category.forms.length, \" form(s)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"forms-list\",\n              children: category.forms.map(form => {\n                var _form$fields;\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-list-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"form-info\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"form-name-section\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                        className: \"form-name\",\n                        children: form.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 250,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `type-badge ${form.type}`,\n                        children: form.type === 'category' ? 'Category' : form.type === 'subcategory' ? 'SubCategory' : 'Custom'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 251,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 249,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"form-description\",\n                      children: form.description || 'No description'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 257,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"form-meta\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"meta-item\",\n                        children: [\"\\uD83D\\uDCDD \", ((_form$fields = form.fields) === null || _form$fields === void 0 ? void 0 : _form$fields.length) || 0, \" fields\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 259,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"meta-item\",\n                        children: [\"\\uD83D\\uDCC5 \", formatDate(form.updatedAt)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 260,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 258,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 248,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"form-actions\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => handleEditForm(form),\n                      className: \"btn-action edit\",\n                      title: \"Edit form\",\n                      children: \"\\u270F\\uFE0F Edit\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 264,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => handleDeleteForm(form),\n                      className: \"btn-action delete\",\n                      title: \"Delete form\",\n                      children: \"\\uD83D\\uDDD1\\uFE0F Delete\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 271,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 263,\n                    columnNumber: 27\n                  }, this)]\n                }, form.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 25\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 21\n            }, this)]\n          }, categoryKey, true, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 19\n          }, this))]\n        }, divisionKey, true, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 213,\n      columnNumber: 7\n    }, this), deleteConfirm && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"delete-confirm-modal\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"delete-confirm-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"delete-confirm-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Confirm Delete\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"delete-confirm-body\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"Are you sure you want to delete the form \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: [\"\\\"\", deleteConfirm.name, \"\\\"\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 59\n            }, this), \"?\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"warning-text\",\n            children: \"This action cannot be undone.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"delete-confirm-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: cancelDelete,\n            className: \"btn-cancel\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: confirmDelete,\n            className: \"btn-delete\",\n            children: \"Delete\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 292,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 207,\n    columnNumber: 5\n  }, this);\n  return isInline ? /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"all-forms-inline\",\n    children: content\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 312,\n    columnNumber: 5\n  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"all-forms-modal\",\n    children: content\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 316,\n    columnNumber: 5\n  }, this);\n};\n_s(AllFormsModal, \"RQ+hg0Bxf6zGnwZ1c4ZpKa9CSY4=\");\n_c = AllFormsModal;\nexport default AllFormsModal;\nvar _c;\n$RefreshReg$(_c, \"AllFormsModal\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "formConfigService", "apiService", "jsxDEV", "_jsxDEV", "AllFormsModal", "onClose", "onEditForm", "onDeleteForm", "isInline", "_s", "forms", "setForms", "divisions", "setDivisions", "categories", "setCategories", "loading", "setLoading", "error", "setError", "deleteConfirm", "setDeleteConfirm", "loadData", "divisionsResponse", "categoriesResponse", "Promise", "all", "getDivisions", "getCategories", "data", "console", "log", "Object", "keys", "localStorage", "filter", "key", "includes", "allForms", "getAllFormConfigs", "length", "_divisionsResponse$da", "_divisionsResponse$da2", "_categoriesResponse$d", "_categoriesResponse$d2", "sampleForm", "name", "description", "fields", "label", "type", "required", "hierarchy", "divisionId", "id", "categoryId", "createdAt", "Date", "toISOString", "updatedAt", "_categoriesResponse$d3", "_categoriesResponse$d4", "saveFormConfig", "updatedForms", "map", "form", "_divisionsResponse$da3", "_divisionsResponse$da4", "_categoriesResponse$d5", "_categoriesResponse$d6", "_form$hierarchy3", "_form$hierarchy4", "divisionName", "find", "d", "_form$hierarchy", "categoryName", "c", "_form$hierarchy2", "enrichedForms", "_form$hierarchy5", "_form$hierarchy6", "_form$hierarchy7", "_form$hierarchy8", "_divisionsResponse$da5", "division", "_categoriesResponse$d7", "category", "err", "handleDeleteForm", "confirmDelete", "deleteFormConfig", "associatedId", "f", "cancelDelete", "handleEditForm", "groupedForms", "reduce", "acc", "divisionKey", "categoryKey", "push", "formatDate", "dateString", "toLocaleDateString", "content", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "entries", "values", "total", "cat", "_form$fields", "title", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/components/forms/AllFormsModal.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport formConfigService from '../../services/formConfigService';\nimport apiService from '../../services/apiService';\nimport './AllFormsModal.css';\n\nconst AllFormsModal = ({ onClose, onEditForm, onDeleteForm, isInline = false }) => {\n  const [forms, setForms] = useState([]);\n  const [divisions, setDivisions] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [deleteConfirm, setDeleteConfirm] = useState(null);\n\n  useEffect(() => {\n    loadData();\n  }, []);\n\n  const loadData = async () => {\n    setLoading(true);\n    setError(null);\n    \n    try {\n      // Load divisions and categories\n      const [divisionsResponse, categoriesResponse] = await Promise.all([\n        apiService.getDivisions(),\n        apiService.getCategories()\n      ]);\n\n      setDivisions(divisionsResponse.data || []);\n      setCategories(categoriesResponse.data || []);\n\n      // Load all forms from localStorage\n      console.log('Checking localStorage for forms...');\n      console.log('All localStorage keys:', Object.keys(localStorage));\n      console.log('Form-related keys:', Object.keys(localStorage).filter(key => key.includes('crm_form_config')));\n\n      const allForms = formConfigService.getAllFormConfigs();\n      console.log('Loaded forms from localStorage:', allForms);\n\n      // If no forms exist, create a sample form for testing\n      if (allForms.length === 0) {\n        console.log('No forms found, creating sample form for testing...');\n        const sampleForm = {\n          name: 'Sample Employee Form',\n          description: 'A sample form for testing',\n          fields: [\n            { name: 'firstName', label: 'First Name', type: 'text', required: true },\n            { name: 'lastName', label: 'Last Name', type: 'text', required: true },\n            { name: 'email', label: 'Email', type: 'email', required: true }\n          ],\n          hierarchy: {\n            divisionId: divisionsResponse.data?.[0]?.id || 1,\n            categoryId: categoriesResponse.data?.[0]?.id || 1\n          },\n          type: 'category',\n          createdAt: new Date().toISOString(),\n          updatedAt: new Date().toISOString()\n        };\n\n        try {\n          formConfigService.saveFormConfig('category', categoriesResponse.data?.[0]?.id || 1, sampleForm);\n          console.log('Sample form created successfully');\n          // Reload forms after creating sample\n          const updatedForms = formConfigService.getAllFormConfigs();\n          console.log('Updated forms list:', updatedForms);\n          setForms(updatedForms.map(form => ({\n            ...form,\n            divisionName: divisionsResponse.data?.find(d => d.id === form.hierarchy?.divisionId)?.name || 'General',\n            categoryName: categoriesResponse.data?.find(c => c.id === form.hierarchy?.categoryId)?.name || 'Default',\n            divisionId: form.hierarchy?.divisionId || null,\n            categoryId: form.hierarchy?.categoryId || null\n          })));\n          setLoading(false);\n          return;\n        } catch (error) {\n          console.error('Error creating sample form:', error);\n        }\n      }\n\n      // Enrich forms with division and category information\n      const enrichedForms = allForms.map(form => {\n        let divisionName = 'General';\n        let categoryName = 'Default';\n\n        if (form.hierarchy?.divisionId) {\n          const division = divisionsResponse.data?.find(d => d.id === form.hierarchy.divisionId);\n          if (division) {\n            divisionName = division.name;\n          }\n        }\n\n        if (form.hierarchy?.categoryId) {\n          const category = categoriesResponse.data?.find(c => c.id === form.hierarchy.categoryId);\n          if (category) {\n            categoryName = category.name;\n          }\n        }\n\n        return {\n          ...form,\n          divisionName,\n          categoryName,\n          divisionId: form.hierarchy?.divisionId || null,\n          categoryId: form.hierarchy?.categoryId || null\n        };\n      });\n\n      console.log('Enriched forms:', enrichedForms);\n      setForms(enrichedForms);\n    } catch (err) {\n      console.error('Error loading forms data:', err);\n      setError('Failed to load forms data. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDeleteForm = (form) => {\n    setDeleteConfirm(form);\n  };\n\n  const confirmDelete = () => {\n    if (deleteConfirm) {\n      try {\n        formConfigService.deleteFormConfig(deleteConfirm.type, deleteConfirm.associatedId);\n        setForms(forms.filter(f => f.id !== deleteConfirm.id));\n        setDeleteConfirm(null);\n        if (onDeleteForm) {\n          onDeleteForm(deleteConfirm);\n        }\n      } catch (err) {\n        console.error('Error deleting form:', err);\n        setError('Failed to delete form. Please try again.');\n      }\n    }\n  };\n\n  const cancelDelete = () => {\n    setDeleteConfirm(null);\n  };\n\n  const handleEditForm = (form) => {\n    if (onEditForm) {\n      onEditForm(form);\n    }\n    // Don't call onClose() here - let the parent component handle navigation\n    // onClose() was causing the component to return to person list instead of form builder\n  };\n\n  // Group forms by division and then by category\n  const groupedForms = forms.reduce((acc, form) => {\n    const divisionKey = form.divisionId || 'general';\n    const categoryKey = form.categoryId || 'default';\n    \n    if (!acc[divisionKey]) {\n      acc[divisionKey] = {\n        name: form.divisionName,\n        categories: {}\n      };\n    }\n    \n    if (!acc[divisionKey].categories[categoryKey]) {\n      acc[divisionKey].categories[categoryKey] = {\n        name: form.categoryName,\n        forms: []\n      };\n    }\n    \n    acc[divisionKey].categories[categoryKey].forms.push(form);\n    \n    return acc;\n  }, {});\n\n  const formatDate = (dateString) => {\n    if (!dateString) return 'N/A';\n    return new Date(dateString).toLocaleDateString();\n  };\n\n  if (loading) {\n    const content = (\n      <div className=\"modal-content\">\n        <div className=\"modal-header\">\n          <h2>All Forms</h2>\n          <button onClick={onClose} className=\"close-button\">×</button>\n        </div>\n        <div className=\"modal-body\">\n          <div className=\"loading-state\">\n            <div className=\"spinner\"></div>\n            <p>Loading forms...</p>\n          </div>\n        </div>\n      </div>\n    );\n\n    return isInline ? (\n      <div className=\"all-forms-inline\">\n        {content}\n      </div>\n    ) : (\n      <div className=\"all-forms-modal\">\n        {content}\n      </div>\n    );\n  }\n\n  const content = (\n    <div className=\"modal-content\">\n      <div className=\"modal-header\">\n        <h2>All Forms</h2>\n        <button onClick={onClose} className=\"close-button\">×</button>\n      </div>\n\n      <div className=\"modal-body\">\n        {error && (\n          <div className=\"error-message\">\n            <span className=\"error-icon\">⚠️</span>\n            <span>{error}</span>\n          </div>\n        )}\n\n        {forms.length === 0 ? (\n          <div className=\"empty-state\">\n            <div className=\"empty-icon\">📝</div>\n            <h3>No Forms Found</h3>\n            <p>No custom forms have been created yet. Use the Form Builder to create your first form.</p>\n          </div>\n        ) : (\n          <div className=\"forms-container\">\n            {Object.entries(groupedForms).map(([divisionKey, division]) => (\n              <div key={divisionKey} className=\"division-section\">\n                <div className=\"division-header\">\n                  <h3>🏢 {division.name}</h3>\n                  <span className=\"forms-count\">\n                    {Object.values(division.categories).reduce((total, cat) => total + cat.forms.length, 0)} form(s)\n                  </span>\n                </div>\n\n                {Object.entries(division.categories).map(([categoryKey, category]) => (\n                  <div key={categoryKey} className=\"category-section\">\n                    <div className=\"category-header\">\n                      <h4>📂 {category.name}</h4>\n                      <span className=\"category-forms-count\">{category.forms.length} form(s)</span>\n                    </div>\n\n                    <div className=\"forms-list\">\n                      {category.forms.map(form => (\n                        <div key={form.id} className=\"form-list-item\">\n                          <div className=\"form-info\">\n                            <div className=\"form-name-section\">\n                              <h5 className=\"form-name\">{form.name}</h5>\n                              <span className={`type-badge ${form.type}`}>\n                                {form.type === 'category' ? 'Category' :\n                                 form.type === 'subcategory' ? 'SubCategory' :\n                                 'Custom'}\n                              </span>\n                            </div>\n                            <p className=\"form-description\">{form.description || 'No description'}</p>\n                            <div className=\"form-meta\">\n                              <span className=\"meta-item\">📝 {form.fields?.length || 0} fields</span>\n                              <span className=\"meta-item\">📅 {formatDate(form.updatedAt)}</span>\n                            </div>\n                          </div>\n                          <div className=\"form-actions\">\n                            <button\n                              onClick={() => handleEditForm(form)}\n                              className=\"btn-action edit\"\n                              title=\"Edit form\"\n                            >\n                              ✏️ Edit\n                            </button>\n                            <button\n                              onClick={() => handleDeleteForm(form)}\n                              className=\"btn-action delete\"\n                              title=\"Delete form\"\n                            >\n                              🗑️ Delete\n                            </button>\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n                ))}\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n\n      {/* Delete Confirmation Modal */}\n      {deleteConfirm && (\n        <div className=\"delete-confirm-modal\">\n          <div className=\"delete-confirm-content\">\n            <div className=\"delete-confirm-header\">\n              <h3>Confirm Delete</h3>\n            </div>\n            <div className=\"delete-confirm-body\">\n              <p>Are you sure you want to delete the form <strong>\"{deleteConfirm.name}\"</strong>?</p>\n              <p className=\"warning-text\">This action cannot be undone.</p>\n            </div>\n            <div className=\"delete-confirm-actions\">\n              <button onClick={cancelDelete} className=\"btn-cancel\">Cancel</button>\n              <button onClick={confirmDelete} className=\"btn-delete\">Delete</button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n\n  return isInline ? (\n    <div className=\"all-forms-inline\">\n      {content}\n    </div>\n  ) : (\n    <div className=\"all-forms-modal\">\n      {content}\n    </div>\n  );\n};\n\nexport default AllFormsModal;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,iBAAiB,MAAM,kCAAkC;AAChE,OAAOC,UAAU,MAAM,2BAA2B;AAClD,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,aAAa,GAAGA,CAAC;EAAEC,OAAO;EAAEC,UAAU;EAAEC,YAAY;EAAEC,QAAQ,GAAG;AAAM,CAAC,KAAK;EAAAC,EAAA;EACjF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACc,SAAS,EAAEC,YAAY,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACgB,UAAU,EAAEC,aAAa,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoB,KAAK,EAAEC,QAAQ,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACsB,aAAa,EAAEC,gBAAgB,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAExDC,SAAS,CAAC,MAAM;IACduB,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3BL,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF;MACA,MAAM,CAACI,iBAAiB,EAAEC,kBAAkB,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAChEzB,UAAU,CAAC0B,YAAY,CAAC,CAAC,EACzB1B,UAAU,CAAC2B,aAAa,CAAC,CAAC,CAC3B,CAAC;MAEFf,YAAY,CAACU,iBAAiB,CAACM,IAAI,IAAI,EAAE,CAAC;MAC1Cd,aAAa,CAACS,kBAAkB,CAACK,IAAI,IAAI,EAAE,CAAC;;MAE5C;MACAC,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;MACjDD,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEC,MAAM,CAACC,IAAI,CAACC,YAAY,CAAC,CAAC;MAChEJ,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEC,MAAM,CAACC,IAAI,CAACC,YAAY,CAAC,CAACC,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACC,QAAQ,CAAC,iBAAiB,CAAC,CAAC,CAAC;MAE3G,MAAMC,QAAQ,GAAGtC,iBAAiB,CAACuC,iBAAiB,CAAC,CAAC;MACtDT,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEO,QAAQ,CAAC;;MAExD;MACA,IAAIA,QAAQ,CAACE,MAAM,KAAK,CAAC,EAAE;QAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA;QACzBd,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;QAClE,MAAMc,UAAU,GAAG;UACjBC,IAAI,EAAE,sBAAsB;UAC5BC,WAAW,EAAE,2BAA2B;UACxCC,MAAM,EAAE,CACN;YAAEF,IAAI,EAAE,WAAW;YAAEG,KAAK,EAAE,YAAY;YAAEC,IAAI,EAAE,MAAM;YAAEC,QAAQ,EAAE;UAAK,CAAC,EACxE;YAAEL,IAAI,EAAE,UAAU;YAAEG,KAAK,EAAE,WAAW;YAAEC,IAAI,EAAE,MAAM;YAAEC,QAAQ,EAAE;UAAK,CAAC,EACtE;YAAEL,IAAI,EAAE,OAAO;YAAEG,KAAK,EAAE,OAAO;YAAEC,IAAI,EAAE,OAAO;YAAEC,QAAQ,EAAE;UAAK,CAAC,CACjE;UACDC,SAAS,EAAE;YACTC,UAAU,EAAE,EAAAZ,qBAAA,GAAAlB,iBAAiB,CAACM,IAAI,cAAAY,qBAAA,wBAAAC,sBAAA,GAAtBD,qBAAA,CAAyB,CAAC,CAAC,cAAAC,sBAAA,uBAA3BA,sBAAA,CAA6BY,EAAE,KAAI,CAAC;YAChDC,UAAU,EAAE,EAAAZ,qBAAA,GAAAnB,kBAAkB,CAACK,IAAI,cAAAc,qBAAA,wBAAAC,sBAAA,GAAvBD,qBAAA,CAA0B,CAAC,CAAC,cAAAC,sBAAA,uBAA5BA,sBAAA,CAA8BU,EAAE,KAAI;UAClD,CAAC;UACDJ,IAAI,EAAE,UAAU;UAChBM,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;UACnCC,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;QACpC,CAAC;QAED,IAAI;UAAA,IAAAE,sBAAA,EAAAC,sBAAA;UACF7D,iBAAiB,CAAC8D,cAAc,CAAC,UAAU,EAAE,EAAAF,sBAAA,GAAApC,kBAAkB,CAACK,IAAI,cAAA+B,sBAAA,wBAAAC,sBAAA,GAAvBD,sBAAA,CAA0B,CAAC,CAAC,cAAAC,sBAAA,uBAA5BA,sBAAA,CAA8BP,EAAE,KAAI,CAAC,EAAET,UAAU,CAAC;UAC/Ff,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;UAC/C;UACA,MAAMgC,YAAY,GAAG/D,iBAAiB,CAACuC,iBAAiB,CAAC,CAAC;UAC1DT,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEgC,YAAY,CAAC;UAChDpD,QAAQ,CAACoD,YAAY,CAACC,GAAG,CAACC,IAAI;YAAA,IAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,gBAAA,EAAAC,gBAAA;YAAA,OAAK;cACjC,GAAGN,IAAI;cACPO,YAAY,EAAE,EAAAN,sBAAA,GAAA3C,iBAAiB,CAACM,IAAI,cAAAqC,sBAAA,wBAAAC,sBAAA,GAAtBD,sBAAA,CAAwBO,IAAI,CAACC,CAAC;gBAAA,IAAAC,eAAA;gBAAA,OAAID,CAAC,CAACpB,EAAE,OAAAqB,eAAA,GAAKV,IAAI,CAACb,SAAS,cAAAuB,eAAA,uBAAdA,eAAA,CAAgBtB,UAAU;cAAA,EAAC,cAAAc,sBAAA,uBAAtEA,sBAAA,CAAwErB,IAAI,KAAI,SAAS;cACvG8B,YAAY,EAAE,EAAAR,sBAAA,GAAA5C,kBAAkB,CAACK,IAAI,cAAAuC,sBAAA,wBAAAC,sBAAA,GAAvBD,sBAAA,CAAyBK,IAAI,CAACI,CAAC;gBAAA,IAAAC,gBAAA;gBAAA,OAAID,CAAC,CAACvB,EAAE,OAAAwB,gBAAA,GAAKb,IAAI,CAACb,SAAS,cAAA0B,gBAAA,uBAAdA,gBAAA,CAAgBvB,UAAU;cAAA,EAAC,cAAAc,sBAAA,uBAAvEA,sBAAA,CAAyEvB,IAAI,KAAI,SAAS;cACxGO,UAAU,EAAE,EAAAiB,gBAAA,GAAAL,IAAI,CAACb,SAAS,cAAAkB,gBAAA,uBAAdA,gBAAA,CAAgBjB,UAAU,KAAI,IAAI;cAC9CE,UAAU,EAAE,EAAAgB,gBAAA,GAAAN,IAAI,CAACb,SAAS,cAAAmB,gBAAA,uBAAdA,gBAAA,CAAgBhB,UAAU,KAAI;YAC5C,CAAC;UAAA,CAAC,CAAC,CAAC;UACJtC,UAAU,CAAC,KAAK,CAAC;UACjB;QACF,CAAC,CAAC,OAAOC,KAAK,EAAE;UACdY,OAAO,CAACZ,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACrD;MACF;;MAEA;MACA,MAAM6D,aAAa,GAAGzC,QAAQ,CAAC0B,GAAG,CAACC,IAAI,IAAI;QAAA,IAAAe,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA;QACzC,IAAIX,YAAY,GAAG,SAAS;QAC5B,IAAII,YAAY,GAAG,SAAS;QAE5B,KAAAI,gBAAA,GAAIf,IAAI,CAACb,SAAS,cAAA4B,gBAAA,eAAdA,gBAAA,CAAgB3B,UAAU,EAAE;UAAA,IAAA+B,sBAAA;UAC9B,MAAMC,QAAQ,IAAAD,sBAAA,GAAG7D,iBAAiB,CAACM,IAAI,cAAAuD,sBAAA,uBAAtBA,sBAAA,CAAwBX,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACpB,EAAE,KAAKW,IAAI,CAACb,SAAS,CAACC,UAAU,CAAC;UACtF,IAAIgC,QAAQ,EAAE;YACZb,YAAY,GAAGa,QAAQ,CAACvC,IAAI;UAC9B;QACF;QAEA,KAAAmC,gBAAA,GAAIhB,IAAI,CAACb,SAAS,cAAA6B,gBAAA,eAAdA,gBAAA,CAAgB1B,UAAU,EAAE;UAAA,IAAA+B,sBAAA;UAC9B,MAAMC,QAAQ,IAAAD,sBAAA,GAAG9D,kBAAkB,CAACK,IAAI,cAAAyD,sBAAA,uBAAvBA,sBAAA,CAAyBb,IAAI,CAACI,CAAC,IAAIA,CAAC,CAACvB,EAAE,KAAKW,IAAI,CAACb,SAAS,CAACG,UAAU,CAAC;UACvF,IAAIgC,QAAQ,EAAE;YACZX,YAAY,GAAGW,QAAQ,CAACzC,IAAI;UAC9B;QACF;QAEA,OAAO;UACL,GAAGmB,IAAI;UACPO,YAAY;UACZI,YAAY;UACZvB,UAAU,EAAE,EAAA6B,gBAAA,GAAAjB,IAAI,CAACb,SAAS,cAAA8B,gBAAA,uBAAdA,gBAAA,CAAgB7B,UAAU,KAAI,IAAI;UAC9CE,UAAU,EAAE,EAAA4B,gBAAA,GAAAlB,IAAI,CAACb,SAAS,cAAA+B,gBAAA,uBAAdA,gBAAA,CAAgB5B,UAAU,KAAI;QAC5C,CAAC;MACH,CAAC,CAAC;MAEFzB,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEgD,aAAa,CAAC;MAC7CpE,QAAQ,CAACoE,aAAa,CAAC;IACzB,CAAC,CAAC,OAAOS,GAAG,EAAE;MACZ1D,OAAO,CAACZ,KAAK,CAAC,2BAA2B,EAAEsE,GAAG,CAAC;MAC/CrE,QAAQ,CAAC,8CAA8C,CAAC;IAC1D,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMwE,gBAAgB,GAAIxB,IAAI,IAAK;IACjC5C,gBAAgB,CAAC4C,IAAI,CAAC;EACxB,CAAC;EAED,MAAMyB,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAItE,aAAa,EAAE;MACjB,IAAI;QACFpB,iBAAiB,CAAC2F,gBAAgB,CAACvE,aAAa,CAAC8B,IAAI,EAAE9B,aAAa,CAACwE,YAAY,CAAC;QAClFjF,QAAQ,CAACD,KAAK,CAACyB,MAAM,CAAC0D,CAAC,IAAIA,CAAC,CAACvC,EAAE,KAAKlC,aAAa,CAACkC,EAAE,CAAC,CAAC;QACtDjC,gBAAgB,CAAC,IAAI,CAAC;QACtB,IAAId,YAAY,EAAE;UAChBA,YAAY,CAACa,aAAa,CAAC;QAC7B;MACF,CAAC,CAAC,OAAOoE,GAAG,EAAE;QACZ1D,OAAO,CAACZ,KAAK,CAAC,sBAAsB,EAAEsE,GAAG,CAAC;QAC1CrE,QAAQ,CAAC,0CAA0C,CAAC;MACtD;IACF;EACF,CAAC;EAED,MAAM2E,YAAY,GAAGA,CAAA,KAAM;IACzBzE,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAM0E,cAAc,GAAI9B,IAAI,IAAK;IAC/B,IAAI3D,UAAU,EAAE;MACdA,UAAU,CAAC2D,IAAI,CAAC;IAClB;IACA;IACA;EACF,CAAC;;EAED;EACA,MAAM+B,YAAY,GAAGtF,KAAK,CAACuF,MAAM,CAAC,CAACC,GAAG,EAAEjC,IAAI,KAAK;IAC/C,MAAMkC,WAAW,GAAGlC,IAAI,CAACZ,UAAU,IAAI,SAAS;IAChD,MAAM+C,WAAW,GAAGnC,IAAI,CAACV,UAAU,IAAI,SAAS;IAEhD,IAAI,CAAC2C,GAAG,CAACC,WAAW,CAAC,EAAE;MACrBD,GAAG,CAACC,WAAW,CAAC,GAAG;QACjBrD,IAAI,EAAEmB,IAAI,CAACO,YAAY;QACvB1D,UAAU,EAAE,CAAC;MACf,CAAC;IACH;IAEA,IAAI,CAACoF,GAAG,CAACC,WAAW,CAAC,CAACrF,UAAU,CAACsF,WAAW,CAAC,EAAE;MAC7CF,GAAG,CAACC,WAAW,CAAC,CAACrF,UAAU,CAACsF,WAAW,CAAC,GAAG;QACzCtD,IAAI,EAAEmB,IAAI,CAACW,YAAY;QACvBlE,KAAK,EAAE;MACT,CAAC;IACH;IAEAwF,GAAG,CAACC,WAAW,CAAC,CAACrF,UAAU,CAACsF,WAAW,CAAC,CAAC1F,KAAK,CAAC2F,IAAI,CAACpC,IAAI,CAAC;IAEzD,OAAOiC,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;EAEN,MAAMI,UAAU,GAAIC,UAAU,IAAK;IACjC,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;IAC7B,OAAO,IAAI9C,IAAI,CAAC8C,UAAU,CAAC,CAACC,kBAAkB,CAAC,CAAC;EAClD,CAAC;EAED,IAAIxF,OAAO,EAAE;IACX,MAAMyF,OAAO,gBACXtG,OAAA;MAAKuG,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BxG,OAAA;QAAKuG,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BxG,OAAA;UAAAwG,QAAA,EAAI;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClB5G,OAAA;UAAQ6G,OAAO,EAAE3G,OAAQ;UAACqG,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,eACN5G,OAAA;QAAKuG,SAAS,EAAC,YAAY;QAAAC,QAAA,eACzBxG,OAAA;UAAKuG,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BxG,OAAA;YAAKuG,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/B5G,OAAA;YAAAwG,QAAA,EAAG;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;IAED,OAAOvG,QAAQ,gBACbL,OAAA;MAAKuG,SAAS,EAAC,kBAAkB;MAAAC,QAAA,EAC9BF;IAAO;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,gBAEN5G,OAAA;MAAKuG,SAAS,EAAC,iBAAiB;MAAAC,QAAA,EAC7BF;IAAO;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN;EACH;EAEA,MAAMN,OAAO,gBACXtG,OAAA;IAAKuG,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAC5BxG,OAAA;MAAKuG,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3BxG,OAAA;QAAAwG,QAAA,EAAI;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAClB5G,OAAA;QAAQ6G,OAAO,EAAE3G,OAAQ;QAACqG,SAAS,EAAC,cAAc;QAAAC,QAAA,EAAC;MAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1D,CAAC,eAEN5G,OAAA;MAAKuG,SAAS,EAAC,YAAY;MAAAC,QAAA,GACxBzF,KAAK,iBACJf,OAAA;QAAKuG,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BxG,OAAA;UAAMuG,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtC5G,OAAA;UAAAwG,QAAA,EAAOzF;QAAK;UAAA0F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CACN,EAEArG,KAAK,CAAC8B,MAAM,KAAK,CAAC,gBACjBrC,OAAA;QAAKuG,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BxG,OAAA;UAAKuG,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACpC5G,OAAA;UAAAwG,QAAA,EAAI;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvB5G,OAAA;UAAAwG,QAAA,EAAG;QAAsF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1F,CAAC,gBAEN5G,OAAA;QAAKuG,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAC7B3E,MAAM,CAACiF,OAAO,CAACjB,YAAY,CAAC,CAAChC,GAAG,CAAC,CAAC,CAACmC,WAAW,EAAEd,QAAQ,CAAC,kBACxDlF,OAAA;UAAuBuG,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBACjDxG,OAAA;YAAKuG,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BxG,OAAA;cAAAwG,QAAA,GAAI,eAAG,EAACtB,QAAQ,CAACvC,IAAI;YAAA;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC3B5G,OAAA;cAAMuG,SAAS,EAAC,aAAa;cAAAC,QAAA,GAC1B3E,MAAM,CAACkF,MAAM,CAAC7B,QAAQ,CAACvE,UAAU,CAAC,CAACmF,MAAM,CAAC,CAACkB,KAAK,EAAEC,GAAG,KAAKD,KAAK,GAAGC,GAAG,CAAC1G,KAAK,CAAC8B,MAAM,EAAE,CAAC,CAAC,EAAC,UAC1F;YAAA;cAAAoE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,EAEL/E,MAAM,CAACiF,OAAO,CAAC5B,QAAQ,CAACvE,UAAU,CAAC,CAACkD,GAAG,CAAC,CAAC,CAACoC,WAAW,EAAEb,QAAQ,CAAC,kBAC/DpF,OAAA;YAAuBuG,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBACjDxG,OAAA;cAAKuG,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BxG,OAAA;gBAAAwG,QAAA,GAAI,eAAG,EAACpB,QAAQ,CAACzC,IAAI;cAAA;gBAAA8D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC3B5G,OAAA;gBAAMuG,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,GAAEpB,QAAQ,CAAC7E,KAAK,CAAC8B,MAAM,EAAC,UAAQ;cAAA;gBAAAoE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1E,CAAC,eAEN5G,OAAA;cAAKuG,SAAS,EAAC,YAAY;cAAAC,QAAA,EACxBpB,QAAQ,CAAC7E,KAAK,CAACsD,GAAG,CAACC,IAAI;gBAAA,IAAAoD,YAAA;gBAAA,oBACtBlH,OAAA;kBAAmBuG,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC3CxG,OAAA;oBAAKuG,SAAS,EAAC,WAAW;oBAAAC,QAAA,gBACxBxG,OAAA;sBAAKuG,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,gBAChCxG,OAAA;wBAAIuG,SAAS,EAAC,WAAW;wBAAAC,QAAA,EAAE1C,IAAI,CAACnB;sBAAI;wBAAA8D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC1C5G,OAAA;wBAAMuG,SAAS,EAAE,cAAczC,IAAI,CAACf,IAAI,EAAG;wBAAAyD,QAAA,EACxC1C,IAAI,CAACf,IAAI,KAAK,UAAU,GAAG,UAAU,GACrCe,IAAI,CAACf,IAAI,KAAK,aAAa,GAAG,aAAa,GAC3C;sBAAQ;wBAAA0D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACN5G,OAAA;sBAAGuG,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAE1C,IAAI,CAAClB,WAAW,IAAI;oBAAgB;sBAAA6D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC1E5G,OAAA;sBAAKuG,SAAS,EAAC,WAAW;sBAAAC,QAAA,gBACxBxG,OAAA;wBAAMuG,SAAS,EAAC,WAAW;wBAAAC,QAAA,GAAC,eAAG,EAAC,EAAAU,YAAA,GAAApD,IAAI,CAACjB,MAAM,cAAAqE,YAAA,uBAAXA,YAAA,CAAa7E,MAAM,KAAI,CAAC,EAAC,SAAO;sBAAA;wBAAAoE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACvE5G,OAAA;wBAAMuG,SAAS,EAAC,WAAW;wBAAAC,QAAA,GAAC,eAAG,EAACL,UAAU,CAACrC,IAAI,CAACN,SAAS,CAAC;sBAAA;wBAAAiD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/D,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN5G,OAAA;oBAAKuG,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBAC3BxG,OAAA;sBACE6G,OAAO,EAAEA,CAAA,KAAMjB,cAAc,CAAC9B,IAAI,CAAE;sBACpCyC,SAAS,EAAC,iBAAiB;sBAC3BY,KAAK,EAAC,WAAW;sBAAAX,QAAA,EAClB;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACT5G,OAAA;sBACE6G,OAAO,EAAEA,CAAA,KAAMvB,gBAAgB,CAACxB,IAAI,CAAE;sBACtCyC,SAAS,EAAC,mBAAmB;sBAC7BY,KAAK,EAAC,aAAa;sBAAAX,QAAA,EACpB;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA,GA/BE9C,IAAI,CAACX,EAAE;kBAAAsD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAgCZ,CAAC;cAAA,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA,GA1CEX,WAAW;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA2ChB,CACN,CAAC;QAAA,GArDMZ,WAAW;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAsDhB,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGL3F,aAAa,iBACZjB,OAAA;MAAKuG,SAAS,EAAC,sBAAsB;MAAAC,QAAA,eACnCxG,OAAA;QAAKuG,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrCxG,OAAA;UAAKuG,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eACpCxG,OAAA;YAAAwG,QAAA,EAAI;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eACN5G,OAAA;UAAKuG,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAClCxG,OAAA;YAAAwG,QAAA,GAAG,2CAAyC,eAAAxG,OAAA;cAAAwG,QAAA,GAAQ,IAAC,EAACvF,aAAa,CAAC0B,IAAI,EAAC,IAAC;YAAA;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACxF5G,OAAA;YAAGuG,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAA6B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC,eACN5G,OAAA;UAAKuG,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrCxG,OAAA;YAAQ6G,OAAO,EAAElB,YAAa;YAACY,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACrE5G,OAAA;YAAQ6G,OAAO,EAAEtB,aAAc;YAACgB,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACN;EAED,OAAOvG,QAAQ,gBACbL,OAAA;IAAKuG,SAAS,EAAC,kBAAkB;IAAAC,QAAA,EAC9BF;EAAO;IAAAG,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC,gBAEN5G,OAAA;IAAKuG,SAAS,EAAC,iBAAiB;IAAAC,QAAA,EAC7BF;EAAO;IAAAG,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CACN;AACH,CAAC;AAACtG,EAAA,CA1TIL,aAAa;AAAAmH,EAAA,GAAbnH,aAAa;AA4TnB,eAAeA,aAAa;AAAC,IAAAmH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}