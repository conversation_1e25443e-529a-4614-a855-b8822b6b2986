{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_helperCompilationTargets", "_helperAnnotateAsPure", "_core", "_transformClass", "globalsBrowserUpper", "globalsBuiltinUpper", "builtinClasses", "Set", "delete", "_default", "exports", "default", "declare", "api", "options", "_api$assumption", "_api$assumption2", "_api$assumption3", "_api$assumption4", "assertVersion", "loose", "setClassMethods", "assumption", "constant<PERSON>uper", "superIsCallableConstructor", "noClassCalls", "supportUnicodeId", "isRequired", "targets", "VISITED", "WeakSet", "name", "visitor", "ExportDefaultDeclaration", "path", "get", "isClassDeclaration", "_path$splitExportDecl", "splitExportDeclaration", "NodePath", "prototype", "ClassDeclaration", "node", "ref", "id", "t", "cloneNode", "scope", "generateUidIdentifier", "replaceWith", "variableDeclaration", "variableDeclarator", "toExpression", "ClassExpression", "state", "has", "_path$ensureFunctionN", "ensureFunctionName", "replacement", "add", "<PERSON><PERSON><PERSON>", "transformClass", "file", "isCallExpression", "annotateAsPure", "callee", "isArrowFunctionExpression", "arrowFunctionToExpression"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport { isRequired } from \"@babel/helper-compilation-targets\";\nimport annotateAsPure from \"@babel/helper-annotate-as-pure\";\nimport { types as t } from \"@babel/core\";\nimport globalsBrowserUpper from \"@babel/helper-globals/data/browser-upper.json\" with { type: \"json\" };\nimport globalsBuiltinUpper from \"@babel/helper-globals/data/builtin-upper.json\" with { type: \"json\" };\nimport transformClass from \"./transformClass.ts\";\n\nconst builtinClasses = new Set([\n  ...globalsBrowserUpper,\n  ...globalsBuiltinUpper,\n]);\n\n// The \"Iterator\" global is removed because the Babel construct helper\n// packages/babel-helpers/src/helpers/construct.ts, emitted from the wrapNativeSuper helper,\n// // will invoke it with `new Iterator()` when native Reflect.construct is not available.\n// However, the abstract class Iterator can not be invoked with new. Since the `builtinClasses`\n// is used for the superIsCallableConstructor assumption, we should prioritize the spec mode\nbuiltinClasses.delete(\"Iterator\");\n\nexport interface Options {\n  loose?: boolean;\n}\n\nexport default declare((api, options: Options) => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  const { loose = false } = options;\n\n  const setClassMethods = api.assumption(\"setClassMethods\") ?? loose;\n  const constantSuper = api.assumption(\"constantSuper\") ?? loose;\n  const superIsCallableConstructor =\n    api.assumption(\"superIsCallableConstructor\") ?? loose;\n  const noClassCalls = api.assumption(\"noClassCalls\") ?? loose;\n  const supportUnicodeId = !isRequired(\n    \"transform-unicode-escapes\",\n    api.targets(),\n  );\n\n  // todo: investigate traversal requeueing\n  const VISITED = new WeakSet();\n\n  return {\n    name: \"transform-classes\",\n\n    visitor: {\n      ExportDefaultDeclaration(path) {\n        if (!path.get(\"declaration\").isClassDeclaration()) return;\n        if (!process.env.BABEL_8_BREAKING && !USE_ESM && !IS_STANDALONE) {\n          // polyfill when being run by an older Babel version\n          path.splitExportDeclaration ??=\n            // eslint-disable-next-line no-restricted-globals\n            require(\"@babel/traverse\").NodePath.prototype.splitExportDeclaration;\n        }\n        path.splitExportDeclaration();\n      },\n\n      ClassDeclaration(path) {\n        const { node } = path;\n\n        const ref = node.id\n          ? t.cloneNode(node.id)\n          : path.scope.generateUidIdentifier(\"class\");\n\n        path.replaceWith(\n          t.variableDeclaration(\"let\", [\n            t.variableDeclarator(ref, t.toExpression(node)),\n          ]),\n        );\n      },\n\n      ClassExpression(path, state) {\n        const { node } = path;\n        if (VISITED.has(node)) return;\n\n        if (!process.env.BABEL_8_BREAKING && !USE_ESM && !IS_STANDALONE) {\n          // polyfill when being run by an older Babel version\n          path.ensureFunctionName ??=\n            // eslint-disable-next-line no-restricted-globals\n            require(\"@babel/traverse\").NodePath.prototype.ensureFunctionName;\n        }\n        const replacement = path.ensureFunctionName(supportUnicodeId);\n        if (replacement && replacement.node !== node) return;\n\n        VISITED.add(node);\n\n        const [replacedPath] = path.replaceWith(\n          transformClass(\n            path,\n            state.file,\n            builtinClasses,\n            loose,\n            {\n              setClassMethods,\n              constantSuper,\n              superIsCallableConstructor,\n              noClassCalls,\n            },\n            supportUnicodeId,\n          ),\n        );\n\n        if (replacedPath.isCallExpression()) {\n          annotateAsPure(replacedPath);\n          const callee = replacedPath.get(\"callee\");\n          if (callee.isArrowFunctionExpression()) {\n            // This is an IIFE, so we don't need to worry about the noNewArrows assumption\n            callee.arrowFunctionToExpression();\n          }\n        }\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,yBAAA,GAAAD,OAAA;AACA,IAAAE,qBAAA,GAAAF,OAAA;AACA,IAAAG,KAAA,GAAAH,OAAA;AAGA,IAAAI,eAAA,GAAAJ,OAAA;AAAiD,MAF1CK,mBAAmB,GAAAL,OAAA,CAAM,+CAA+C;EACxEM,mBAAmB,GAAAN,OAAA,CAAM,+CAA+C;AAG/E,MAAMO,cAAc,GAAG,IAAIC,GAAG,CAAC,CAC7B,GAAGH,mBAAmB,EACtB,GAAGC,mBAAmB,CACvB,CAAC;AAOFC,cAAc,CAACE,MAAM,CAAC,UAAU,CAAC;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAMnB,IAAAC,0BAAO,EAAC,CAACC,GAAG,EAAEC,OAAgB,KAAK;EAAA,IAAAC,eAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA;EAChDL,GAAG,CAACM,aAAa,CAAkB,CAAE,CAAC;EAEtC,MAAM;IAAEC,KAAK,GAAG;EAAM,CAAC,GAAGN,OAAO;EAEjC,MAAMO,eAAe,IAAAN,eAAA,GAAGF,GAAG,CAACS,UAAU,CAAC,iBAAiB,CAAC,YAAAP,eAAA,GAAIK,KAAK;EAClE,MAAMG,aAAa,IAAAP,gBAAA,GAAGH,GAAG,CAACS,UAAU,CAAC,eAAe,CAAC,YAAAN,gBAAA,GAAII,KAAK;EAC9D,MAAMI,0BAA0B,IAAAP,gBAAA,GAC9BJ,GAAG,CAACS,UAAU,CAAC,4BAA4B,CAAC,YAAAL,gBAAA,GAAIG,KAAK;EACvD,MAAMK,YAAY,IAAAP,gBAAA,GAAGL,GAAG,CAACS,UAAU,CAAC,cAAc,CAAC,YAAAJ,gBAAA,GAAIE,KAAK;EAC5D,MAAMM,gBAAgB,GAAG,CAAC,IAAAC,oCAAU,EAClC,2BAA2B,EAC3Bd,GAAG,CAACe,OAAO,CAAC,CACd,CAAC;EAGD,MAAMC,OAAO,GAAG,IAAIC,OAAO,CAAC,CAAC;EAE7B,OAAO;IACLC,IAAI,EAAE,mBAAmB;IAEzBC,OAAO,EAAE;MACPC,wBAAwBA,CAACC,IAAI,EAAE;QAC7B,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,aAAa,CAAC,CAACC,kBAAkB,CAAC,CAAC,EAAE;QACc;UAAA,IAAAC,qBAAA;UAE/D,CAAAA,qBAAA,GAAAH,IAAI,CAACI,sBAAsB,YAAAD,qBAAA,GAA3BH,IAAI,CAACI,sBAAsB,GAEzBvC,OAAO,CAAC,iBAAiB,CAAC,CAACwC,QAAQ,CAACC,SAAS,CAACF,sBAAsB;QACxE;QACAJ,IAAI,CAACI,sBAAsB,CAAC,CAAC;MAC/B,CAAC;MAEDG,gBAAgBA,CAACP,IAAI,EAAE;QACrB,MAAM;UAAEQ;QAAK,CAAC,GAAGR,IAAI;QAErB,MAAMS,GAAG,GAAGD,IAAI,CAACE,EAAE,GACfC,WAAC,CAACC,SAAS,CAACJ,IAAI,CAACE,EAAE,CAAC,GACpBV,IAAI,CAACa,KAAK,CAACC,qBAAqB,CAAC,OAAO,CAAC;QAE7Cd,IAAI,CAACe,WAAW,CACdJ,WAAC,CAACK,mBAAmB,CAAC,KAAK,EAAE,CAC3BL,WAAC,CAACM,kBAAkB,CAACR,GAAG,EAAEE,WAAC,CAACO,YAAY,CAACV,IAAI,CAAC,CAAC,CAChD,CACH,CAAC;MACH,CAAC;MAEDW,eAAeA,CAACnB,IAAI,EAAEoB,KAAK,EAAE;QAC3B,MAAM;UAAEZ;QAAK,CAAC,GAAGR,IAAI;QACrB,IAAIL,OAAO,CAAC0B,GAAG,CAACb,IAAI,CAAC,EAAE;QAE0C;UAAA,IAAAc,qBAAA;UAE/D,CAAAA,qBAAA,GAAAtB,IAAI,CAACuB,kBAAkB,YAAAD,qBAAA,GAAvBtB,IAAI,CAACuB,kBAAkB,GAErB1D,OAAO,CAAC,iBAAiB,CAAC,CAACwC,QAAQ,CAACC,SAAS,CAACiB,kBAAkB;QACpE;QACA,MAAMC,WAAW,GAAGxB,IAAI,CAACuB,kBAAkB,CAAC/B,gBAAgB,CAAC;QAC7D,IAAIgC,WAAW,IAAIA,WAAW,CAAChB,IAAI,KAAKA,IAAI,EAAE;QAE9Cb,OAAO,CAAC8B,GAAG,CAACjB,IAAI,CAAC;QAEjB,MAAM,CAACkB,YAAY,CAAC,GAAG1B,IAAI,CAACe,WAAW,CACrC,IAAAY,uBAAc,EACZ3B,IAAI,EACJoB,KAAK,CAACQ,IAAI,EACVxD,cAAc,EACdc,KAAK,EACL;UACEC,eAAe;UACfE,aAAa;UACbC,0BAA0B;UAC1BC;QACF,CAAC,EACDC,gBACF,CACF,CAAC;QAED,IAAIkC,YAAY,CAACG,gBAAgB,CAAC,CAAC,EAAE;UACnC,IAAAC,6BAAc,EAACJ,YAAY,CAAC;UAC5B,MAAMK,MAAM,GAAGL,YAAY,CAACzB,GAAG,CAAC,QAAQ,CAAC;UACzC,IAAI8B,MAAM,CAACC,yBAAyB,CAAC,CAAC,EAAE;YAEtCD,MAAM,CAACE,yBAAyB,CAAC,CAAC;UACpC;QACF;MACF;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}