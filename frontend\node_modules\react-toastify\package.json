{"version": "9.1.3", "license": "MIT", "typings": "dist/index.d.ts", "description": "React notification made easy", "keywords": ["react", "notification", "toast", "react-component", "react-toastify", "push", "alert"], "files": ["dist", "addons", "scss"], "sideEffects": true, "scripts": {"start": "cd example && npm run start", "test": "jest", "clean": "rimraf dist && rimraf addons", "test:coverage": "yarn test --coverage", "lint": "eslint \"src/**/*.{ts,tsx}\"", "prettier": "prettier --write src", "prettier-scss": "prettier --write scss", "sass": "sass scss/main.scss dist/ReactToastify.css", "sass-minimal": "sass scss/minimal.scss dist/ReactToastify.minimal.css", "postsass": "postcss dist/ReactToastify.css --use autoprefixer -m -o dist/ReactToastify.css", "postsass-minimal": "cssnano dist/ReactToastify.minimal.css dist/ReactToastify.minimal.css --no-zindex --no-reduceIdents", "style": "npm run sass && npm run sass-minimal && cssnano dist/ReactToastify.css dist/ReactToastify.min.css --no-zindex --no-reduceIdents && npm run style-injector", "style-injector": "style2js --out-dir dist dist/ReactToastify.min.css", "build": "npm run clean && npm run build:core && npm run pack-and-extract && npm run build:addons && npm run style && husky install", "build:core": "microbundle --jsx React.createElement --jsxFragment React.Fragment", "build:addons": "node build-addons.mjs", "postbuild": "./prepend-use-client.sh", "setup": "npm run clean && npm run build && npm run pack-and-extract", "pack-and-extract": "yarn pack -f react-toastify.tgz && npm run rm-pkg && npm run extract-pkg", "rm-pkg": "rimraf node_modules/react-toastify && mkdir -p node_modules/react-toastify", "extract-pkg": "tar xzvf react-toastify.tgz -C node_modules/react-toastify --strip-components 1 && rimraf react-toastify.tgz"}, "peerDependencies": {"react": ">=16", "react-dom": ">=16"}, "prettier": {"printWidth": 80, "semi": true, "singleQuote": true, "trailingComma": "none", "arrowParens": "avoid"}, "jest": {"verbose": true, "coveragePathIgnorePatterns": ["/dist/*", "/src/index.tsx"], "roots": ["<rootDir>/test"], "transform": {"^.+\\.tsx?$": ["ts-jest", {"isolatedModules": true}]}, "setupFilesAfterEnv": ["@testing-library/jest-dom/extend-expect"], "testRegex": "(.+(test|spec))\\.tsx?$", "testEnvironment": "jsdom", "testRunner": "jest-jasmine2", "moduleFileExtensions": ["ts", "tsx", "js", "jsx", "json", "node"]}, "name": "react-toastify", "repository": {"type": "git", "url": "git+https://github.com/fkhadra/react-toastify.git"}, "author": "<PERSON><PERSON>  <<EMAIL>> (https://fkhadra.github.io)", "bugs": {"url": "https://github.com/fkhadra/react-toastify/issues"}, "homepage": "https://github.com/fkhadra/react-toastify#readme", "devDependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.0.0", "@types/jest": "^29.5.1", "@types/react": "^18.2.6", "@types/react-dom": "^18.2.4", "@typescript-eslint/eslint-plugin": "^5.59.5", "@typescript-eslint/parser": "^5.59.5", "coveralls": "^3.0.9", "cssnano": "^6.0.1", "cssnano-cli": "^1.0.5", "eslint": "^8.40.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.4.0", "husky": "^8.0.3", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "jest-jasmine2": "^29.5.0", "microbundle": "^0.15.1", "postcss": "^8.4.23", "postcss-cli": "^10.1.0", "prettier": "2.8.8", "react": "^18.0.0", "react-dom": "^18.0.0", "rimraf": "^5.0.0", "sass": "^1.62.1", "style2js": "^1.0.1", "ts-jest": "^29.1.0", "tslib": "^2.5.0", "typescript": "^5.0.4"}, "dependencies": {"clsx": "^1.1.1"}, "main": "dist/react-toastify.js", "module": "dist/react-toastify.esm.mjs", "umd:main": "dist/react-toastify.umd.js", "unpkg": "dist/react-toastify.umd.js", "source": "src/index.ts", "exports": {".": {"types": "./dist/index.d.ts", "require": "./dist/react-toastify.js", "import": "./dist/react-toastify.esm.mjs", "umd": "./dist/react-toastify.umd.js"}, "./dist/ReactToastify.min.css": "./dist/ReactToastify.min.css", "./dist/ReactToastify.css": "./dist/ReactToastify.css", "./dist/ReactToastify.minimal.css": "./dist/ReactToastify.minimal.css", "./ReactToastify.min.css": "./dist/ReactToastify.min.css", "./ReactToastify.css": "./dist/ReactToastify.css", "./ReactToastify.minimal.css": "./dist/ReactToastify.minimal.css", "./dist/inject-style": {"types": "./dist/inject-style.d.ts", "require": "./dist/inject-style.js", "import": "./dist/inject-style.esm.mjs"}, "./inject-style": {"types": "./dist/inject-style.d.ts", "require": "./dist/inject-style.js", "import": "./dist/inject-style.esm.mjs"}, "./package.json": "./package.json", "./scss/": "./scss/", "./addons/use-notification-center": {"types": "./addons/use-notification-center/index.d.ts", "require": "./addons/use-notification-center/index.js", "import": "./addons/use-notification-center/index.esm.mjs"}}}