import React, { useState, useEffect } from 'react';
import formConfigService from '../../services/formConfigService';
import apiService from '../../services/apiService';
import './AllFormsModal.css';

const AllFormsModal = ({ onClose, onEditForm, onDeleteForm, isInline = false }) => {
  const [forms, setForms] = useState([]);
  const [divisions, setDivisions] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [deleteConfirm, setDeleteConfirm] = useState(null);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Load divisions and categories
      const [divisionsResponse, categoriesResponse] = await Promise.all([
        apiService.getDivisions(),
        apiService.getCategories()
      ]);

      setDivisions(divisionsResponse.data || []);
      setCategories(categoriesResponse.data || []);

      // Load all forms from localStorage
      const allForms = formConfigService.getAllFormConfigs();

      // Enrich forms with division and category information
      const enrichedForms = allForms.map(form => {
        let divisionName = 'General';
        let categoryName = 'Default';

        if (form.hierarchy?.divisionId) {
          const division = divisionsResponse.data?.find(d => d.id === form.hierarchy.divisionId);
          if (division) {
            divisionName = division.name;
          }
        }

        if (form.hierarchy?.categoryId) {
          const category = categoriesResponse.data?.find(c => c.id === form.hierarchy.categoryId);
          if (category) {
            categoryName = category.name;
          }
        }

        return {
          ...form,
          divisionName,
          categoryName,
          divisionId: form.hierarchy?.divisionId || null,
          categoryId: form.hierarchy?.categoryId || null
        };
      });

      setForms(enrichedForms);
    } catch (err) {
      console.error('Error loading forms data:', err);
      setError('Failed to load forms data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteForm = (form) => {
    setDeleteConfirm(form);
  };

  const confirmDelete = () => {
    if (deleteConfirm) {
      try {
        formConfigService.deleteFormConfig(deleteConfirm.type, deleteConfirm.associatedId);
        setForms(forms.filter(f => f.id !== deleteConfirm.id));
        setDeleteConfirm(null);
        if (onDeleteForm) {
          onDeleteForm(deleteConfirm);
        }
      } catch (err) {
        console.error('Error deleting form:', err);
        setError('Failed to delete form. Please try again.');
      }
    }
  };

  const cancelDelete = () => {
    setDeleteConfirm(null);
  };

  const handleEditForm = (form) => {
    if (onEditForm) {
      onEditForm(form);
    }
    // Don't call onClose() here - let the parent component handle navigation
    // onClose() was causing the component to return to person list instead of form builder
  };

  // Group forms by division and then by category
  const groupedForms = forms.reduce((acc, form) => {
    const divisionKey = form.divisionId || 'general';
    const categoryKey = form.categoryId || 'default';
    
    if (!acc[divisionKey]) {
      acc[divisionKey] = {
        name: form.divisionName,
        categories: {}
      };
    }
    
    if (!acc[divisionKey].categories[categoryKey]) {
      acc[divisionKey].categories[categoryKey] = {
        name: form.categoryName,
        forms: []
      };
    }
    
    acc[divisionKey].categories[categoryKey].forms.push(form);
    
    return acc;
  }, {});

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  if (loading) {
    const content = (
      <div className="modal-content">
        <div className="modal-header">
          <h2>All Forms</h2>
          <button onClick={onClose} className="close-button">×</button>
        </div>
        <div className="modal-body">
          <div className="loading-state">
            <div className="spinner"></div>
            <p>Loading forms...</p>
          </div>
        </div>
      </div>
    );

    return isInline ? (
      <div className="all-forms-inline">
        {content}
      </div>
    ) : (
      <div className="all-forms-modal">
        {content}
      </div>
    );
  }

  const content = (
    <div className="modal-content">
      <div className="modal-header">
        <h2>All Forms</h2>
        <button onClick={onClose} className="close-button">×</button>
      </div>

      <div className="modal-body">
        {error && (
          <div className="error-message">
            <span className="error-icon">⚠️</span>
            <span>{error}</span>
          </div>
        )}

        {forms.length === 0 ? (
          <div className="empty-state">
            <div className="empty-icon">📝</div>
            <h3>No Forms Found</h3>
            <p>No custom forms have been created yet. Use the Form Builder to create your first form.</p>
          </div>
        ) : (
          <div className="forms-container">
            {Object.entries(groupedForms).map(([divisionKey, division]) => (
              <div key={divisionKey} className="division-section">
                <div className="division-header">
                  <h3>🏢 {division.name}</h3>
                  <span className="forms-count">
                    {Object.values(division.categories).reduce((total, cat) => total + cat.forms.length, 0)} form(s)
                  </span>
                </div>

                {Object.entries(division.categories).map(([categoryKey, category]) => (
                  <div key={categoryKey} className="category-section">
                    <div className="category-header">
                      <h4>📂 {category.name}</h4>
                      <span className="category-forms-count">{category.forms.length} form(s)</span>
                    </div>

                    <div className="forms-list">
                      {category.forms.map(form => (
                        <div key={form.id} className="form-list-item">
                          <div className="form-info">
                            <div className="form-name-section">
                              <h5 className="form-name">{form.name}</h5>
                              <span className={`type-badge ${form.type}`}>
                                {form.type === 'category' ? 'Category' :
                                 form.type === 'subcategory' ? 'SubCategory' :
                                 'Custom'}
                              </span>
                            </div>
                            <p className="form-description">{form.description || 'No description'}</p>
                            <div className="form-meta">
                              <span className="meta-item">📝 {form.fields?.length || 0} fields</span>
                              <span className="meta-item">📅 {formatDate(form.updatedAt)}</span>
                            </div>
                          </div>
                          <div className="form-actions">
                            <button
                              onClick={() => handleEditForm(form)}
                              className="btn-action edit"
                              title="Edit form"
                            >
                              ✏️ Edit
                            </button>
                            <button
                              onClick={() => handleDeleteForm(form)}
                              className="btn-action delete"
                              title="Delete form"
                            >
                              🗑️ Delete
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Delete Confirmation Modal */}
      {deleteConfirm && (
        <div className="delete-confirm-modal">
          <div className="delete-confirm-content">
            <div className="delete-confirm-header">
              <h3>Confirm Delete</h3>
            </div>
            <div className="delete-confirm-body">
              <p>Are you sure you want to delete the form <strong>"{deleteConfirm.name}"</strong>?</p>
              <p className="warning-text">This action cannot be undone.</p>
            </div>
            <div className="delete-confirm-actions">
              <button onClick={cancelDelete} className="btn-cancel">Cancel</button>
              <button onClick={confirmDelete} className="btn-delete">Delete</button>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  return isInline ? (
    <div className="all-forms-inline">
      {content}
    </div>
  ) : (
    <div className="all-forms-modal">
      {content}
    </div>
  );
};

export default AllFormsModal;
