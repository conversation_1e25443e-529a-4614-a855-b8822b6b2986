/* All Forms Modal Styles */
.all-forms-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 1rem;
}

/* All Forms Inline Styles */
.all-forms-inline {
  width: 100%;
  min-height: auto;
  background-color: transparent;
}

.all-forms-inline .modal-content {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: none;
  max-height: none;
  overflow: visible;
  display: flex;
  flex-direction: column;
  margin: 0;
}

.modal-content {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  width: 100%;
  max-width: 1200px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e1e5e9;
  background-color: #f8f9fa;
}

.modal-header h2 {
  margin: 0;
  color: #495057;
  font-size: 1.5rem;
}

.close-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #6c757d;
  padding: 0;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.close-button:hover {
  background-color: #e9ecef;
}

.modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;
}

/* Loading State */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error State */
.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 6px;
  color: #721c24;
  margin-bottom: 1rem;
}

.error-icon {
  font-size: 1.2rem;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 3rem;
  color: #6c757d;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.empty-state h3 {
  margin: 0 0 0.5rem 0;
  color: #495057;
}

.empty-state p {
  margin: 0;
  font-size: 0.9rem;
}

/* Forms Container */
.forms-container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Division Section */
.division-section {
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  overflow: hidden;
}

.division-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e1e5e9;
}

.division-header h3 {
  margin: 0;
  color: #495057;
  font-size: 1.2rem;
}

.forms-count {
  background-color: #007bff;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

/* Category Section */
.category-section {
  border-bottom: 1px solid #e1e5e9;
}

.category-section:last-child {
  border-bottom: none;
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1.5rem;
  background-color: #ffffff;
  border-bottom: 1px solid #f1f3f4;
}

.category-header h4 {
  margin: 0;
  color: #6c757d;
  font-size: 1rem;
  font-weight: 500;
}

.category-forms-count {
  background-color: #6c757d;
  color: white;
  padding: 0.2rem 0.6rem;
  border-radius: 10px;
  font-size: 0.75rem;
}

/* Forms List */
.forms-list {
  padding: 1rem 1.5rem;
  background-color: #fafbfc;
}

/* Form List Item */
.form-list-item {
  background-color: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  margin-bottom: 0.75rem;
  transition: box-shadow 0.2s, border-color 0.2s;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  gap: 1rem;
}

.form-list-item:hover {
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
  border-color: #007bff;
}

.form-list-item:last-child {
  margin-bottom: 0;
}

.form-info {
  flex: 1;
  min-width: 0; /* Allow text truncation */
}

.form-name-section {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
}

.form-name {
  margin: 0;
  color: #495057;
  font-size: 1.1rem;
  font-weight: 600;
  flex-shrink: 0;
}

.form-actions {
  display: flex;
  gap: 0.5rem;
  flex-shrink: 0;
}

.btn-action {
  background: none;
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  white-space: nowrap;
}

.btn-action:hover {
  background-color: #f8f9fa;
  border-color: #007bff;
}

.btn-action.edit:hover {
  background-color: #e3f2fd;
  border-color: #2196f3;
  color: #1976d2;
}

.btn-action.delete:hover {
  background-color: #ffebee;
  border-color: #f44336;
  color: #d32f2f;
}

.form-card-body {
  padding: 1rem;
}

.form-description {
  margin: 0 0 1rem 0;
  color: #6c757d;
  font-size: 0.9rem;
  line-height: 1.4;
}

.form-stats {
  display: flex;
  gap: 1rem;
  margin-bottom: 0.75rem;
}

.stat {
  font-size: 0.8rem;
  color: #6c757d;
}

.form-type {
  display: flex;
  justify-content: flex-end;
}

.type-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.type-badge.category {
  background-color: #e3f2fd;
  color: #1976d2;
}

.type-badge.subcategory {
  background-color: #f3e5f5;
  color: #7b1fa2;
}

.type-badge.default {
  background-color: #e8f5e8;
  color: #388e3c;
}

/* Delete Confirmation Modal */
.delete-confirm-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1100;
}

.delete-confirm-content {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  width: 100%;
  max-width: 400px;
  margin: 1rem;
}

.delete-confirm-header {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e1e5e9;
  background-color: #f8f9fa;
}

.delete-confirm-header h3 {
  margin: 0;
  color: #495057;
}

.delete-confirm-body {
  padding: 1.5rem;
}

.delete-confirm-body p {
  margin: 0 0 1rem 0;
  color: #495057;
}

.warning-text {
  color: #dc3545;
  font-size: 0.9rem;
  font-style: italic;
}

.delete-confirm-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1rem 1.5rem;
  border-top: 1px solid #e1e5e9;
  background-color: #f8f9fa;
}

.btn-cancel {
  padding: 0.5rem 1rem;
  border: 1px solid #6c757d;
  background-color: white;
  color: #6c757d;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-cancel:hover {
  background-color: #6c757d;
  color: white;
}

.btn-delete {
  padding: 0.5rem 1rem;
  border: 1px solid #dc3545;
  background-color: #dc3545;
  color: white;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-delete:hover {
  background-color: #c82333;
  border-color: #c82333;
}

/* Responsive Design */
@media (max-width: 768px) {
  .all-forms-modal {
    padding: 0.5rem;
  }
  
  .modal-content {
    max-height: 95vh;
  }
  
  .forms-list {
    padding: 1rem;
  }

  .division-header,
  .category-header {
    padding: 1rem;
  }

  .form-list-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
  }

  .form-info {
    width: 100%;
  }

  .form-name-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .form-actions {
    align-self: flex-end;
    width: auto;
  }
}
