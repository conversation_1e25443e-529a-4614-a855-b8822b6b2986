{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Advanced\\\\Advanced CRM filed all use\\\\frontend\\\\src\\\\components\\\\forms\\\\AllFormsModal.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport formConfigService from '../../services/formConfigService';\nimport apiService from '../../services/apiService';\nimport './AllFormsModal.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AllFormsModal = ({\n  onClose,\n  onEditForm,\n  onDeleteForm,\n  isInline = false\n}) => {\n  _s();\n  const [forms, setForms] = useState([]);\n  const [divisions, setDivisions] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [deleteConfirm, setDeleteConfirm] = useState(null);\n  useEffect(() => {\n    loadData();\n  }, []);\n  const loadData = async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      // Load divisions and categories\n      const [divisionsResponse, categoriesResponse] = await Promise.all([apiService.getDivisions(), apiService.getCategories()]);\n      setDivisions(divisionsResponse.data || []);\n      setCategories(categoriesResponse.data || []);\n\n      // Load all forms from localStorage\n      const allForms = formConfigService.getAllFormConfigs();\n\n      // Enrich forms with division and category information\n      const enrichedForms = allForms.map(form => {\n        var _form$hierarchy, _form$hierarchy2, _form$hierarchy3, _form$hierarchy4;\n        let divisionName = 'General';\n        let categoryName = 'Default';\n        if ((_form$hierarchy = form.hierarchy) !== null && _form$hierarchy !== void 0 && _form$hierarchy.divisionId) {\n          var _divisionsResponse$da;\n          const division = (_divisionsResponse$da = divisionsResponse.data) === null || _divisionsResponse$da === void 0 ? void 0 : _divisionsResponse$da.find(d => d.id === form.hierarchy.divisionId);\n          if (division) {\n            divisionName = division.name;\n          }\n        }\n        if ((_form$hierarchy2 = form.hierarchy) !== null && _form$hierarchy2 !== void 0 && _form$hierarchy2.categoryId) {\n          var _categoriesResponse$d;\n          const category = (_categoriesResponse$d = categoriesResponse.data) === null || _categoriesResponse$d === void 0 ? void 0 : _categoriesResponse$d.find(c => c.id === form.hierarchy.categoryId);\n          if (category) {\n            categoryName = category.name;\n          }\n        }\n        return {\n          ...form,\n          divisionName,\n          categoryName,\n          divisionId: ((_form$hierarchy3 = form.hierarchy) === null || _form$hierarchy3 === void 0 ? void 0 : _form$hierarchy3.divisionId) || null,\n          categoryId: ((_form$hierarchy4 = form.hierarchy) === null || _form$hierarchy4 === void 0 ? void 0 : _form$hierarchy4.categoryId) || null\n        };\n      });\n      setForms(enrichedForms);\n    } catch (err) {\n      console.error('Error loading forms data:', err);\n      setError('Failed to load forms data. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleDeleteForm = form => {\n    setDeleteConfirm(form);\n  };\n  const confirmDelete = () => {\n    if (deleteConfirm) {\n      try {\n        formConfigService.deleteFormConfig(deleteConfirm.type, deleteConfirm.associatedId);\n        setForms(forms.filter(f => f.id !== deleteConfirm.id));\n        setDeleteConfirm(null);\n        if (onDeleteForm) {\n          onDeleteForm(deleteConfirm);\n        }\n      } catch (err) {\n        console.error('Error deleting form:', err);\n        setError('Failed to delete form. Please try again.');\n      }\n    }\n  };\n  const cancelDelete = () => {\n    setDeleteConfirm(null);\n  };\n  const handleEditForm = form => {\n    if (onEditForm) {\n      onEditForm(form);\n    }\n    onClose();\n  };\n\n  // Group forms by division and then by category\n  const groupedForms = forms.reduce((acc, form) => {\n    const divisionKey = form.divisionId || 'general';\n    const categoryKey = form.categoryId || 'default';\n    if (!acc[divisionKey]) {\n      acc[divisionKey] = {\n        name: form.divisionName,\n        categories: {}\n      };\n    }\n    if (!acc[divisionKey].categories[categoryKey]) {\n      acc[divisionKey].categories[categoryKey] = {\n        name: form.categoryName,\n        forms: []\n      };\n    }\n    acc[divisionKey].categories[categoryKey].forms.push(form);\n    return acc;\n  }, {});\n  const formatDate = dateString => {\n    if (!dateString) return 'N/A';\n    return new Date(dateString).toLocaleDateString();\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"all-forms-modal\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"All Forms\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onClose,\n            className: \"close-button\",\n            children: \"\\xD7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-body\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"loading-state\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"spinner\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Loading forms...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"all-forms-modal\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"All Forms\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onClose,\n          className: \"close-button\",\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-body\",\n        children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-message\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"error-icon\",\n            children: \"\\u26A0\\uFE0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 13\n        }, this), forms.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"empty-state\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"empty-icon\",\n            children: \"\\uD83D\\uDCDD\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"No Forms Found\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"No custom forms have been created yet. Use the Form Builder to create your first form.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"forms-container\",\n          children: Object.entries(groupedForms).map(([divisionKey, division]) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"division-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"division-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: [\"\\uD83C\\uDFE2 \", division.name]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"forms-count\",\n                children: [Object.values(division.categories).reduce((total, cat) => total + cat.forms.length, 0), \" form(s)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 19\n            }, this), Object.entries(division.categories).map(([categoryKey, category]) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"category-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"category-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: [\"\\uD83D\\uDCC2 \", category.name]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"category-forms-count\",\n                  children: [category.forms.length, \" form(s)\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"forms-grid\",\n                children: category.forms.map(form => {\n                  var _form$fields;\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"form-card\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"form-card-header\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                        children: form.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 195,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"form-actions\",\n                        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                          onClick: () => handleEditForm(form),\n                          className: \"btn-action edit\",\n                          title: \"Edit form\",\n                          children: \"\\u270F\\uFE0F\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 197,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                          onClick: () => handleDeleteForm(form),\n                          className: \"btn-action delete\",\n                          title: \"Delete form\",\n                          children: \"\\uD83D\\uDDD1\\uFE0F\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 204,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 196,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 194,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"form-card-body\",\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"form-description\",\n                        children: form.description || 'No description'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 215,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"form-stats\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"stat\",\n                          children: [\"\\uD83D\\uDCDD \", ((_form$fields = form.fields) === null || _form$fields === void 0 ? void 0 : _form$fields.length) || 0, \" fields\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 217,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"stat\",\n                          children: [\"\\uD83D\\uDCC5 \", formatDate(form.updatedAt)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 218,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 216,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"form-type\",\n                        children: /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: `type-badge ${form.type}`,\n                          children: form.type === 'category' ? 'Category Form' : form.type === 'subcategory' ? 'SubCategory Form' : 'Custom Form'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 221,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 220,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 214,\n                      columnNumber: 29\n                    }, this)]\n                  }, form.id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 193,\n                    columnNumber: 27\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 23\n              }, this)]\n            }, categoryKey, true, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 21\n            }, this))]\n          }, divisionKey, true, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 7\n    }, this), deleteConfirm && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"delete-confirm-modal\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"delete-confirm-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"delete-confirm-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Confirm Delete\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"delete-confirm-body\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"Are you sure you want to delete the form \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: [\"\\\"\", deleteConfirm.name, \"\\\"\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 59\n            }, this), \"?\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"warning-text\",\n            children: \"This action cannot be undone.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"delete-confirm-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: cancelDelete,\n            className: \"btn-cancel\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: confirmDelete,\n            className: \"btn-delete\",\n            children: \"Delete\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 242,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 152,\n    columnNumber: 5\n  }, this);\n};\n_s(AllFormsModal, \"RQ+hg0Bxf6zGnwZ1c4ZpKa9CSY4=\");\n_c = AllFormsModal;\nexport default AllFormsModal;\nvar _c;\n$RefreshReg$(_c, \"AllFormsModal\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "formConfigService", "apiService", "jsxDEV", "_jsxDEV", "AllFormsModal", "onClose", "onEditForm", "onDeleteForm", "isInline", "_s", "forms", "setForms", "divisions", "setDivisions", "categories", "setCategories", "loading", "setLoading", "error", "setError", "deleteConfirm", "setDeleteConfirm", "loadData", "divisionsResponse", "categoriesResponse", "Promise", "all", "getDivisions", "getCategories", "data", "allForms", "getAllFormConfigs", "enrichedForms", "map", "form", "_form$hierarchy", "_form$hierarchy2", "_form$hierarchy3", "_form$hierarchy4", "divisionName", "categoryName", "hierarchy", "divisionId", "_divisionsResponse$da", "division", "find", "d", "id", "name", "categoryId", "_categoriesResponse$d", "category", "c", "err", "console", "handleDeleteForm", "confirmDelete", "deleteFormConfig", "type", "associatedId", "filter", "f", "cancelDelete", "handleEditForm", "groupedForms", "reduce", "acc", "divisionKey", "categoryKey", "push", "formatDate", "dateString", "Date", "toLocaleDateString", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "length", "Object", "entries", "values", "total", "cat", "_form$fields", "title", "description", "fields", "updatedAt", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/components/forms/AllFormsModal.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport formConfigService from '../../services/formConfigService';\nimport apiService from '../../services/apiService';\nimport './AllFormsModal.css';\n\nconst AllFormsModal = ({ onClose, onEditForm, onDeleteForm, isInline = false }) => {\n  const [forms, setForms] = useState([]);\n  const [divisions, setDivisions] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [deleteConfirm, setDeleteConfirm] = useState(null);\n\n  useEffect(() => {\n    loadData();\n  }, []);\n\n  const loadData = async () => {\n    setLoading(true);\n    setError(null);\n    \n    try {\n      // Load divisions and categories\n      const [divisionsResponse, categoriesResponse] = await Promise.all([\n        apiService.getDivisions(),\n        apiService.getCategories()\n      ]);\n\n      setDivisions(divisionsResponse.data || []);\n      setCategories(categoriesResponse.data || []);\n\n      // Load all forms from localStorage\n      const allForms = formConfigService.getAllFormConfigs();\n      \n      // Enrich forms with division and category information\n      const enrichedForms = allForms.map(form => {\n        let divisionName = 'General';\n        let categoryName = 'Default';\n\n        if (form.hierarchy?.divisionId) {\n          const division = divisionsResponse.data?.find(d => d.id === form.hierarchy.divisionId);\n          if (division) {\n            divisionName = division.name;\n          }\n        }\n\n        if (form.hierarchy?.categoryId) {\n          const category = categoriesResponse.data?.find(c => c.id === form.hierarchy.categoryId);\n          if (category) {\n            categoryName = category.name;\n          }\n        }\n\n        return {\n          ...form,\n          divisionName,\n          categoryName,\n          divisionId: form.hierarchy?.divisionId || null,\n          categoryId: form.hierarchy?.categoryId || null\n        };\n      });\n\n      setForms(enrichedForms);\n    } catch (err) {\n      console.error('Error loading forms data:', err);\n      setError('Failed to load forms data. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDeleteForm = (form) => {\n    setDeleteConfirm(form);\n  };\n\n  const confirmDelete = () => {\n    if (deleteConfirm) {\n      try {\n        formConfigService.deleteFormConfig(deleteConfirm.type, deleteConfirm.associatedId);\n        setForms(forms.filter(f => f.id !== deleteConfirm.id));\n        setDeleteConfirm(null);\n        if (onDeleteForm) {\n          onDeleteForm(deleteConfirm);\n        }\n      } catch (err) {\n        console.error('Error deleting form:', err);\n        setError('Failed to delete form. Please try again.');\n      }\n    }\n  };\n\n  const cancelDelete = () => {\n    setDeleteConfirm(null);\n  };\n\n  const handleEditForm = (form) => {\n    if (onEditForm) {\n      onEditForm(form);\n    }\n    onClose();\n  };\n\n  // Group forms by division and then by category\n  const groupedForms = forms.reduce((acc, form) => {\n    const divisionKey = form.divisionId || 'general';\n    const categoryKey = form.categoryId || 'default';\n    \n    if (!acc[divisionKey]) {\n      acc[divisionKey] = {\n        name: form.divisionName,\n        categories: {}\n      };\n    }\n    \n    if (!acc[divisionKey].categories[categoryKey]) {\n      acc[divisionKey].categories[categoryKey] = {\n        name: form.categoryName,\n        forms: []\n      };\n    }\n    \n    acc[divisionKey].categories[categoryKey].forms.push(form);\n    \n    return acc;\n  }, {});\n\n  const formatDate = (dateString) => {\n    if (!dateString) return 'N/A';\n    return new Date(dateString).toLocaleDateString();\n  };\n\n  if (loading) {\n    return (\n      <div className=\"all-forms-modal\">\n        <div className=\"modal-content\">\n          <div className=\"modal-header\">\n            <h2>All Forms</h2>\n            <button onClick={onClose} className=\"close-button\">×</button>\n          </div>\n          <div className=\"modal-body\">\n            <div className=\"loading-state\">\n              <div className=\"spinner\"></div>\n              <p>Loading forms...</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"all-forms-modal\">\n      <div className=\"modal-content\">\n        <div className=\"modal-header\">\n          <h2>All Forms</h2>\n          <button onClick={onClose} className=\"close-button\">×</button>\n        </div>\n        \n        <div className=\"modal-body\">\n          {error && (\n            <div className=\"error-message\">\n              <span className=\"error-icon\">⚠️</span>\n              <span>{error}</span>\n            </div>\n          )}\n\n          {forms.length === 0 ? (\n            <div className=\"empty-state\">\n              <div className=\"empty-icon\">📝</div>\n              <h3>No Forms Found</h3>\n              <p>No custom forms have been created yet. Use the Form Builder to create your first form.</p>\n            </div>\n          ) : (\n            <div className=\"forms-container\">\n              {Object.entries(groupedForms).map(([divisionKey, division]) => (\n                <div key={divisionKey} className=\"division-section\">\n                  <div className=\"division-header\">\n                    <h3>🏢 {division.name}</h3>\n                    <span className=\"forms-count\">\n                      {Object.values(division.categories).reduce((total, cat) => total + cat.forms.length, 0)} form(s)\n                    </span>\n                  </div>\n                  \n                  {Object.entries(division.categories).map(([categoryKey, category]) => (\n                    <div key={categoryKey} className=\"category-section\">\n                      <div className=\"category-header\">\n                        <h4>📂 {category.name}</h4>\n                        <span className=\"category-forms-count\">{category.forms.length} form(s)</span>\n                      </div>\n                      \n                      <div className=\"forms-grid\">\n                        {category.forms.map(form => (\n                          <div key={form.id} className=\"form-card\">\n                            <div className=\"form-card-header\">\n                              <h5>{form.name}</h5>\n                              <div className=\"form-actions\">\n                                <button\n                                  onClick={() => handleEditForm(form)}\n                                  className=\"btn-action edit\"\n                                  title=\"Edit form\"\n                                >\n                                  ✏️\n                                </button>\n                                <button\n                                  onClick={() => handleDeleteForm(form)}\n                                  className=\"btn-action delete\"\n                                  title=\"Delete form\"\n                                >\n                                  🗑️\n                                </button>\n                              </div>\n                            </div>\n                            \n                            <div className=\"form-card-body\">\n                              <p className=\"form-description\">{form.description || 'No description'}</p>\n                              <div className=\"form-stats\">\n                                <span className=\"stat\">📝 {form.fields?.length || 0} fields</span>\n                                <span className=\"stat\">📅 {formatDate(form.updatedAt)}</span>\n                              </div>\n                              <div className=\"form-type\">\n                                <span className={`type-badge ${form.type}`}>\n                                  {form.type === 'category' ? 'Category Form' : \n                                   form.type === 'subcategory' ? 'SubCategory Form' : \n                                   'Custom Form'}\n                                </span>\n                              </div>\n                            </div>\n                          </div>\n                        ))}\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Delete Confirmation Modal */}\n      {deleteConfirm && (\n        <div className=\"delete-confirm-modal\">\n          <div className=\"delete-confirm-content\">\n            <div className=\"delete-confirm-header\">\n              <h3>Confirm Delete</h3>\n            </div>\n            <div className=\"delete-confirm-body\">\n              <p>Are you sure you want to delete the form <strong>\"{deleteConfirm.name}\"</strong>?</p>\n              <p className=\"warning-text\">This action cannot be undone.</p>\n            </div>\n            <div className=\"delete-confirm-actions\">\n              <button onClick={cancelDelete} className=\"btn-cancel\">Cancel</button>\n              <button onClick={confirmDelete} className=\"btn-delete\">Delete</button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default AllFormsModal;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,iBAAiB,MAAM,kCAAkC;AAChE,OAAOC,UAAU,MAAM,2BAA2B;AAClD,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,aAAa,GAAGA,CAAC;EAAEC,OAAO;EAAEC,UAAU;EAAEC,YAAY;EAAEC,QAAQ,GAAG;AAAM,CAAC,KAAK;EAAAC,EAAA;EACjF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACc,SAAS,EAAEC,YAAY,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACgB,UAAU,EAAEC,aAAa,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoB,KAAK,EAAEC,QAAQ,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACsB,aAAa,EAAEC,gBAAgB,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAExDC,SAAS,CAAC,MAAM;IACduB,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3BL,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF;MACA,MAAM,CAACI,iBAAiB,EAAEC,kBAAkB,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAChEzB,UAAU,CAAC0B,YAAY,CAAC,CAAC,EACzB1B,UAAU,CAAC2B,aAAa,CAAC,CAAC,CAC3B,CAAC;MAEFf,YAAY,CAACU,iBAAiB,CAACM,IAAI,IAAI,EAAE,CAAC;MAC1Cd,aAAa,CAACS,kBAAkB,CAACK,IAAI,IAAI,EAAE,CAAC;;MAE5C;MACA,MAAMC,QAAQ,GAAG9B,iBAAiB,CAAC+B,iBAAiB,CAAC,CAAC;;MAEtD;MACA,MAAMC,aAAa,GAAGF,QAAQ,CAACG,GAAG,CAACC,IAAI,IAAI;QAAA,IAAAC,eAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA;QACzC,IAAIC,YAAY,GAAG,SAAS;QAC5B,IAAIC,YAAY,GAAG,SAAS;QAE5B,KAAAL,eAAA,GAAID,IAAI,CAACO,SAAS,cAAAN,eAAA,eAAdA,eAAA,CAAgBO,UAAU,EAAE;UAAA,IAAAC,qBAAA;UAC9B,MAAMC,QAAQ,IAAAD,qBAAA,GAAGpB,iBAAiB,CAACM,IAAI,cAAAc,qBAAA,uBAAtBA,qBAAA,CAAwBE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKb,IAAI,CAACO,SAAS,CAACC,UAAU,CAAC;UACtF,IAAIE,QAAQ,EAAE;YACZL,YAAY,GAAGK,QAAQ,CAACI,IAAI;UAC9B;QACF;QAEA,KAAAZ,gBAAA,GAAIF,IAAI,CAACO,SAAS,cAAAL,gBAAA,eAAdA,gBAAA,CAAgBa,UAAU,EAAE;UAAA,IAAAC,qBAAA;UAC9B,MAAMC,QAAQ,IAAAD,qBAAA,GAAG1B,kBAAkB,CAACK,IAAI,cAAAqB,qBAAA,uBAAvBA,qBAAA,CAAyBL,IAAI,CAACO,CAAC,IAAIA,CAAC,CAACL,EAAE,KAAKb,IAAI,CAACO,SAAS,CAACQ,UAAU,CAAC;UACvF,IAAIE,QAAQ,EAAE;YACZX,YAAY,GAAGW,QAAQ,CAACH,IAAI;UAC9B;QACF;QAEA,OAAO;UACL,GAAGd,IAAI;UACPK,YAAY;UACZC,YAAY;UACZE,UAAU,EAAE,EAAAL,gBAAA,GAAAH,IAAI,CAACO,SAAS,cAAAJ,gBAAA,uBAAdA,gBAAA,CAAgBK,UAAU,KAAI,IAAI;UAC9CO,UAAU,EAAE,EAAAX,gBAAA,GAAAJ,IAAI,CAACO,SAAS,cAAAH,gBAAA,uBAAdA,gBAAA,CAAgBW,UAAU,KAAI;QAC5C,CAAC;MACH,CAAC,CAAC;MAEFtC,QAAQ,CAACqB,aAAa,CAAC;IACzB,CAAC,CAAC,OAAOqB,GAAG,EAAE;MACZC,OAAO,CAACpC,KAAK,CAAC,2BAA2B,EAAEmC,GAAG,CAAC;MAC/ClC,QAAQ,CAAC,8CAA8C,CAAC;IAC1D,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMsC,gBAAgB,GAAIrB,IAAI,IAAK;IACjCb,gBAAgB,CAACa,IAAI,CAAC;EACxB,CAAC;EAED,MAAMsB,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIpC,aAAa,EAAE;MACjB,IAAI;QACFpB,iBAAiB,CAACyD,gBAAgB,CAACrC,aAAa,CAACsC,IAAI,EAAEtC,aAAa,CAACuC,YAAY,CAAC;QAClFhD,QAAQ,CAACD,KAAK,CAACkD,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACd,EAAE,KAAK3B,aAAa,CAAC2B,EAAE,CAAC,CAAC;QACtD1B,gBAAgB,CAAC,IAAI,CAAC;QACtB,IAAId,YAAY,EAAE;UAChBA,YAAY,CAACa,aAAa,CAAC;QAC7B;MACF,CAAC,CAAC,OAAOiC,GAAG,EAAE;QACZC,OAAO,CAACpC,KAAK,CAAC,sBAAsB,EAAEmC,GAAG,CAAC;QAC1ClC,QAAQ,CAAC,0CAA0C,CAAC;MACtD;IACF;EACF,CAAC;EAED,MAAM2C,YAAY,GAAGA,CAAA,KAAM;IACzBzC,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAM0C,cAAc,GAAI7B,IAAI,IAAK;IAC/B,IAAI5B,UAAU,EAAE;MACdA,UAAU,CAAC4B,IAAI,CAAC;IAClB;IACA7B,OAAO,CAAC,CAAC;EACX,CAAC;;EAED;EACA,MAAM2D,YAAY,GAAGtD,KAAK,CAACuD,MAAM,CAAC,CAACC,GAAG,EAAEhC,IAAI,KAAK;IAC/C,MAAMiC,WAAW,GAAGjC,IAAI,CAACQ,UAAU,IAAI,SAAS;IAChD,MAAM0B,WAAW,GAAGlC,IAAI,CAACe,UAAU,IAAI,SAAS;IAEhD,IAAI,CAACiB,GAAG,CAACC,WAAW,CAAC,EAAE;MACrBD,GAAG,CAACC,WAAW,CAAC,GAAG;QACjBnB,IAAI,EAAEd,IAAI,CAACK,YAAY;QACvBzB,UAAU,EAAE,CAAC;MACf,CAAC;IACH;IAEA,IAAI,CAACoD,GAAG,CAACC,WAAW,CAAC,CAACrD,UAAU,CAACsD,WAAW,CAAC,EAAE;MAC7CF,GAAG,CAACC,WAAW,CAAC,CAACrD,UAAU,CAACsD,WAAW,CAAC,GAAG;QACzCpB,IAAI,EAAEd,IAAI,CAACM,YAAY;QACvB9B,KAAK,EAAE;MACT,CAAC;IACH;IAEAwD,GAAG,CAACC,WAAW,CAAC,CAACrD,UAAU,CAACsD,WAAW,CAAC,CAAC1D,KAAK,CAAC2D,IAAI,CAACnC,IAAI,CAAC;IAEzD,OAAOgC,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;EAEN,MAAMI,UAAU,GAAIC,UAAU,IAAK;IACjC,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;IAC7B,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,CAAC;EAClD,CAAC;EAED,IAAIzD,OAAO,EAAE;IACX,oBACEb,OAAA;MAAKuE,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAC9BxE,OAAA;QAAKuE,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BxE,OAAA;UAAKuE,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BxE,OAAA;YAAAwE,QAAA,EAAI;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClB5E,OAAA;YAAQ6E,OAAO,EAAE3E,OAAQ;YAACqE,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC,eACN5E,OAAA;UAAKuE,SAAS,EAAC,YAAY;UAAAC,QAAA,eACzBxE,OAAA;YAAKuE,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BxE,OAAA;cAAKuE,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC/B5E,OAAA;cAAAwE,QAAA,EAAG;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE5E,OAAA;IAAKuE,SAAS,EAAC,iBAAiB;IAAAC,QAAA,gBAC9BxE,OAAA;MAAKuE,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BxE,OAAA;QAAKuE,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BxE,OAAA;UAAAwE,QAAA,EAAI;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClB5E,OAAA;UAAQ6E,OAAO,EAAE3E,OAAQ;UAACqE,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,eAEN5E,OAAA;QAAKuE,SAAS,EAAC,YAAY;QAAAC,QAAA,GACxBzD,KAAK,iBACJf,OAAA;UAAKuE,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BxE,OAAA;YAAMuE,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACtC5E,OAAA;YAAAwE,QAAA,EAAOzD;UAAK;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CACN,EAEArE,KAAK,CAACuE,MAAM,KAAK,CAAC,gBACjB9E,OAAA;UAAKuE,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BxE,OAAA;YAAKuE,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACpC5E,OAAA;YAAAwE,QAAA,EAAI;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvB5E,OAAA;YAAAwE,QAAA,EAAG;UAAsF;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1F,CAAC,gBAEN5E,OAAA;UAAKuE,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAC7BO,MAAM,CAACC,OAAO,CAACnB,YAAY,CAAC,CAAC/B,GAAG,CAAC,CAAC,CAACkC,WAAW,EAAEvB,QAAQ,CAAC,kBACxDzC,OAAA;YAAuBuE,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBACjDxE,OAAA;cAAKuE,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BxE,OAAA;gBAAAwE,QAAA,GAAI,eAAG,EAAC/B,QAAQ,CAACI,IAAI;cAAA;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC3B5E,OAAA;gBAAMuE,SAAS,EAAC,aAAa;gBAAAC,QAAA,GAC1BO,MAAM,CAACE,MAAM,CAACxC,QAAQ,CAAC9B,UAAU,CAAC,CAACmD,MAAM,CAAC,CAACoB,KAAK,EAAEC,GAAG,KAAKD,KAAK,GAAGC,GAAG,CAAC5E,KAAK,CAACuE,MAAM,EAAE,CAAC,CAAC,EAAC,UAC1F;cAAA;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,EAELG,MAAM,CAACC,OAAO,CAACvC,QAAQ,CAAC9B,UAAU,CAAC,CAACmB,GAAG,CAAC,CAAC,CAACmC,WAAW,EAAEjB,QAAQ,CAAC,kBAC/DhD,OAAA;cAAuBuE,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBACjDxE,OAAA;gBAAKuE,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BxE,OAAA;kBAAAwE,QAAA,GAAI,eAAG,EAACxB,QAAQ,CAACH,IAAI;gBAAA;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC3B5E,OAAA;kBAAMuE,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,GAAExB,QAAQ,CAACzC,KAAK,CAACuE,MAAM,EAAC,UAAQ;gBAAA;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1E,CAAC,eAEN5E,OAAA;gBAAKuE,SAAS,EAAC,YAAY;gBAAAC,QAAA,EACxBxB,QAAQ,CAACzC,KAAK,CAACuB,GAAG,CAACC,IAAI;kBAAA,IAAAqD,YAAA;kBAAA,oBACtBpF,OAAA;oBAAmBuE,SAAS,EAAC,WAAW;oBAAAC,QAAA,gBACtCxE,OAAA;sBAAKuE,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,gBAC/BxE,OAAA;wBAAAwE,QAAA,EAAKzC,IAAI,CAACc;sBAAI;wBAAA4B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACpB5E,OAAA;wBAAKuE,SAAS,EAAC,cAAc;wBAAAC,QAAA,gBAC3BxE,OAAA;0BACE6E,OAAO,EAAEA,CAAA,KAAMjB,cAAc,CAAC7B,IAAI,CAAE;0BACpCwC,SAAS,EAAC,iBAAiB;0BAC3Bc,KAAK,EAAC,WAAW;0BAAAb,QAAA,EAClB;wBAED;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACT5E,OAAA;0BACE6E,OAAO,EAAEA,CAAA,KAAMzB,gBAAgB,CAACrB,IAAI,CAAE;0BACtCwC,SAAS,EAAC,mBAAmB;0BAC7Bc,KAAK,EAAC,aAAa;0BAAAb,QAAA,EACpB;wBAED;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAEN5E,OAAA;sBAAKuE,SAAS,EAAC,gBAAgB;sBAAAC,QAAA,gBAC7BxE,OAAA;wBAAGuE,SAAS,EAAC,kBAAkB;wBAAAC,QAAA,EAAEzC,IAAI,CAACuD,WAAW,IAAI;sBAAgB;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC1E5E,OAAA;wBAAKuE,SAAS,EAAC,YAAY;wBAAAC,QAAA,gBACzBxE,OAAA;0BAAMuE,SAAS,EAAC,MAAM;0BAAAC,QAAA,GAAC,eAAG,EAAC,EAAAY,YAAA,GAAArD,IAAI,CAACwD,MAAM,cAAAH,YAAA,uBAAXA,YAAA,CAAaN,MAAM,KAAI,CAAC,EAAC,SAAO;wBAAA;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eAClE5E,OAAA;0BAAMuE,SAAS,EAAC,MAAM;0BAAAC,QAAA,GAAC,eAAG,EAACL,UAAU,CAACpC,IAAI,CAACyD,SAAS,CAAC;wBAAA;0BAAAf,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1D,CAAC,eACN5E,OAAA;wBAAKuE,SAAS,EAAC,WAAW;wBAAAC,QAAA,eACxBxE,OAAA;0BAAMuE,SAAS,EAAE,cAAcxC,IAAI,CAACwB,IAAI,EAAG;0BAAAiB,QAAA,EACxCzC,IAAI,CAACwB,IAAI,KAAK,UAAU,GAAG,eAAe,GAC1CxB,IAAI,CAACwB,IAAI,KAAK,aAAa,GAAG,kBAAkB,GAChD;wBAAa;0BAAAkB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACV;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA,GAlCE7C,IAAI,CAACa,EAAE;oBAAA6B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAmCZ,CAAC;gBAAA,CACP;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA,GA7CEX,WAAW;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA8ChB,CACN,CAAC;UAAA,GAxDMZ,WAAW;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAyDhB,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL3D,aAAa,iBACZjB,OAAA;MAAKuE,SAAS,EAAC,sBAAsB;MAAAC,QAAA,eACnCxE,OAAA;QAAKuE,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrCxE,OAAA;UAAKuE,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eACpCxE,OAAA;YAAAwE,QAAA,EAAI;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eACN5E,OAAA;UAAKuE,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAClCxE,OAAA;YAAAwE,QAAA,GAAG,2CAAyC,eAAAxE,OAAA;cAAAwE,QAAA,GAAQ,IAAC,EAACvD,aAAa,CAAC4B,IAAI,EAAC,IAAC;YAAA;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACxF5E,OAAA;YAAGuE,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAA6B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC,eACN5E,OAAA;UAAKuE,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrCxE,OAAA;YAAQ6E,OAAO,EAAElB,YAAa;YAACY,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACrE5E,OAAA;YAAQ6E,OAAO,EAAExB,aAAc;YAACkB,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACtE,EAAA,CA9PIL,aAAa;AAAAwF,EAAA,GAAbxF,aAAa;AAgQnB,eAAeA,aAAa;AAAC,IAAAwF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}