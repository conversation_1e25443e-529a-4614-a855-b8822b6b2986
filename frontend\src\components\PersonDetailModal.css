.person-detail-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 1rem;
}

.person-detail-modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem;
  border-bottom: 1px solid #e9ecef;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.header-icon {
  font-size: 2rem;
  opacity: 0.9;
}

.modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.firm-name {
  margin: 0.25rem 0 0 0;
  font-size: 0.9rem;
  opacity: 0.9;
}

.header-actions {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.btn-primary {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-primary:hover {
  background-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.close-button {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.close-button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 2rem;
}

.detail-sections {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.detail-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
}

.detail-section h3 {
  margin: 0 0 1.5rem 0;
  color: #2c3e50;
  font-size: 1.1rem;
  font-weight: 600;
  border-bottom: 2px solid #3498db;
  padding-bottom: 0.5rem;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.detail-item.full-width {
  grid-column: 1 / -1;
}

.detail-item label {
  font-weight: 500;
  color: #6c757d;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.detail-item span {
  color: #2c3e50;
  font-size: 1rem;
  word-break: break-word;
}

.nature-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.nature-badge.nature-1 {
  background-color: #e3f2fd;
  color: #1976d2;
}

.nature-badge.nature-2 {
  background-color: #f3e5f5;
  color: #7b1fa2;
}

.nature-badge.nature-3 {
  background-color: #e8f5e8;
  color: #388e3c;
}

.nature-badge.nature-4 {
  background-color: #fff3e0;
  color: #f57c00;
}

.star-rating {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.star {
  color: #ddd;
  transition: color 0.2s ease;
}

.star.filled {
  color: #ffc107;
}

.rating-text {
  margin-left: 0.5rem;
  font-size: 0.9rem;
  color: #6c757d;
}

.no-rating {
  color: #6c757d;
  font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
  .person-detail-overlay {
    padding: 0.5rem;
  }

  .person-detail-modal {
    max-height: 95vh;
  }

  .modal-header {
    padding: 1.5rem;
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .header-actions {
    align-self: flex-end;
  }

  .modal-body {
    padding: 1.5rem;
  }

  .detail-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .detail-section {
    padding: 1rem;
  }
}
