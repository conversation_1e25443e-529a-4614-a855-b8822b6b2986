{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_core", "_default", "exports", "default", "declare", "api", "assertVersion", "name", "visitor", "MetaProperty", "path", "meta", "get", "property", "scope", "isIdentifier", "func", "findParent", "isClass", "isFunction", "isArrowFunctionExpression", "isClassMethod", "kind", "buildCodeFrameError", "node", "t", "isMethod", "replaceWith", "buildUndefinedNode", "constructor", "memberExpression", "thisExpression", "identifier", "id", "generateUidIdentifier", "parentPath", "hasOwnBinding", "bindingIdentifierEquals", "rename", "parent", "conditionalExpression", "binaryExpression", "cloneNode"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport { types as t, type NodePath } from \"@babel/core\";\n\nexport default declare(api => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  return {\n    name: \"transform-new-target\",\n\n    visitor: {\n      MetaProperty(path) {\n        const meta = path.get(\"meta\");\n        const property = path.get(\"property\");\n        const { scope } = path;\n\n        if (\n          meta.isIdentifier({ name: \"new\" }) &&\n          property.isIdentifier({ name: \"target\" })\n        ) {\n          const func = path.findParent(path => {\n            if (path.isClass()) return true;\n            if (path.isFunction() && !path.isArrowFunctionExpression()) {\n              if (path.isClassMethod({ kind: \"constructor\" })) {\n                return false;\n              }\n\n              return true;\n            }\n            return false;\n          }) as NodePath<\n            | t.FunctionDeclaration\n            | t.FunctionExpression\n            | t.Class\n            | t.ClassMethod\n            | t.ClassPrivateMethod\n          >;\n\n          if (!func) {\n            throw path.buildCodeFrameError(\n              \"new.target must be under a (non-arrow) function or a class.\",\n            );\n          }\n\n          const { node } = func;\n          if (t.isMethod(node)) {\n            path.replaceWith(scope.buildUndefinedNode());\n            return;\n          }\n\n          const constructor = t.memberExpression(\n            t.thisExpression(),\n            t.identifier(\"constructor\"),\n          );\n\n          if (func.isClass()) {\n            path.replaceWith(constructor);\n            return;\n          }\n\n          if (!node.id) {\n            node.id = scope.generateUidIdentifier(\"target\");\n          } else {\n            // packages/babel-helper-create-class-features-plugin/src/fields.ts#L192 unshadow\n            let scope = path.scope;\n            const name = node.id.name;\n            while (scope !== func.parentPath.scope) {\n              if (\n                scope.hasOwnBinding(name) &&\n                !scope.bindingIdentifierEquals(name, node.id)\n              ) {\n                scope.rename(name);\n              }\n              scope = scope.parent;\n            }\n          }\n\n          path.replaceWith(\n            t.conditionalExpression(\n              t.binaryExpression(\n                \"instanceof\",\n                t.thisExpression(),\n                t.cloneNode(node.id),\n              ),\n              constructor,\n              scope.buildUndefinedNode(),\n            ),\n          );\n        }\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AAAwD,IAAAE,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEzC,IAAAC,0BAAO,EAACC,GAAG,IAAI;EAC5BA,GAAG,CAACC,aAAa,CAAkB,CAAE,CAAC;EAEtC,OAAO;IACLC,IAAI,EAAE,sBAAsB;IAE5BC,OAAO,EAAE;MACPC,YAAYA,CAACC,IAAI,EAAE;QACjB,MAAMC,IAAI,GAAGD,IAAI,CAACE,GAAG,CAAC,MAAM,CAAC;QAC7B,MAAMC,QAAQ,GAAGH,IAAI,CAACE,GAAG,CAAC,UAAU,CAAC;QACrC,MAAM;UAAEE;QAAM,CAAC,GAAGJ,IAAI;QAEtB,IACEC,IAAI,CAACI,YAAY,CAAC;UAAER,IAAI,EAAE;QAAM,CAAC,CAAC,IAClCM,QAAQ,CAACE,YAAY,CAAC;UAAER,IAAI,EAAE;QAAS,CAAC,CAAC,EACzC;UACA,MAAMS,IAAI,GAAGN,IAAI,CAACO,UAAU,CAACP,IAAI,IAAI;YACnC,IAAIA,IAAI,CAACQ,OAAO,CAAC,CAAC,EAAE,OAAO,IAAI;YAC/B,IAAIR,IAAI,CAACS,UAAU,CAAC,CAAC,IAAI,CAACT,IAAI,CAACU,yBAAyB,CAAC,CAAC,EAAE;cAC1D,IAAIV,IAAI,CAACW,aAAa,CAAC;gBAAEC,IAAI,EAAE;cAAc,CAAC,CAAC,EAAE;gBAC/C,OAAO,KAAK;cACd;cAEA,OAAO,IAAI;YACb;YACA,OAAO,KAAK;UACd,CAAC,CAMA;UAED,IAAI,CAACN,IAAI,EAAE;YACT,MAAMN,IAAI,CAACa,mBAAmB,CAC5B,6DACF,CAAC;UACH;UAEA,MAAM;YAAEC;UAAK,CAAC,GAAGR,IAAI;UACrB,IAAIS,WAAC,CAACC,QAAQ,CAACF,IAAI,CAAC,EAAE;YACpBd,IAAI,CAACiB,WAAW,CAACb,KAAK,CAACc,kBAAkB,CAAC,CAAC,CAAC;YAC5C;UACF;UAEA,MAAMC,WAAW,GAAGJ,WAAC,CAACK,gBAAgB,CACpCL,WAAC,CAACM,cAAc,CAAC,CAAC,EAClBN,WAAC,CAACO,UAAU,CAAC,aAAa,CAC5B,CAAC;UAED,IAAIhB,IAAI,CAACE,OAAO,CAAC,CAAC,EAAE;YAClBR,IAAI,CAACiB,WAAW,CAACE,WAAW,CAAC;YAC7B;UACF;UAEA,IAAI,CAACL,IAAI,CAACS,EAAE,EAAE;YACZT,IAAI,CAACS,EAAE,GAAGnB,KAAK,CAACoB,qBAAqB,CAAC,QAAQ,CAAC;UACjD,CAAC,MAAM;YAEL,IAAIpB,KAAK,GAAGJ,IAAI,CAACI,KAAK;YACtB,MAAMP,IAAI,GAAGiB,IAAI,CAACS,EAAE,CAAC1B,IAAI;YACzB,OAAOO,KAAK,KAAKE,IAAI,CAACmB,UAAU,CAACrB,KAAK,EAAE;cACtC,IACEA,KAAK,CAACsB,aAAa,CAAC7B,IAAI,CAAC,IACzB,CAACO,KAAK,CAACuB,uBAAuB,CAAC9B,IAAI,EAAEiB,IAAI,CAACS,EAAE,CAAC,EAC7C;gBACAnB,KAAK,CAACwB,MAAM,CAAC/B,IAAI,CAAC;cACpB;cACAO,KAAK,GAAGA,KAAK,CAACyB,MAAM;YACtB;UACF;UAEA7B,IAAI,CAACiB,WAAW,CACdF,WAAC,CAACe,qBAAqB,CACrBf,WAAC,CAACgB,gBAAgB,CAChB,YAAY,EACZhB,WAAC,CAACM,cAAc,CAAC,CAAC,EAClBN,WAAC,CAACiB,SAAS,CAAClB,IAAI,CAACS,EAAE,CACrB,CAAC,EACDJ,WAAW,EACXf,KAAK,CAACc,kBAAkB,CAAC,CAC3B,CACF,CAAC;QACH;MACF;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}