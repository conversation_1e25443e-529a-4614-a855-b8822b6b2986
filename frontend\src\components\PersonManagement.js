import { useState } from 'react';
import FormBuilder from './forms/FormBuilder';
import DynamicPersonForm from './forms/DynamicPersonForm';
import PersonList from './PersonList';
import ImportPersons from './import/ImportPersons';
import AllFormsModal from './forms/AllFormsModal';

import './PersonManagement.css';

const PersonManagement = () => {
  const [currentView, setCurrentView] = useState('list'); // 'list', 'create', 'edit', 'formBuilder', 'import', 'allForms'
  const [selectedPerson, setSelectedPerson] = useState(null);
  const [selectedForm, setSelectedForm] = useState(null);
  const [notification, setNotification] = useState(null);

  const showNotification = (message, type = 'success') => {
    setNotification({ message, type });
    setTimeout(() => setNotification(null), 5000);
  };

  const handleCreatePerson = () => {
    setSelectedPerson(null);
    setCurrentView('create');
  };

  const handleEditPerson = (person) => {
    setSelectedPerson(person);
    setCurrentView('edit');
  };

  const handlePersonSubmit = () => {
    const action = currentView === 'create' ? 'created' : 'updated';
    showNotification(`Person ${action} successfully!`);
    setCurrentView('list');
    setSelectedPerson(null);
  };

  const handleFormBuilderOpen = () => {
    setSelectedForm(null); // Clear any selected form for new form creation
    setCurrentView('formBuilder');
  };

  const handleFormBuilderSave = (config) => {
    const action = selectedForm ? 'updated' : 'created';
    showNotification(`Form configuration "${config.name}" ${action} successfully!`);
    setSelectedForm(null);
    setCurrentView('list');
  };

  const handleImportOpen = () => {
    setCurrentView('import');
  };

  const handleImportSuccess = (results) => {
    showNotification(`Import completed! ${results.successfulRows} persons imported successfully.`);
    setCurrentView('list');
    // Refresh the person list if we're on the list view
  };

  const handleImportClose = () => {
    setCurrentView('list');
  };

  const handleViewForms = () => {
    setCurrentView('create'); // This will trigger the form selection view
  };

  const handleAllFormsOpen = () => {
    setCurrentView('allForms');
  };

  const handleAllFormsClose = () => {
    setCurrentView('list');
  };

  const handleEditFormFromModal = (form) => {
    // Set the selected form for editing
    setSelectedForm(form);
    setCurrentView('formBuilder');
  };

  const handleDeleteFormFromModal = (form) => {
    showNotification(`Form "${form.name}" deleted successfully!`, 'success');
  };

  const handleCancel = () => {
    setCurrentView('list');
    setSelectedPerson(null);
    setSelectedForm(null);
  };

  const renderCurrentView = () => {
    switch (currentView) {
      case 'create':
        return (
          <DynamicPersonForm
            mode="create"
            onSubmit={handlePersonSubmit}
            onCancel={handleCancel}
          />
        );
      
      case 'edit':
        return (
          <DynamicPersonForm
            mode="edit"
            initialData={selectedPerson}
            onSubmit={handlePersonSubmit}
            onCancel={handleCancel}
          />
        );
      
      case 'formBuilder':
        return (
          <FormBuilder
            onSave={handleFormBuilderSave}
            onCancel={handleCancel}
            initialConfig={selectedForm}
          />
        );

      case 'import':
        return (
          <ImportPersons
            onClose={handleImportClose}
            onSuccess={handleImportSuccess}
            onViewForms={handleViewForms}
          />
        );

      case 'allForms':
        return (
          <AllFormsModal
            onClose={handleAllFormsClose}
            onEditForm={handleEditFormFromModal}
            onDeleteForm={handleDeleteFormFromModal}
            isInline={true}
          />
        );

      case 'list':
      default:
        return (
          <PersonList
            onEditPerson={handleEditPerson}
          />
        );
    }
  };

  return (
    <div className="person-management">
      {/* Header */}
      <div className="management-header">
        <div className="header-content">
          <h1>Person Management System</h1>
        </div>
        
        {/* Navigation */}
        <div className="header-nav">
          <button
            onClick={() => setCurrentView('list')}
            className={`nav-btn ${currentView === 'list' ? 'active' : ''}`}
          >
            📋 Person List
          </button>
          <button
            onClick={handleCreatePerson}
            className={`nav-btn ${currentView === 'create' ? 'active' : ''}`}
          >
            ➕ Create Person
          </button>
          <button
            onClick={handleFormBuilderOpen}
            className={`nav-btn ${currentView === 'formBuilder' ? 'active' : ''}`}
          >
            🔧 Form Builder
          </button>
          <button
            onClick={handleImportOpen}
            className={`nav-btn ${currentView === 'import' ? 'active' : ''}`}
          >
            📥 Import Persons
          </button>
          <button
            onClick={handleAllFormsOpen}
            className={`nav-btn ${currentView === 'allForms' ? 'active' : ''}`}
          >
            📋 All Forms
          </button>
        </div>
      </div>

      {/* Notification */}
      {notification && (
        <div className={`notification ${notification.type}`}>
          <span>{notification.message}</span>
          <button onClick={() => setNotification(null)} className="notification-close">
            ×
          </button>
        </div>
      )}

      {/* Main Content */}
      <div className="management-content">
        {renderCurrentView()}
      </div>
    </div>
  );
};

export default PersonManagement;
