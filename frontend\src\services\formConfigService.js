import { PersonFieldDefinitions, getAllPersonFields } from '../constants/personConstants';

class FormConfigService {
  constructor() {
    this.storagePrefix = 'crm_form_config_';
    this.defaultFormKey = 'default_person_form';
  }

  // Generate storage key for form configuration
  getStorageKey(type, id) {
    return `${this.storagePrefix}${type}_${id}`;
  }

  // Save form configuration
  saveFormConfig(type, id, config) {
    try {
      const key = this.getStorageKey(type, id);

      // Deduplicate fields before saving
      const deduplicatedFields = this.deduplicateFields(config.fields || []);

      const formConfig = {
        id: `${type}_${id}`,
        type, // 'division', 'category', or 'subcategory'
        associatedId: id,
        name: config.name || `${type} ${id} Form`,
        description: config.description || '',
        fields: deduplicatedFields, // Use deduplicated fields
        sections: config.sections || [],
        settings: config.settings || {},
        hierarchy: config.hierarchy || {},
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        version: 1,
        ...config,
        fields: deduplicatedFields // Ensure deduplicated fields override any from ...config
      };

      localStorage.setItem(key, JSON.stringify(formConfig));
      return formConfig;
    } catch (error) {
      console.error('Error saving form configuration:', error);
      throw new Error('Failed to save form configuration');
    }
  }

  // Load form configuration
  loadFormConfig(type, id) {
    try {
      const key = this.getStorageKey(type, id);
      const stored = localStorage.getItem(key);
      
      if (stored) {
        return JSON.parse(stored);
      }
      
      return null;
    } catch (error) {
      console.error('Error loading form configuration:', error);
      return null;
    }
  }

  // Delete form configuration
  deleteFormConfig(type, id) {
    try {
      const key = this.getStorageKey(type, id);
      localStorage.removeItem(key);
      return true;
    } catch (error) {
      console.error('Error deleting form configuration:', error);
      return false;
    }
  }

  // Clear all form configurations (for testing/debugging)
  clearAllFormConfigs() {
    try {
      const keys = Object.keys(localStorage);
      const formKeys = keys.filter(key => key.startsWith('form_config_'));
      formKeys.forEach(key => localStorage.removeItem(key));
      return true;
    } catch (error) {
      console.error('Error clearing form configurations:', error);
      return false;
    }
  }



  // Check if a form exists for a specific category
  hasFormForCategory(categoryId) {
    try {
      const key = this.getStorageKey('category', categoryId);
      return localStorage.getItem(key) !== null;
    } catch (error) {
      console.error('Error checking category form:', error);
      return false;
    }
  }

  // Check if a form exists for a specific subcategory
  hasFormForSubCategory(subCategoryId) {
    try {
      const key = this.getStorageKey('subcategory', subCategoryId);
      return localStorage.getItem(key) !== null;
    } catch (error) {
      console.error('Error checking subcategory form:', error);
      return false;
    }
  }

  // Get all subcategories for a category that have forms
  getSubCategoriesWithForms(categoryId) {
    try {
      const allConfigs = this.getAllFormConfigs();
      return allConfigs
        .filter(config => config.type === 'subcategory' && config.hierarchy?.categoryId === categoryId)
        .map(config => config.hierarchy?.subCategoryId)
        .filter(id => id !== null && id !== undefined);
    } catch (error) {
      console.error('Error getting subcategories with forms:', error);
      return [];
    }
  }

  // Get existing form information for a category/subcategory
  getExistingFormInfo(categoryId, subCategoryId = null) {
    try {
      const info = {
        categoryHasForm: false,
        subCategoryHasForm: false,
        subCategoriesWithForms: [],
        existingForms: []
      };

      if (categoryId) {
        info.categoryHasForm = this.hasFormForCategory(categoryId);
        info.subCategoriesWithForms = this.getSubCategoriesWithForms(categoryId);

        if (info.categoryHasForm) {
          const categoryForm = this.loadFormConfig('category', categoryId);
          if (categoryForm) {
            info.existingForms.push({
              type: 'category',
              name: categoryForm.name,
              description: categoryForm.description
            });
          }
        }
      }

      if (subCategoryId) {
        info.subCategoryHasForm = this.hasFormForSubCategory(subCategoryId);

        if (info.subCategoryHasForm) {
          const subCategoryForm = this.loadFormConfig('subcategory', subCategoryId);
          if (subCategoryForm) {
            info.existingForms.push({
              type: 'subcategory',
              name: subCategoryForm.name,
              description: subCategoryForm.description
            });
          }
        }
      }

      return info;
    } catch (error) {
      console.error('Error getting existing form info:', error);
      return {
        categoryHasForm: false,
        subCategoryHasForm: false,
        subCategoriesWithForms: [],
        existingForms: []
      };
    }
  }

  // Validate form creation rules
  validateFormCreation(categoryId, subCategoryId = null) {
    const errors = [];

    if (subCategoryId) {
      // Rule 1: Check if category already has a form
      if (this.hasFormForCategory(categoryId)) {
        errors.push('Cannot create form for subcategory because a form already exists for the parent category');
      }

      // Rule 2: Check if subcategory already has a form
      if (this.hasFormForSubCategory(subCategoryId)) {
        errors.push('A form already exists for this subcategory. Only one form per subcategory is allowed');
      }
    } else if (categoryId) {
      // Rule 3: Check if any subcategories have forms
      const subCategoriesWithForms = this.getSubCategoriesWithForms(categoryId);
      if (subCategoriesWithForms.length > 0) {
        errors.push('Cannot create form for category because forms already exist for its subcategories');
      }

      // Rule 4: Check if category already has a form
      if (this.hasFormForCategory(categoryId)) {
        errors.push('A form already exists for this category');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Get form configuration for category or subcategory
  getFormConfigForSelection(divisionId, categoryId, subCategoryId = null) {
    // divisionId is kept for future use and API consistency
    // First try to get subcategory-specific form if subcategory is selected
    if (subCategoryId) {
      const subCategoryForm = this.loadFormConfig('subcategory', subCategoryId);
      if (subCategoryForm) {
        return subCategoryForm;
      }
    }

    // Then try category-specific form
    const categoryForm = this.loadFormConfig('category', categoryId);
    if (categoryForm) {
      return categoryForm;
    }

    // Return default form if no custom form found
    return this.getDefaultFormConfig();
  }

  // Get default form configuration with all fields
  getDefaultFormConfig() {
    const allFields = getAllPersonFields();

    // Deduplicate fields to ensure no duplicates
    const deduplicatedFields = this.deduplicateFields(allFields);

    return {
      id: 'default',
      type: 'default',
      name: 'Default Person Form',
      description: 'Default form with all person fields',
      fields: deduplicatedFields,
      sections: Object.keys(PersonFieldDefinitions),
      settings: {
        showSections: true,
        allowConditionalFields: true,
        validateOnChange: true
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      version: 1
    };
  }

  // Create form configuration from field selection
  createFormConfigFromFields(selectedFields, settings = {}) {
    const config = {
      fields: selectedFields,
      sections: this.groupFieldsBySections(selectedFields),
      settings: {
        showSections: true,
        allowConditionalFields: true,
        validateOnChange: true,
        ...settings
      }
    };

    return config;
  }

  // Deduplicate fields based on field key
  deduplicateFields(fields) {
    const seen = new Set();
    const deduplicated = [];

    fields.forEach(field => {
      if (!seen.has(field.key)) {
        seen.add(field.key);
        deduplicated.push(field);
      }
    });

    return deduplicated;
  }

  // Group fields by their sections
  groupFieldsBySections(fields) {
    const sections = {};

    fields.forEach(field => {
      const sectionKey = field.section;
      if (!sections[sectionKey]) {
        sections[sectionKey] = {
          key: sectionKey,
          title: PersonFieldDefinitions[sectionKey]?.title || sectionKey,
          fields: []
        };
      }
      sections[sectionKey].fields.push(field);
    });

    return Object.values(sections);
  }

  // Validate form configuration
  validateFormConfig(config) {
    const errors = [];

    if (!config.name || config.name.trim() === '') {
      errors.push('Form name is required');
    }

    if (!config.fields || config.fields.length === 0) {
      errors.push('At least one field must be selected');
    }

    // Check if required fields are included
    const requiredFields = ['name', 'mobileNumber', 'nature'];
    const selectedFieldKeys = config.fields.map(f => f.key);
    
    requiredFields.forEach(requiredField => {
      if (!selectedFieldKeys.includes(requiredField)) {
        errors.push(`Required field '${requiredField}' must be included`);
      }
    });

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Export form configuration
  exportFormConfig(type, id) {
    const config = this.loadFormConfig(type, id);
    if (!config) {
      throw new Error('Form configuration not found');
    }

    const exportData = {
      ...config,
      exportedAt: new Date().toISOString(),
      exportVersion: '1.0'
    };

    return JSON.stringify(exportData, null, 2);
  }

  // Import form configuration
  importFormConfig(configJson, type, id) {
    try {
      const config = JSON.parse(configJson);
      
      // Validate imported configuration
      const validation = this.validateFormConfig(config);
      if (!validation.isValid) {
        throw new Error(`Invalid configuration: ${validation.errors.join(', ')}`);
      }

      // Update metadata for import
      config.type = type;
      config.associatedId = id;
      config.importedAt = new Date().toISOString();
      config.updatedAt = new Date().toISOString();

      return this.saveFormConfig(type, id, config);
    } catch (error) {
      console.error('Error importing form configuration:', error);
      throw new Error('Failed to import form configuration');
    }
  }

  // Clone form configuration
  cloneFormConfig(sourceType, sourceId, targetType, targetId, newName) {
    const sourceConfig = this.loadFormConfig(sourceType, sourceId);
    if (!sourceConfig) {
      throw new Error('Source form configuration not found');
    }

    const clonedConfig = {
      ...sourceConfig,
      name: newName || `${sourceConfig.name} (Copy)`,
      type: targetType,
      associatedId: targetId,
      clonedFrom: `${sourceType}_${sourceId}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    return this.saveFormConfig(targetType, targetId, clonedConfig);
  }

  // Get form statistics
  getFormStatistics() {
    const allConfigs = this.getAllFormConfigs();
    
    return {
      totalForms: allConfigs.length,
      categoryForms: allConfigs.filter(c => c.type === 'category').length,
      subCategoryForms: allConfigs.filter(c => c.type === 'subcategory').length,
      averageFieldCount: allConfigs.length > 0 
        ? Math.round(allConfigs.reduce((sum, c) => sum + (c.fields?.length || 0), 0) / allConfigs.length)
        : 0,
      mostUsedFields: this.getMostUsedFields(allConfigs),
      recentlyModified: allConfigs
        .sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt))
        .slice(0, 5)
    };
  }

  // Get most used fields across all forms
  getMostUsedFields(configs) {
    const fieldUsage = {};
    
    configs.forEach(config => {
      if (config.fields) {
        config.fields.forEach(field => {
          fieldUsage[field.key] = (fieldUsage[field.key] || 0) + 1;
        });
      }
    });

    return Object.entries(fieldUsage)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([key, count]) => ({ field: key, usage: count }));
  }

  // Get all saved form configurations
  getAllFormConfigs() {
    try {
      const forms = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith(this.storagePrefix)) {
          const stored = localStorage.getItem(key);
          if (stored) {
            const config = JSON.parse(stored);
            forms.push({
              key,
              ...config,
              summary: {
                fieldCount: config.fields?.length || 0,
                type: config.type,
                associatedId: config.associatedId,
                createdAt: config.createdAt,
                updatedAt: config.updatedAt
              }
            });
          }
        }
      }

      // Sort by updated date (most recent first)
      return forms.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt));
    } catch (error) {
      console.error('Error getting form configurations:', error);
      return [];
    }
  }

  // Delete a specific form configuration
  deleteFormConfig(type, id) {
    try {
      const key = this.getStorageKey(type, id);
      localStorage.removeItem(key);
      return true;
    } catch (error) {
      console.error('Error deleting form configuration:', error);
      return false;
    }
  }

  // Clear all form configurations (for development/testing)
  clearAllFormConfigs() {
    try {
      const keysToRemove = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith(this.storagePrefix)) {
          keysToRemove.push(key);
        }
      }

      keysToRemove.forEach(key => localStorage.removeItem(key));
      return true;
    } catch (error) {
      console.error('Error clearing form configurations:', error);
      return false;
    }
  }
}

export default new FormConfigService();
