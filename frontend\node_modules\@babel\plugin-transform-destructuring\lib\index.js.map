{"version": 3, "file": "index.js", "sources": ["../src/util.ts", "../src/index.ts"], "sourcesContent": ["import { types as t, template } from \"@babel/core\";\nimport type { File, Scope, NodePath } from \"@babel/core\";\n\nfunction isPureVoid(node: t.Node) {\n  return (\n    t.isUnaryExpression(node) &&\n    node.operator === \"void\" &&\n    t.isPureish(node.argument)\n  );\n}\n\nexport function unshiftForXStatementBody(\n  statementPath: NodePath<t.ForXStatement>,\n  newStatements: t.Statement[],\n) {\n  statementPath.ensureBlock();\n  const { scope, node } = statementPath;\n  const bodyScopeBindings = statementPath.get(\"body\").scope.bindings;\n  const hasShadowedBlockScopedBindings = Object.keys(bodyScopeBindings).some(\n    name => scope.hasBinding(name),\n  );\n\n  if (hasShadowedBlockScopedBindings) {\n    // handle shadowed variables referenced in computed keys:\n    // var a = 0;for (const { #x: x, [a++]: y } of z) { const a = 1; }\n    node.body = t.blockStatement([...newStatements, node.body]);\n  } else {\n    (node.body as t.BlockStatement).body.unshift(...newStatements);\n  }\n}\n\n/**\n * Test if an ArrayPattern's elements contain any RestElements.\n */\n\nfunction hasArrayRest(pattern: t.ArrayPattern) {\n  return pattern.elements.some(elem => t.isRestElement(elem));\n}\n\n/**\n * Test if an ObjectPattern's properties contain any RestElements.\n */\n\nfunction hasObjectRest(pattern: t.ObjectPattern) {\n  return pattern.properties.some(prop => t.isRestElement(prop));\n}\n\ninterface UnpackableArrayExpression extends t.ArrayExpression {\n  elements: (null | t.Expression)[];\n}\n\nconst STOP_TRAVERSAL = {};\n\ninterface ArrayUnpackVisitorState {\n  deopt: boolean;\n  bindings: Record<string, t.Identifier>;\n}\n\n// NOTE: This visitor is meant to be used via t.traverse\nconst arrayUnpackVisitor = (\n  node: t.Node,\n  ancestors: t.TraversalAncestors,\n  state: ArrayUnpackVisitorState,\n) => {\n  if (!ancestors.length) {\n    // Top-level node: this is the array literal.\n    return;\n  }\n\n  if (\n    t.isIdentifier(node) &&\n    t.isReferenced(node, ancestors[ancestors.length - 1].node) &&\n    state.bindings[node.name]\n  ) {\n    state.deopt = true;\n    // eslint-disable-next-line @typescript-eslint/only-throw-error\n    throw STOP_TRAVERSAL;\n  }\n};\n\nexport type DestructuringTransformerNode =\n  | t.VariableDeclaration\n  | t.ExpressionStatement\n  | t.ReturnStatement;\n\n// using/await using declaration must not contain pattern as its id.\nexport type VariableDeclarationKindAllowsPattern = Exclude<\n  t.VariableDeclaration[\"kind\"],\n  \"using\" | \"await using\"\n>;\n\ninterface DestructuringTransformerOption {\n  blockHoist?: number;\n  operator?: t.AssignmentExpression[\"operator\"];\n  nodes?: DestructuringTransformerNode[];\n  kind?: VariableDeclarationKindAllowsPattern;\n  scope: Scope;\n  arrayLikeIsIterable: boolean;\n  iterableIsArray: boolean;\n  objectRestNoSymbols: boolean;\n  useBuiltIns: boolean;\n  addHelper: File[\"addHelper\"];\n}\nexport class DestructuringTransformer {\n  private blockHoist: number;\n  private operator: t.AssignmentExpression[\"operator\"];\n  arrayRefSet: Set<string>;\n  private nodes: DestructuringTransformerNode[];\n  private scope: Scope;\n  private kind: VariableDeclarationKindAllowsPattern;\n  private iterableIsArray: boolean;\n  private arrayLikeIsIterable: boolean;\n  private objectRestNoSymbols: boolean;\n  private useBuiltIns: boolean;\n  private addHelper: File[\"addHelper\"];\n  constructor(opts: DestructuringTransformerOption) {\n    this.blockHoist = opts.blockHoist;\n    this.operator = opts.operator;\n    this.arrayRefSet = new Set();\n    this.nodes = opts.nodes || [];\n    this.scope = opts.scope;\n    this.kind = opts.kind;\n    this.iterableIsArray = opts.iterableIsArray;\n    this.arrayLikeIsIterable = opts.arrayLikeIsIterable;\n    this.objectRestNoSymbols = opts.objectRestNoSymbols;\n    this.useBuiltIns = opts.useBuiltIns;\n    this.addHelper = opts.addHelper;\n  }\n\n  getExtendsHelper() {\n    return this.useBuiltIns\n      ? t.memberExpression(t.identifier(\"Object\"), t.identifier(\"assign\"))\n      : this.addHelper(\"extends\");\n  }\n\n  buildVariableAssignment(\n    id: t.AssignmentExpression[\"left\"],\n    init: t.Expression,\n  ) {\n    let op = this.operator;\n    if (t.isMemberExpression(id) || t.isOptionalMemberExpression(id)) op = \"=\";\n\n    let node: t.ExpressionStatement | t.VariableDeclaration;\n\n    if (op) {\n      node = t.expressionStatement(\n        t.assignmentExpression(\n          op,\n          id,\n          t.cloneNode(init) || this.scope.buildUndefinedNode(),\n        ),\n      );\n    } else {\n      let nodeInit: t.Expression;\n\n      if (this.kind === \"const\" && init === null) {\n        nodeInit = this.scope.buildUndefinedNode();\n      } else {\n        nodeInit = t.cloneNode(init);\n      }\n\n      node = t.variableDeclaration(this.kind, [\n        t.variableDeclarator(\n          id as t.Identifier | t.ArrayPattern | t.ObjectPattern,\n          nodeInit,\n        ),\n      ]);\n    }\n\n    //@ts-expect-error(todo): document block hoist property\n    node._blockHoist = this.blockHoist;\n\n    return node;\n  }\n\n  buildVariableDeclaration(id: t.Identifier, init: t.Expression) {\n    const declar = t.variableDeclaration(\"var\", [\n      t.variableDeclarator(t.cloneNode(id), t.cloneNode(init)),\n    ]);\n    // @ts-expect-error todo(flow->ts): avoid mutations\n    declar._blockHoist = this.blockHoist;\n    return declar;\n  }\n\n  push(id: t.LVal | t.PatternLike, _init: t.Expression | null) {\n    const init = t.cloneNode(_init);\n    if (t.isObjectPattern(id)) {\n      this.pushObjectPattern(id, init);\n    } else if (t.isArrayPattern(id)) {\n      this.pushArrayPattern(id, init);\n    } else if (t.isAssignmentPattern(id)) {\n      this.pushAssignmentPattern(id, init);\n    } else {\n      this.nodes.push(\n        this.buildVariableAssignment(\n          id as t.AssignmentExpression[\"left\"],\n          init,\n        ),\n      );\n    }\n  }\n\n  toArray(node: t.Expression, count?: false | number) {\n    if (\n      this.iterableIsArray ||\n      (t.isIdentifier(node) && this.arrayRefSet.has(node.name))\n    ) {\n      return node;\n    } else {\n      const { scope, arrayLikeIsIterable } = this;\n\n      if (t.isIdentifier(node)) {\n        const binding = scope.getBinding(node.name);\n        if (binding?.constant && binding.path.isGenericType(\"Array\")) {\n          return node;\n        }\n      }\n\n      if (t.isArrayExpression(node)) {\n        return node;\n      }\n\n      if (t.isIdentifier(node, { name: \"arguments\" })) {\n        return template.expression.ast`\n          Array.prototype.slice.call(${node})\n        `;\n      }\n\n      let helperName;\n      const args = [node];\n      if (typeof count === \"number\") {\n        args.push(t.numericLiteral(count));\n\n        // Used in array-rest to create an array from a subset of an iterable.\n        helperName = \"slicedToArray\";\n        // TODO if (this.hub.isLoose(\"es6.forOf\")) helperName += \"-loose\";\n      } else {\n        // Used in array-rest to create an array\n        helperName = \"toArray\";\n      }\n\n      if (arrayLikeIsIterable) {\n        args.unshift(scope.path.hub.addHelper(helperName));\n        helperName = \"maybeArrayLike\";\n      }\n\n      return t.callExpression(scope.path.hub.addHelper(helperName), args);\n    }\n  }\n\n  pushAssignmentPattern(\n    { left, right }: t.AssignmentPattern,\n    valueRef: t.Expression | null,\n  ) {\n    // handle array init with void 0. This also happens when\n    // the value was originally a hole.\n    // const [x = 42] = [void 0,];\n    // -> const x = 42;\n    if (isPureVoid(valueRef)) {\n      this.push(left, right);\n      return;\n    }\n\n    // we need to assign the current value of the assignment to avoid evaluating\n    // it more than once\n    const tempId = this.scope.generateUidIdentifierBasedOnNode(valueRef);\n\n    this.nodes.push(this.buildVariableDeclaration(tempId, valueRef));\n\n    const tempConditional = t.conditionalExpression(\n      t.binaryExpression(\n        \"===\",\n        t.cloneNode(tempId),\n        this.scope.buildUndefinedNode(),\n      ),\n      right,\n      t.cloneNode(tempId),\n    );\n\n    if (t.isPattern(left)) {\n      let patternId;\n      let node;\n\n      if (this.kind === \"const\" || this.kind === \"let\") {\n        patternId = this.scope.generateUidIdentifier(tempId.name);\n        node = this.buildVariableDeclaration(patternId, tempConditional);\n      } else {\n        patternId = tempId;\n\n        node = t.expressionStatement(\n          t.assignmentExpression(\"=\", t.cloneNode(tempId), tempConditional),\n        );\n      }\n\n      this.nodes.push(node);\n      this.push(left, patternId);\n    } else {\n      this.nodes.push(this.buildVariableAssignment(left, tempConditional));\n    }\n  }\n\n  pushObjectRest(\n    pattern: t.ObjectPattern,\n    objRef: t.Expression,\n    spreadProp: t.RestElement,\n    spreadPropIndex: number,\n  ) {\n    const value = buildObjectExcludingKeys(\n      pattern.properties.slice(0, spreadPropIndex) as t.ObjectProperty[],\n      objRef,\n      this.scope,\n      name => this.addHelper(name),\n      this.objectRestNoSymbols,\n      this.useBuiltIns,\n    );\n    this.nodes.push(this.buildVariableAssignment(spreadProp.argument, value));\n  }\n\n  pushObjectProperty(prop: t.ObjectProperty, propRef: t.Expression) {\n    if (t.isLiteral(prop.key)) prop.computed = true;\n\n    const pattern = prop.value as t.AssignmentExpression[\"left\"];\n    const objRef = t.memberExpression(\n      t.cloneNode(propRef),\n      prop.key,\n      prop.computed,\n    );\n\n    if (t.isPattern(pattern)) {\n      this.push(pattern, objRef);\n    } else {\n      this.nodes.push(this.buildVariableAssignment(pattern, objRef));\n    }\n  }\n\n  pushObjectPattern(pattern: t.ObjectPattern, objRef: t.Expression) {\n    // https://github.com/babel/babel/issues/681\n\n    if (!pattern.properties.length) {\n      this.nodes.push(\n        t.expressionStatement(\n          t.callExpression(\n            this.addHelper(\"objectDestructuringEmpty\"),\n            isPureVoid(objRef) ? [] : [objRef],\n          ),\n        ),\n      );\n      return;\n    }\n\n    // if we have more than one properties in this pattern and the objectRef is a\n    // member expression then we need to assign it to a temporary variable so it's\n    // only evaluated once\n\n    if (pattern.properties.length > 1 && !this.scope.isStatic(objRef)) {\n      const temp = this.scope.generateUidIdentifierBasedOnNode(objRef);\n      this.nodes.push(this.buildVariableDeclaration(temp, objRef));\n      objRef = temp;\n    }\n\n    // Replace impure computed key expressions if we have a rest parameter\n    if (hasObjectRest(pattern)) {\n      let copiedPattern: t.ObjectPattern;\n      for (let i = 0; i < pattern.properties.length; i++) {\n        const prop = pattern.properties[i];\n        if (t.isRestElement(prop)) {\n          break;\n        }\n        const key = prop.key;\n        if (prop.computed && !this.scope.isPure(key)) {\n          const name = this.scope.generateUidIdentifierBasedOnNode(key);\n          this.nodes.push(\n            //@ts-expect-error PrivateName has been handled by destructuring-private\n            this.buildVariableDeclaration(name, key),\n          );\n          if (!copiedPattern) {\n            copiedPattern = pattern = {\n              ...pattern,\n              properties: pattern.properties.slice(),\n            };\n          }\n          copiedPattern.properties[i] = {\n            ...prop,\n            key: name,\n          };\n        }\n      }\n    }\n\n    for (let i = 0; i < pattern.properties.length; i++) {\n      const prop = pattern.properties[i];\n      if (t.isRestElement(prop)) {\n        this.pushObjectRest(pattern, objRef, prop, i);\n      } else {\n        this.pushObjectProperty(prop, objRef);\n      }\n    }\n  }\n\n  canUnpackArrayPattern(\n    pattern: t.ArrayPattern,\n    arr: t.Expression,\n  ): arr is UnpackableArrayExpression {\n    // not an array so there's no way we can deal with this\n    if (!t.isArrayExpression(arr)) return false;\n\n    // pattern has less elements than the array and doesn't have a rest so some\n    // elements won't be evaluated\n    if (pattern.elements.length > arr.elements.length) return;\n    if (\n      pattern.elements.length < arr.elements.length &&\n      !hasArrayRest(pattern)\n    ) {\n      return false;\n    }\n\n    for (const elem of pattern.elements) {\n      // deopt on holes\n      if (!elem) return false;\n\n      // deopt on member expressions as they may be included in the RHS\n      if (t.isMemberExpression(elem)) return false;\n    }\n\n    for (const elem of arr.elements) {\n      // deopt on spread elements\n      if (t.isSpreadElement(elem)) return false;\n\n      // deopt call expressions as they might change values of LHS variables\n      if (t.isCallExpression(elem)) return false;\n\n      // deopt on member expressions as they may be getter/setters and have side-effects\n      if (t.isMemberExpression(elem)) return false;\n    }\n\n    // deopt on reference to left side identifiers\n    const bindings = t.getBindingIdentifiers(pattern);\n    const state: ArrayUnpackVisitorState = { deopt: false, bindings };\n\n    try {\n      t.traverse(arr, arrayUnpackVisitor, state);\n    } catch (e) {\n      if (e !== STOP_TRAVERSAL) throw e;\n    }\n\n    return !state.deopt;\n  }\n\n  pushUnpackedArrayPattern(\n    pattern: t.ArrayPattern,\n    arr: UnpackableArrayExpression,\n  ) {\n    const holeToUndefined = (el: t.Expression) =>\n      el ?? this.scope.buildUndefinedNode();\n\n    for (let i = 0; i < pattern.elements.length; i++) {\n      const elem = pattern.elements[i];\n      if (t.isRestElement(elem)) {\n        this.push(\n          elem.argument,\n          t.arrayExpression(arr.elements.slice(i).map(holeToUndefined)),\n        );\n      } else {\n        this.push(elem, holeToUndefined(arr.elements[i]));\n      }\n    }\n  }\n\n  pushArrayPattern(pattern: t.ArrayPattern, arrayRef: t.Expression | null) {\n    if (arrayRef === null) {\n      this.nodes.push(\n        t.expressionStatement(\n          t.callExpression(this.addHelper(\"objectDestructuringEmpty\"), []),\n        ),\n      );\n      return;\n    }\n    if (!pattern.elements) return;\n\n    // optimise basic array destructuring of an array expression\n    //\n    // we can't do this to a pattern of unequal size to it's right hand\n    // array expression as then there will be values that won't be evaluated\n    //\n    // eg: let [a, b] = [1, 2];\n\n    if (this.canUnpackArrayPattern(pattern, arrayRef)) {\n      this.pushUnpackedArrayPattern(pattern, arrayRef);\n      return;\n    }\n\n    // if we have a rest then we need all the elements so don't tell\n    // `scope.toArray` to only get a certain amount\n\n    const count = !hasArrayRest(pattern) && pattern.elements.length;\n\n    // so we need to ensure that the `arrayRef` is an array, `scope.toArray` will\n    // return a locally bound identifier if it's been inferred to be an array,\n    // otherwise it'll be a call to a helper that will ensure it's one\n\n    const toArray = this.toArray(arrayRef, count);\n\n    if (t.isIdentifier(toArray)) {\n      // we've been given an identifier so it must have been inferred to be an\n      // array\n      arrayRef = toArray;\n    } else {\n      arrayRef = this.scope.generateUidIdentifierBasedOnNode(arrayRef);\n      this.arrayRefSet.add(arrayRef.name);\n      this.nodes.push(this.buildVariableDeclaration(arrayRef, toArray));\n    }\n\n    for (let i = 0; i < pattern.elements.length; i++) {\n      const elem = pattern.elements[i];\n\n      // hole\n      if (!elem) continue;\n\n      let elemRef;\n\n      if (t.isRestElement(elem)) {\n        elemRef = this.toArray(arrayRef);\n        elemRef = t.callExpression(\n          t.memberExpression(elemRef, t.identifier(\"slice\")),\n          [t.numericLiteral(i)],\n        );\n\n        // set the element to the rest element argument since we've dealt with it\n        // being a rest already\n        this.push(elem.argument, elemRef);\n      } else {\n        elemRef = t.memberExpression(arrayRef, t.numericLiteral(i), true);\n        this.push(elem, elemRef);\n      }\n    }\n  }\n\n  init(pattern: t.LVal, ref: t.Expression) {\n    // trying to destructure a value that we can't evaluate more than once so we\n    // need to save it to a variable\n\n    if (!t.isArrayExpression(ref) && !t.isMemberExpression(ref)) {\n      const memo = this.scope.maybeGenerateMemoised(ref, true);\n      if (memo) {\n        this.nodes.push(this.buildVariableDeclaration(memo, t.cloneNode(ref)));\n        ref = memo;\n      }\n    }\n\n    this.push(pattern, ref);\n\n    return this.nodes;\n  }\n}\n\ninterface ExcludingKey {\n  key: t.Expression | t.PrivateName;\n  computed: boolean;\n}\n\nexport function buildObjectExcludingKeys<T extends ExcludingKey>(\n  excludedKeys: T[],\n  objRef: t.Expression,\n  scope: Scope,\n  addHelper: File[\"addHelper\"],\n  objectRestNoSymbols: boolean,\n  useBuiltIns: boolean,\n): t.CallExpression {\n  // get all the keys that appear in this object before the current spread\n\n  const keys = [];\n  let allLiteral = true;\n  let hasTemplateLiteral = false;\n  for (let i = 0; i < excludedKeys.length; i++) {\n    const prop = excludedKeys[i];\n    const key = prop.key;\n    if (t.isIdentifier(key) && !prop.computed) {\n      keys.push(t.stringLiteral(key.name));\n    } else if (t.isTemplateLiteral(key)) {\n      keys.push(t.cloneNode(key));\n      hasTemplateLiteral = true;\n    } else if (t.isLiteral(key)) {\n      // @ts-expect-error todo(flow->ts) NullLiteral\n      keys.push(t.stringLiteral(String(key.value)));\n    } else if (t.isPrivateName(key)) {\n      // private key is not enumerable\n    } else {\n      keys.push(t.cloneNode(key));\n      allLiteral = false;\n    }\n  }\n\n  let value;\n  if (keys.length === 0) {\n    const extendsHelper = useBuiltIns\n      ? t.memberExpression(t.identifier(\"Object\"), t.identifier(\"assign\"))\n      : addHelper(\"extends\");\n    value = t.callExpression(extendsHelper, [\n      t.objectExpression([]),\n      t.sequenceExpression([\n        t.callExpression(addHelper(\"objectDestructuringEmpty\"), [\n          t.cloneNode(objRef),\n        ]),\n        t.cloneNode(objRef),\n      ]),\n    ]);\n  } else {\n    let keyExpression: t.Expression = t.arrayExpression(keys);\n\n    if (!allLiteral) {\n      keyExpression = t.callExpression(\n        t.memberExpression(keyExpression, t.identifier(\"map\")),\n        [addHelper(\"toPropertyKey\")],\n      );\n    } else if (!hasTemplateLiteral && !t.isProgram(scope.block)) {\n      // Hoist definition of excluded keys, so that it's not created each time.\n      const programScope = scope.getProgramParent();\n      const id = programScope.generateUidIdentifier(\"excluded\");\n\n      programScope.push({\n        id,\n        init: keyExpression,\n        kind: \"const\",\n      });\n\n      keyExpression = t.cloneNode(id);\n    }\n\n    value = t.callExpression(\n      addHelper(`objectWithoutProperties${objectRestNoSymbols ? \"Loose\" : \"\"}`),\n      [t.cloneNode(objRef), keyExpression],\n    );\n  }\n  return value;\n}\n\nexport function convertVariableDeclaration(\n  path: NodePath<t.VariableDeclaration>,\n  addHelper: File[\"addHelper\"],\n  arrayLikeIsIterable: boolean,\n  iterableIsArray: boolean,\n  objectRestNoSymbols: boolean,\n  useBuiltIns: boolean,\n) {\n  const { node, scope } = path;\n\n  const nodeKind = node.kind;\n  const nodeLoc = node.loc;\n  const nodes = [];\n\n  for (let i = 0; i < node.declarations.length; i++) {\n    const declar = node.declarations[i];\n\n    const patternId = declar.init;\n    const pattern = declar.id;\n\n    const destructuring: DestructuringTransformer =\n      new DestructuringTransformer({\n        // @ts-expect-error(todo): avoid internal properties access\n        blockHoist: node._blockHoist,\n        nodes: nodes,\n        scope: scope,\n        kind: node.kind as VariableDeclarationKindAllowsPattern,\n        iterableIsArray,\n        arrayLikeIsIterable,\n        useBuiltIns,\n        objectRestNoSymbols,\n        addHelper,\n      });\n\n    if (t.isPattern(pattern)) {\n      // variableDeclarationHasDestructuringPattern ensures that the pattern is not a VoidPattern\n      destructuring.init(pattern as Exclude<t.LVal, t.VoidPattern>, patternId);\n\n      if (+i !== node.declarations.length - 1) {\n        // we aren't the last declarator so let's just make the\n        // last transformed node inherit from us\n        t.inherits(nodes[nodes.length - 1], declar);\n      }\n    } else {\n      nodes.push(\n        t.inherits(\n          destructuring.buildVariableAssignment(pattern, patternId),\n          declar,\n        ),\n      );\n    }\n  }\n\n  let tail: t.VariableDeclaration | null = null;\n  let nodesOut = [];\n  for (const node of nodes) {\n    if (t.isVariableDeclaration(node)) {\n      if (tail !== null) {\n        // Create a single compound declarations\n        tail.declarations.push(...node.declarations);\n        continue;\n      } else {\n        // Make sure the original node kind is used for each compound declaration\n        node.kind = nodeKind;\n        tail = node;\n      }\n    } else {\n      tail = null;\n    }\n    // Propagate the original declaration node's location\n    if (!node.loc) {\n      node.loc = nodeLoc;\n    }\n    nodesOut.push(node);\n  }\n\n  if (\n    nodesOut.length === 2 &&\n    t.isVariableDeclaration(nodesOut[0]) &&\n    t.isExpressionStatement(nodesOut[1]) &&\n    t.isCallExpression(nodesOut[1].expression) &&\n    nodesOut[0].declarations.length === 1\n  ) {\n    // This can only happen when we generate this code:\n    //    var _ref = DESTRUCTURED_VALUE;\n    //     babelHelpers.objectDestructuringEmpty(_ref);\n    // Since pushing those two statements to the for loop .init will require an IIFE,\n    // we can optimize them to\n    //     babelHelpers.objectDestructuringEmpty(DESTRUCTURED_VALUE);\n    const expr = nodesOut[1].expression;\n    expr.arguments = [nodesOut[0].declarations[0].init];\n    nodesOut = [expr];\n  } else {\n    // We must keep nodes all are expressions or statements, so `replaceWithMultiple` can work.\n    if (\n      t.isForStatement(path.parent, { init: node }) &&\n      !nodesOut.some(v => t.isVariableDeclaration(v))\n    ) {\n      for (let i = 0; i < nodesOut.length; i++) {\n        const node: t.Node = nodesOut[i];\n        if (t.isExpressionStatement(node)) {\n          nodesOut[i] = node.expression;\n        }\n      }\n    }\n  }\n\n  if (nodesOut.length === 1) {\n    path.replaceWith(nodesOut[0]);\n  } else {\n    path.replaceWithMultiple(nodesOut);\n  }\n  scope.crawl();\n}\n\nexport function convertAssignmentExpression(\n  path: NodePath<t.AssignmentExpression & { left: t.Pattern }>,\n  addHelper: File[\"addHelper\"],\n  arrayLikeIsIterable: boolean,\n  iterableIsArray: boolean,\n  objectRestNoSymbols: boolean,\n  useBuiltIns: boolean,\n) {\n  const { node, scope, parentPath } = path;\n\n  const nodes: DestructuringTransformerNode[] = [];\n\n  const destructuring = new DestructuringTransformer({\n    operator: node.operator,\n    scope: scope,\n    nodes: nodes,\n    arrayLikeIsIterable,\n    iterableIsArray,\n    objectRestNoSymbols,\n    useBuiltIns,\n    addHelper,\n  });\n\n  let ref: t.Identifier | void;\n  if (\n    (!parentPath.isExpressionStatement() &&\n      !parentPath.isSequenceExpression()) ||\n    path.isCompletionRecord()\n  ) {\n    ref = scope.generateUidIdentifierBasedOnNode(node.right, \"ref\");\n\n    nodes.push(\n      t.variableDeclaration(\"var\", [t.variableDeclarator(ref, node.right)]),\n    );\n\n    if (t.isArrayExpression(node.right)) {\n      destructuring.arrayRefSet.add(ref.name);\n    }\n  }\n\n  destructuring.init(node.left, ref || node.right);\n\n  if (ref) {\n    if (parentPath.isArrowFunctionExpression()) {\n      path.replaceWith(t.blockStatement([]));\n      nodes.push(t.returnStatement(t.cloneNode(ref)));\n    } else {\n      nodes.push(t.expressionStatement(t.cloneNode(ref)));\n    }\n  }\n\n  path.replaceWithMultiple(nodes);\n  scope.crawl();\n}\n", "import { declare } from \"@babel/helper-plugin-utils\";\nimport { types as t, type NodePath } from \"@babel/core\";\nimport {\n  DestructuringTransformer,\n  convertVariableDeclaration,\n  convertAssignmentExpression,\n  unshiftForXStatementBody,\n  type DestructuringTransformerNode,\n  type VariableDeclarationKindAllowsPattern,\n} from \"./util.ts\";\nexport { buildObjectExcludingKeys, unshiftForXStatementBody } from \"./util.ts\";\n\n/**\n * Test if a VariableDeclaration's declarations contains any destructuring patterns.\n * @param node VariableDeclaration node to test\n */\nfunction variableDeclarationHasDestructuringPattern(\n  node: t.VariableDeclaration,\n) {\n  for (const declar of node.declarations) {\n    if (t.isPattern(declar.id) && declar.id.type !== \"VoidPattern\") {\n      return true;\n    }\n  }\n  return false;\n}\n\nexport interface Options {\n  allowArrayLike?: boolean;\n  loose?: boolean;\n  useBuiltIns?: boolean;\n}\n\nexport default declare((api, options: Options) => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  const { useBuiltIns = false } = options;\n\n  const iterableIsArray =\n    api.assumption(\"iterableIsArray\") ?? options.loose ?? false;\n  const arrayLikeIsIterable =\n    options.allowArrayLike ?? api.assumption(\"arrayLikeIsIterable\") ?? false;\n  const objectRestNoSymbols =\n    api.assumption(\"objectRestNoSymbols\") ?? options.loose ?? false;\n\n  return {\n    name: \"transform-destructuring\",\n\n    visitor: {\n      ExportNamedDeclaration(path) {\n        const declaration = path.get(\"declaration\");\n        if (!declaration.isVariableDeclaration()) return;\n        if (!variableDeclarationHasDestructuringPattern(declaration.node))\n          return;\n\n        // Split the declaration and export list into two declarations so that the variable\n        // declaration can be split up later without needing to worry about not being a\n        // top-level statement.\n        if (!process.env.BABEL_8_BREAKING && !USE_ESM && !IS_STANDALONE) {\n          // polyfill when being run by an older Babel version\n          path.splitExportDeclaration ??=\n            // eslint-disable-next-line no-restricted-globals\n            require(\"@babel/traverse\").NodePath.prototype.splitExportDeclaration;\n        }\n        path.splitExportDeclaration();\n      },\n\n      ForXStatement(path: NodePath<t.ForXStatement>) {\n        const { node, scope } = path;\n        const left = node.left;\n\n        if (t.isPattern(left)) {\n          // for ({ length: k } in { abc: 3 });\n\n          const temp = scope.generateUidIdentifier(\"ref\");\n\n          node.left = t.variableDeclaration(\"var\", [\n            t.variableDeclarator(temp),\n          ]);\n\n          path.ensureBlock();\n          const statementBody = (path.node.body as t.BlockStatement).body;\n          const nodes = [];\n          // todo: the completion of a for statement can only be observed from\n          // a do block (or eval that we don't support),\n          // but the new do-expression proposal plans to ban iteration ends in the\n          // do block, maybe we can get rid of this\n          if (statementBody.length === 0 && path.isCompletionRecord()) {\n            nodes.unshift(t.expressionStatement(scope.buildUndefinedNode()));\n          }\n\n          nodes.unshift(\n            t.expressionStatement(\n              t.assignmentExpression(\"=\", left, t.cloneNode(temp)),\n            ),\n          );\n\n          unshiftForXStatementBody(path, nodes);\n          scope.crawl();\n          return;\n        }\n\n        if (!t.isVariableDeclaration(left)) return;\n\n        const pattern = left.declarations[0].id;\n        if (!t.isPattern(pattern) || pattern.type === \"VoidPattern\") return;\n\n        const key = scope.generateUidIdentifier(\"ref\");\n        node.left = t.variableDeclaration(left.kind, [\n          t.variableDeclarator(key, null),\n        ]);\n\n        const nodes: DestructuringTransformerNode[] = [];\n\n        const destructuring = new DestructuringTransformer({\n          kind: left.kind as VariableDeclarationKindAllowsPattern,\n          scope: scope,\n          nodes: nodes,\n          arrayLikeIsIterable,\n          iterableIsArray,\n          objectRestNoSymbols,\n          useBuiltIns,\n          addHelper: name => this.addHelper(name),\n        });\n\n        destructuring.init(pattern, key);\n\n        unshiftForXStatementBody(path, nodes);\n        scope.crawl();\n      },\n\n      CatchClause({ node, scope }) {\n        const pattern = node.param;\n        if (!t.isPattern(pattern)) return;\n\n        const ref = scope.generateUidIdentifier(\"ref\");\n        node.param = ref;\n\n        const nodes: DestructuringTransformerNode[] = [];\n\n        const destructuring = new DestructuringTransformer({\n          kind: \"let\",\n          scope: scope,\n          nodes: nodes,\n          arrayLikeIsIterable,\n          iterableIsArray,\n          objectRestNoSymbols,\n          useBuiltIns,\n          addHelper: name => this.addHelper(name),\n        });\n        destructuring.init(pattern, ref);\n\n        node.body.body = [...nodes, ...node.body.body];\n        scope.crawl();\n      },\n\n      AssignmentExpression(path, state) {\n        if (!t.isPattern(path.node.left)) return;\n        convertAssignmentExpression(\n          path as NodePath<t.AssignmentExpression & { left: t.Pattern }>,\n          name => state.addHelper(name),\n          arrayLikeIsIterable,\n          iterableIsArray,\n          objectRestNoSymbols,\n          useBuiltIns,\n        );\n      },\n\n      VariableDeclaration(path, state) {\n        const { node, parent } = path;\n        if (t.isForXStatement(parent)) return;\n        if (!parent || !path.container) return; // i don't know why this is necessary - TODO\n        if (!variableDeclarationHasDestructuringPattern(node)) return;\n        convertVariableDeclaration(\n          path,\n          name => state.addHelper(name),\n          arrayLikeIsIterable,\n          iterableIsArray,\n          objectRestNoSymbols,\n          useBuiltIns,\n        );\n      },\n    },\n  };\n});\n"], "names": ["isPureVoid", "node", "t", "isUnaryExpression", "operator", "isPureish", "argument", "unshiftForXStatementBody", "statementPath", "newStatements", "ensureBlock", "scope", "bodyScopeBindings", "get", "bindings", "hasShadowedBlockScopedBindings", "Object", "keys", "some", "name", "hasBinding", "body", "blockStatement", "unshift", "hasArrayRest", "pattern", "elements", "elem", "isRestElement", "hasObjectRest", "properties", "prop", "STOP_TRAVERSAL", "arrayUnpackVisitor", "ancestors", "state", "length", "isIdentifier", "isReferenced", "de<PERSON>t", "DestructuringTransformer", "constructor", "opts", "blockHoist", "arrayRefSet", "nodes", "kind", "iterableIsArray", "arrayLikeIsIterable", "objectRestNoSymbols", "useBuiltIns", "addHelper", "Set", "getExtendsHelper", "memberExpression", "identifier", "buildVariableAssignment", "id", "init", "op", "isMemberExpression", "isOptionalMemberExpression", "expressionStatement", "assignmentExpression", "cloneNode", "buildUndefinedNode", "nodeInit", "variableDeclaration", "variableDeclarator", "_blockHoist", "buildVariableDeclaration", "declar", "push", "_init", "isObjectPattern", "pushObjectPattern", "isArrayPattern", "pushArrayPattern", "isAssignmentPattern", "pushAssignmentPattern", "toArray", "count", "has", "binding", "getBinding", "constant", "path", "isGenericType", "isArrayExpression", "template", "expression", "ast", "helper<PERSON><PERSON>", "args", "numericLiteral", "hub", "callExpression", "left", "right", "valueRef", "tempId", "generateUidIdentifierBasedOnNode", "tempConditional", "conditionalExpression", "binaryExpression", "isPattern", "patternId", "generateUidIdentifier", "pushObjectRest", "objRef", "spreadProp", "spreadPropIndex", "value", "buildObjectExcludingKeys", "slice", "pushObjectProperty", "propRef", "isLiteral", "key", "computed", "isStatic", "temp", "copiedPattern", "i", "isPure", "assign", "canUnpackArrayPattern", "arr", "isSpreadElement", "isCallExpression", "getBindingIdentifiers", "traverse", "e", "pushUnpackedArrayPattern", "holeToUndefined", "el", "arrayExpression", "map", "arrayRef", "add", "elemRef", "ref", "memo", "maybeGenerateMemoised", "<PERSON><PERSON><PERSON><PERSON>", "allLiteral", "hasTemplateLiteral", "stringLiteral", "isTemplateLiteral", "String", "isPrivateName", "extendsHelper", "objectExpression", "sequenceExpression", "keyExpression", "isProgram", "block", "programScope", "getProgramParent", "convertVariableDeclaration", "nodeKind", "nodeLoc", "loc", "declarations", "destructuring", "inherits", "tail", "nodesOut", "isVariableDeclaration", "isExpressionStatement", "expr", "arguments", "isForStatement", "parent", "v", "replaceWith", "replaceWithMultiple", "crawl", "convertAssignmentExpression", "parentPath", "isSequenceExpression", "isCompletionRecord", "isArrowFunctionExpression", "returnStatement", "variableDeclarationHasDestructuringPattern", "type", "declare", "api", "options", "_ref", "_api$assumption", "_ref2", "_options$allowArrayLi", "_ref3", "_api$assumption2", "assertVersion", "assumption", "loose", "allowArrayLike", "visitor", "ExportNamedDeclaration", "declaration", "_path$splitExportDecl", "splitExportDeclaration", "require", "NodePath", "prototype", "ForXStatement", "statementBody", "CatchClause", "param", "AssignmentExpression", "VariableDeclaration", "isForXStatement", "container"], "mappings": ";;;;;;;AAGA,SAASA,UAAUA,CAACC,IAAY,EAAE;EAChC,OACEC,UAAC,CAACC,iBAAiB,CAACF,IAAI,CAAC,IACzBA,IAAI,CAACG,QAAQ,KAAK,MAAM,IACxBF,UAAC,CAACG,SAAS,CAACJ,IAAI,CAACK,QAAQ,CAAC,CAAA;AAE9B,CAAA;AAEO,SAASC,wBAAwBA,CACtCC,aAAwC,EACxCC,aAA4B,EAC5B;EACAD,aAAa,CAACE,WAAW,EAAE,CAAA;EAC3B,MAAM;IAAEC,KAAK;AAAEV,IAAAA,IAAAA;AAAK,GAAC,GAAGO,aAAa,CAAA;EACrC,MAAMI,iBAAiB,GAAGJ,aAAa,CAACK,GAAG,CAAC,MAAM,CAAC,CAACF,KAAK,CAACG,QAAQ,CAAA;AAClE,EAAA,MAAMC,8BAA8B,GAAGC,MAAM,CAACC,IAAI,CAACL,iBAAiB,CAAC,CAACM,IAAI,CACxEC,IAAI,IAAIR,KAAK,CAACS,UAAU,CAACD,IAAI,CAC/B,CAAC,CAAA;AAED,EAAA,IAAIJ,8BAA8B,EAAE;AAGlCd,IAAAA,IAAI,CAACoB,IAAI,GAAGnB,UAAC,CAACoB,cAAc,CAAC,CAAC,GAAGb,aAAa,EAAER,IAAI,CAACoB,IAAI,CAAC,CAAC,CAAA;AAC7D,GAAC,MAAM;IACJpB,IAAI,CAACoB,IAAI,CAAsBA,IAAI,CAACE,OAAO,CAAC,GAAGd,aAAa,CAAC,CAAA;AAChE,GAAA;AACF,CAAA;AAMA,SAASe,YAAYA,CAACC,OAAuB,EAAE;AAC7C,EAAA,OAAOA,OAAO,CAACC,QAAQ,CAACR,IAAI,CAACS,IAAI,IAAIzB,UAAC,CAAC0B,aAAa,CAACD,IAAI,CAAC,CAAC,CAAA;AAC7D,CAAA;AAMA,SAASE,aAAaA,CAACJ,OAAwB,EAAE;AAC/C,EAAA,OAAOA,OAAO,CAACK,UAAU,CAACZ,IAAI,CAACa,IAAI,IAAI7B,UAAC,CAAC0B,aAAa,CAACG,IAAI,CAAC,CAAC,CAAA;AAC/D,CAAA;AAMA,MAAMC,cAAc,GAAG,EAAE,CAAA;AAQzB,MAAMC,kBAAkB,GAAGA,CACzBhC,IAAY,EACZiC,SAA+B,EAC/BC,KAA8B,KAC3B;AACH,EAAA,IAAI,CAACD,SAAS,CAACE,MAAM,EAAE;AAErB,IAAA,OAAA;AACF,GAAA;AAEA,EAAA,IACElC,UAAC,CAACmC,YAAY,CAACpC,IAAI,CAAC,IACpBC,UAAC,CAACoC,YAAY,CAACrC,IAAI,EAAEiC,SAAS,CAACA,SAAS,CAACE,MAAM,GAAG,CAAC,CAAC,CAACnC,IAAI,CAAC,IAC1DkC,KAAK,CAACrB,QAAQ,CAACb,IAAI,CAACkB,IAAI,CAAC,EACzB;IACAgB,KAAK,CAACI,KAAK,GAAG,IAAI,CAAA;AAElB,IAAA,MAAMP,cAAc,CAAA;AACtB,GAAA;AACF,CAAC,CAAA;AAyBM,MAAMQ,wBAAwB,CAAC;EAYpCC,WAAWA,CAACC,IAAoC,EAAE;AAAA,IAAA,IAAA,CAX1CC,UAAU,GAAA,KAAA,CAAA,CAAA;AAAA,IAAA,IAAA,CACVvC,QAAQ,GAAA,KAAA,CAAA,CAAA;AAAA,IAAA,IAAA,CAChBwC,WAAW,GAAA,KAAA,CAAA,CAAA;AAAA,IAAA,IAAA,CACHC,KAAK,GAAA,KAAA,CAAA,CAAA;AAAA,IAAA,IAAA,CACLlC,KAAK,GAAA,KAAA,CAAA,CAAA;AAAA,IAAA,IAAA,CACLmC,IAAI,GAAA,KAAA,CAAA,CAAA;AAAA,IAAA,IAAA,CACJC,eAAe,GAAA,KAAA,CAAA,CAAA;AAAA,IAAA,IAAA,CACfC,mBAAmB,GAAA,KAAA,CAAA,CAAA;AAAA,IAAA,IAAA,CACnBC,mBAAmB,GAAA,KAAA,CAAA,CAAA;AAAA,IAAA,IAAA,CACnBC,WAAW,GAAA,KAAA,CAAA,CAAA;AAAA,IAAA,IAAA,CACXC,SAAS,GAAA,KAAA,CAAA,CAAA;AAEf,IAAA,IAAI,CAACR,UAAU,GAAGD,IAAI,CAACC,UAAU,CAAA;AACjC,IAAA,IAAI,CAACvC,QAAQ,GAAGsC,IAAI,CAACtC,QAAQ,CAAA;AAC7B,IAAA,IAAI,CAACwC,WAAW,GAAG,IAAIQ,GAAG,EAAE,CAAA;AAC5B,IAAA,IAAI,CAACP,KAAK,GAAGH,IAAI,CAACG,KAAK,IAAI,EAAE,CAAA;AAC7B,IAAA,IAAI,CAAClC,KAAK,GAAG+B,IAAI,CAAC/B,KAAK,CAAA;AACvB,IAAA,IAAI,CAACmC,IAAI,GAAGJ,IAAI,CAACI,IAAI,CAAA;AACrB,IAAA,IAAI,CAACC,eAAe,GAAGL,IAAI,CAACK,eAAe,CAAA;AAC3C,IAAA,IAAI,CAACC,mBAAmB,GAAGN,IAAI,CAACM,mBAAmB,CAAA;AACnD,IAAA,IAAI,CAACC,mBAAmB,GAAGP,IAAI,CAACO,mBAAmB,CAAA;AACnD,IAAA,IAAI,CAACC,WAAW,GAAGR,IAAI,CAACQ,WAAW,CAAA;AACnC,IAAA,IAAI,CAACC,SAAS,GAAGT,IAAI,CAACS,SAAS,CAAA;AACjC,GAAA;AAEAE,EAAAA,gBAAgBA,GAAG;AACjB,IAAA,OAAO,IAAI,CAACH,WAAW,GACnBhD,UAAC,CAACoD,gBAAgB,CAACpD,UAAC,CAACqD,UAAU,CAAC,QAAQ,CAAC,EAAErD,UAAC,CAACqD,UAAU,CAAC,QAAQ,CAAC,CAAC,GAClE,IAAI,CAACJ,SAAS,CAAC,SAAS,CAAC,CAAA;AAC/B,GAAA;AAEAK,EAAAA,uBAAuBA,CACrBC,EAAkC,EAClCC,IAAkB,EAClB;AACA,IAAA,IAAIC,EAAE,GAAG,IAAI,CAACvD,QAAQ,CAAA;AACtB,IAAA,IAAIF,UAAC,CAAC0D,kBAAkB,CAACH,EAAE,CAAC,IAAIvD,UAAC,CAAC2D,0BAA0B,CAACJ,EAAE,CAAC,EAAEE,EAAE,GAAG,GAAG,CAAA;AAE1E,IAAA,IAAI1D,IAAmD,CAAA;AAEvD,IAAA,IAAI0D,EAAE,EAAE;AACN1D,MAAAA,IAAI,GAAGC,UAAC,CAAC4D,mBAAmB,CAC1B5D,UAAC,CAAC6D,oBAAoB,CACpBJ,EAAE,EACFF,EAAE,EACFvD,UAAC,CAAC8D,SAAS,CAACN,IAAI,CAAC,IAAI,IAAI,CAAC/C,KAAK,CAACsD,kBAAkB,EACpD,CACF,CAAC,CAAA;AACH,KAAC,MAAM;AACL,MAAA,IAAIC,QAAsB,CAAA;MAE1B,IAAI,IAAI,CAACpB,IAAI,KAAK,OAAO,IAAIY,IAAI,KAAK,IAAI,EAAE;AAC1CQ,QAAAA,QAAQ,GAAG,IAAI,CAACvD,KAAK,CAACsD,kBAAkB,EAAE,CAAA;AAC5C,OAAC,MAAM;AACLC,QAAAA,QAAQ,GAAGhE,UAAC,CAAC8D,SAAS,CAACN,IAAI,CAAC,CAAA;AAC9B,OAAA;AAEAzD,MAAAA,IAAI,GAAGC,UAAC,CAACiE,mBAAmB,CAAC,IAAI,CAACrB,IAAI,EAAE,CACtC5C,UAAC,CAACkE,kBAAkB,CAClBX,EAAE,EACFS,QACF,CAAC,CACF,CAAC,CAAA;AACJ,KAAA;AAGAjE,IAAAA,IAAI,CAACoE,WAAW,GAAG,IAAI,CAAC1B,UAAU,CAAA;AAElC,IAAA,OAAO1C,IAAI,CAAA;AACb,GAAA;AAEAqE,EAAAA,wBAAwBA,CAACb,EAAgB,EAAEC,IAAkB,EAAE;AAC7D,IAAA,MAAMa,MAAM,GAAGrE,UAAC,CAACiE,mBAAmB,CAAC,KAAK,EAAE,CAC1CjE,UAAC,CAACkE,kBAAkB,CAAClE,UAAC,CAAC8D,SAAS,CAACP,EAAE,CAAC,EAAEvD,UAAC,CAAC8D,SAAS,CAACN,IAAI,CAAC,CAAC,CACzD,CAAC,CAAA;AAEFa,IAAAA,MAAM,CAACF,WAAW,GAAG,IAAI,CAAC1B,UAAU,CAAA;AACpC,IAAA,OAAO4B,MAAM,CAAA;AACf,GAAA;AAEAC,EAAAA,IAAIA,CAACf,EAA0B,EAAEgB,KAA0B,EAAE;AAC3D,IAAA,MAAMf,IAAI,GAAGxD,UAAC,CAAC8D,SAAS,CAACS,KAAK,CAAC,CAAA;AAC/B,IAAA,IAAIvE,UAAC,CAACwE,eAAe,CAACjB,EAAE,CAAC,EAAE;AACzB,MAAA,IAAI,CAACkB,iBAAiB,CAAClB,EAAE,EAAEC,IAAI,CAAC,CAAA;KACjC,MAAM,IAAIxD,UAAC,CAAC0E,cAAc,CAACnB,EAAE,CAAC,EAAE;AAC/B,MAAA,IAAI,CAACoB,gBAAgB,CAACpB,EAAE,EAAEC,IAAI,CAAC,CAAA;KAChC,MAAM,IAAIxD,UAAC,CAAC4E,mBAAmB,CAACrB,EAAE,CAAC,EAAE;AACpC,MAAA,IAAI,CAACsB,qBAAqB,CAACtB,EAAE,EAAEC,IAAI,CAAC,CAAA;AACtC,KAAC,MAAM;AACL,MAAA,IAAI,CAACb,KAAK,CAAC2B,IAAI,CACb,IAAI,CAAChB,uBAAuB,CAC1BC,EAAE,EACFC,IACF,CACF,CAAC,CAAA;AACH,KAAA;AACF,GAAA;AAEAsB,EAAAA,OAAOA,CAAC/E,IAAkB,EAAEgF,KAAsB,EAAE;IAClD,IACE,IAAI,CAAClC,eAAe,IACnB7C,UAAC,CAACmC,YAAY,CAACpC,IAAI,CAAC,IAAI,IAAI,CAAC2C,WAAW,CAACsC,GAAG,CAACjF,IAAI,CAACkB,IAAI,CAAE,EACzD;AACA,MAAA,OAAOlB,IAAI,CAAA;AACb,KAAC,MAAM;MACL,MAAM;QAAEU,KAAK;AAAEqC,QAAAA,mBAAAA;AAAoB,OAAC,GAAG,IAAI,CAAA;AAE3C,MAAA,IAAI9C,UAAC,CAACmC,YAAY,CAACpC,IAAI,CAAC,EAAE;QACxB,MAAMkF,OAAO,GAAGxE,KAAK,CAACyE,UAAU,CAACnF,IAAI,CAACkB,IAAI,CAAC,CAAA;AAC3C,QAAA,IAAIgE,OAAO,IAAA,IAAA,IAAPA,OAAO,CAAEE,QAAQ,IAAIF,OAAO,CAACG,IAAI,CAACC,aAAa,CAAC,OAAO,CAAC,EAAE;AAC5D,UAAA,OAAOtF,IAAI,CAAA;AACb,SAAA;AACF,OAAA;AAEA,MAAA,IAAIC,UAAC,CAACsF,iBAAiB,CAACvF,IAAI,CAAC,EAAE;AAC7B,QAAA,OAAOA,IAAI,CAAA;AACb,OAAA;AAEA,MAAA,IAAIC,UAAC,CAACmC,YAAY,CAACpC,IAAI,EAAE;AAAEkB,QAAAA,IAAI,EAAE,WAAA;AAAY,OAAC,CAAC,EAAE;AAC/C,QAAA,OAAOsE,aAAQ,CAACC,UAAU,CAACC,GAAG,CAAA;AACtC,qCAAA,EAAuC1F,IAAI,CAAA;AAC3C,QAAS,CAAA,CAAA;AACH,OAAA;AAEA,MAAA,IAAI2F,UAAU,CAAA;AACd,MAAA,MAAMC,IAAI,GAAG,CAAC5F,IAAI,CAAC,CAAA;AACnB,MAAA,IAAI,OAAOgF,KAAK,KAAK,QAAQ,EAAE;QAC7BY,IAAI,CAACrB,IAAI,CAACtE,UAAC,CAAC4F,cAAc,CAACb,KAAK,CAAC,CAAC,CAAA;AAGlCW,QAAAA,UAAU,GAAG,eAAe,CAAA;AAE9B,OAAC,MAAM;AAELA,QAAAA,UAAU,GAAG,SAAS,CAAA;AACxB,OAAA;AAEA,MAAA,IAAI5C,mBAAmB,EAAE;AACvB6C,QAAAA,IAAI,CAACtE,OAAO,CAACZ,KAAK,CAAC2E,IAAI,CAACS,GAAG,CAAC5C,SAAS,CAACyC,UAAU,CAAC,CAAC,CAAA;AAClDA,QAAAA,UAAU,GAAG,gBAAgB,CAAA;AAC/B,OAAA;AAEA,MAAA,OAAO1F,UAAC,CAAC8F,cAAc,CAACrF,KAAK,CAAC2E,IAAI,CAACS,GAAG,CAAC5C,SAAS,CAACyC,UAAU,CAAC,EAAEC,IAAI,CAAC,CAAA;AACrE,KAAA;AACF,GAAA;AAEAd,EAAAA,qBAAqBA,CACnB;IAAEkB,IAAI;AAAEC,IAAAA,KAAAA;GAA4B,EACpCC,QAA6B,EAC7B;AAKA,IAAA,IAAInG,UAAU,CAACmG,QAAQ,CAAC,EAAE;AACxB,MAAA,IAAI,CAAC3B,IAAI,CAACyB,IAAI,EAAEC,KAAK,CAAC,CAAA;AACtB,MAAA,OAAA;AACF,KAAA;IAIA,MAAME,MAAM,GAAG,IAAI,CAACzF,KAAK,CAAC0F,gCAAgC,CAACF,QAAQ,CAAC,CAAA;AAEpE,IAAA,IAAI,CAACtD,KAAK,CAAC2B,IAAI,CAAC,IAAI,CAACF,wBAAwB,CAAC8B,MAAM,EAAED,QAAQ,CAAC,CAAC,CAAA;AAEhE,IAAA,MAAMG,eAAe,GAAGpG,UAAC,CAACqG,qBAAqB,CAC7CrG,UAAC,CAACsG,gBAAgB,CAChB,KAAK,EACLtG,UAAC,CAAC8D,SAAS,CAACoC,MAAM,CAAC,EACnB,IAAI,CAACzF,KAAK,CAACsD,kBAAkB,EAC/B,CAAC,EACDiC,KAAK,EACLhG,UAAC,CAAC8D,SAAS,CAACoC,MAAM,CACpB,CAAC,CAAA;AAED,IAAA,IAAIlG,UAAC,CAACuG,SAAS,CAACR,IAAI,CAAC,EAAE;AACrB,MAAA,IAAIS,SAAS,CAAA;AACb,MAAA,IAAIzG,IAAI,CAAA;MAER,IAAI,IAAI,CAAC6C,IAAI,KAAK,OAAO,IAAI,IAAI,CAACA,IAAI,KAAK,KAAK,EAAE;QAChD4D,SAAS,GAAG,IAAI,CAAC/F,KAAK,CAACgG,qBAAqB,CAACP,MAAM,CAACjF,IAAI,CAAC,CAAA;QACzDlB,IAAI,GAAG,IAAI,CAACqE,wBAAwB,CAACoC,SAAS,EAAEJ,eAAe,CAAC,CAAA;AAClE,OAAC,MAAM;AACLI,QAAAA,SAAS,GAAGN,MAAM,CAAA;QAElBnG,IAAI,GAAGC,UAAC,CAAC4D,mBAAmB,CAC1B5D,UAAC,CAAC6D,oBAAoB,CAAC,GAAG,EAAE7D,UAAC,CAAC8D,SAAS,CAACoC,MAAM,CAAC,EAAEE,eAAe,CAClE,CAAC,CAAA;AACH,OAAA;AAEA,MAAA,IAAI,CAACzD,KAAK,CAAC2B,IAAI,CAACvE,IAAI,CAAC,CAAA;AACrB,MAAA,IAAI,CAACuE,IAAI,CAACyB,IAAI,EAAES,SAAS,CAAC,CAAA;AAC5B,KAAC,MAAM;AACL,MAAA,IAAI,CAAC7D,KAAK,CAAC2B,IAAI,CAAC,IAAI,CAAChB,uBAAuB,CAACyC,IAAI,EAAEK,eAAe,CAAC,CAAC,CAAA;AACtE,KAAA;AACF,GAAA;EAEAM,cAAcA,CACZnF,OAAwB,EACxBoF,MAAoB,EACpBC,UAAyB,EACzBC,eAAuB,EACvB;AACA,IAAA,MAAMC,KAAK,GAAGC,wBAAwB,CACpCxF,OAAO,CAACK,UAAU,CAACoF,KAAK,CAAC,CAAC,EAAEH,eAAe,CAAC,EAC5CF,MAAM,EACN,IAAI,CAAClG,KAAK,EACVQ,IAAI,IAAI,IAAI,CAACgC,SAAS,CAAChC,IAAI,CAAC,EAC5B,IAAI,CAAC8B,mBAAmB,EACxB,IAAI,CAACC,WACP,CAAC,CAAA;AACD,IAAA,IAAI,CAACL,KAAK,CAAC2B,IAAI,CAAC,IAAI,CAAChB,uBAAuB,CAACsD,UAAU,CAACxG,QAAQ,EAAE0G,KAAK,CAAC,CAAC,CAAA;AAC3E,GAAA;AAEAG,EAAAA,kBAAkBA,CAACpF,IAAsB,EAAEqF,OAAqB,EAAE;AAChE,IAAA,IAAIlH,UAAC,CAACmH,SAAS,CAACtF,IAAI,CAACuF,GAAG,CAAC,EAAEvF,IAAI,CAACwF,QAAQ,GAAG,IAAI,CAAA;AAE/C,IAAA,MAAM9F,OAAO,GAAGM,IAAI,CAACiF,KAAuC,CAAA;IAC5D,MAAMH,MAAM,GAAG3G,UAAC,CAACoD,gBAAgB,CAC/BpD,UAAC,CAAC8D,SAAS,CAACoD,OAAO,CAAC,EACpBrF,IAAI,CAACuF,GAAG,EACRvF,IAAI,CAACwF,QACP,CAAC,CAAA;AAED,IAAA,IAAIrH,UAAC,CAACuG,SAAS,CAAChF,OAAO,CAAC,EAAE;AACxB,MAAA,IAAI,CAAC+C,IAAI,CAAC/C,OAAO,EAAEoF,MAAM,CAAC,CAAA;AAC5B,KAAC,MAAM;AACL,MAAA,IAAI,CAAChE,KAAK,CAAC2B,IAAI,CAAC,IAAI,CAAChB,uBAAuB,CAAC/B,OAAO,EAAEoF,MAAM,CAAC,CAAC,CAAA;AAChE,KAAA;AACF,GAAA;AAEAlC,EAAAA,iBAAiBA,CAAClD,OAAwB,EAAEoF,MAAoB,EAAE;AAGhE,IAAA,IAAI,CAACpF,OAAO,CAACK,UAAU,CAACM,MAAM,EAAE;AAC9B,MAAA,IAAI,CAACS,KAAK,CAAC2B,IAAI,CACbtE,UAAC,CAAC4D,mBAAmB,CACnB5D,UAAC,CAAC8F,cAAc,CACd,IAAI,CAAC7C,SAAS,CAAC,0BAA0B,CAAC,EAC1CnD,UAAU,CAAC6G,MAAM,CAAC,GAAG,EAAE,GAAG,CAACA,MAAM,CACnC,CACF,CACF,CAAC,CAAA;AACD,MAAA,OAAA;AACF,KAAA;AAMA,IAAA,IAAIpF,OAAO,CAACK,UAAU,CAACM,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAACzB,KAAK,CAAC6G,QAAQ,CAACX,MAAM,CAAC,EAAE;MACjE,MAAMY,IAAI,GAAG,IAAI,CAAC9G,KAAK,CAAC0F,gCAAgC,CAACQ,MAAM,CAAC,CAAA;AAChE,MAAA,IAAI,CAAChE,KAAK,CAAC2B,IAAI,CAAC,IAAI,CAACF,wBAAwB,CAACmD,IAAI,EAAEZ,MAAM,CAAC,CAAC,CAAA;AAC5DA,MAAAA,MAAM,GAAGY,IAAI,CAAA;AACf,KAAA;AAGA,IAAA,IAAI5F,aAAa,CAACJ,OAAO,CAAC,EAAE;AAC1B,MAAA,IAAIiG,aAA8B,CAAA;AAClC,MAAA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlG,OAAO,CAACK,UAAU,CAACM,MAAM,EAAEuF,CAAC,EAAE,EAAE;AAClD,QAAA,MAAM5F,IAAI,GAAGN,OAAO,CAACK,UAAU,CAAC6F,CAAC,CAAC,CAAA;AAClC,QAAA,IAAIzH,UAAC,CAAC0B,aAAa,CAACG,IAAI,CAAC,EAAE;AACzB,UAAA,MAAA;AACF,SAAA;AACA,QAAA,MAAMuF,GAAG,GAAGvF,IAAI,CAACuF,GAAG,CAAA;AACpB,QAAA,IAAIvF,IAAI,CAACwF,QAAQ,IAAI,CAAC,IAAI,CAAC5G,KAAK,CAACiH,MAAM,CAACN,GAAG,CAAC,EAAE;UAC5C,MAAMnG,IAAI,GAAG,IAAI,CAACR,KAAK,CAAC0F,gCAAgC,CAACiB,GAAG,CAAC,CAAA;AAC7D,UAAA,IAAI,CAACzE,KAAK,CAAC2B,IAAI,CAEb,IAAI,CAACF,wBAAwB,CAACnD,IAAI,EAAEmG,GAAG,CACzC,CAAC,CAAA;UACD,IAAI,CAACI,aAAa,EAAE;AAClBA,YAAAA,aAAa,GAAGjG,OAAO,GAAAT,MAAA,CAAA6G,MAAA,KAClBpG,OAAO,EAAA;AACVK,cAAAA,UAAU,EAAEL,OAAO,CAACK,UAAU,CAACoF,KAAK,EAAC;aACtC,CAAA,CAAA;AACH,WAAA;UACAQ,aAAa,CAAC5F,UAAU,CAAC6F,CAAC,CAAC,GAAA3G,MAAA,CAAA6G,MAAA,CAAA,EAAA,EACtB9F,IAAI,EAAA;AACPuF,YAAAA,GAAG,EAAEnG,IAAAA;WACN,CAAA,CAAA;AACH,SAAA;AACF,OAAA;AACF,KAAA;AAEA,IAAA,KAAK,IAAIwG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlG,OAAO,CAACK,UAAU,CAACM,MAAM,EAAEuF,CAAC,EAAE,EAAE;AAClD,MAAA,MAAM5F,IAAI,GAAGN,OAAO,CAACK,UAAU,CAAC6F,CAAC,CAAC,CAAA;AAClC,MAAA,IAAIzH,UAAC,CAAC0B,aAAa,CAACG,IAAI,CAAC,EAAE;QACzB,IAAI,CAAC6E,cAAc,CAACnF,OAAO,EAAEoF,MAAM,EAAE9E,IAAI,EAAE4F,CAAC,CAAC,CAAA;AAC/C,OAAC,MAAM;AACL,QAAA,IAAI,CAACR,kBAAkB,CAACpF,IAAI,EAAE8E,MAAM,CAAC,CAAA;AACvC,OAAA;AACF,KAAA;AACF,GAAA;AAEAiB,EAAAA,qBAAqBA,CACnBrG,OAAuB,EACvBsG,GAAiB,EACiB;IAElC,IAAI,CAAC7H,UAAC,CAACsF,iBAAiB,CAACuC,GAAG,CAAC,EAAE,OAAO,KAAK,CAAA;IAI3C,IAAItG,OAAO,CAACC,QAAQ,CAACU,MAAM,GAAG2F,GAAG,CAACrG,QAAQ,CAACU,MAAM,EAAE,OAAA;AACnD,IAAA,IACEX,OAAO,CAACC,QAAQ,CAACU,MAAM,GAAG2F,GAAG,CAACrG,QAAQ,CAACU,MAAM,IAC7C,CAACZ,YAAY,CAACC,OAAO,CAAC,EACtB;AACA,MAAA,OAAO,KAAK,CAAA;AACd,KAAA;AAEA,IAAA,KAAK,MAAME,IAAI,IAAIF,OAAO,CAACC,QAAQ,EAAE;AAEnC,MAAA,IAAI,CAACC,IAAI,EAAE,OAAO,KAAK,CAAA;MAGvB,IAAIzB,UAAC,CAAC0D,kBAAkB,CAACjC,IAAI,CAAC,EAAE,OAAO,KAAK,CAAA;AAC9C,KAAA;AAEA,IAAA,KAAK,MAAMA,IAAI,IAAIoG,GAAG,CAACrG,QAAQ,EAAE;MAE/B,IAAIxB,UAAC,CAAC8H,eAAe,CAACrG,IAAI,CAAC,EAAE,OAAO,KAAK,CAAA;MAGzC,IAAIzB,UAAC,CAAC+H,gBAAgB,CAACtG,IAAI,CAAC,EAAE,OAAO,KAAK,CAAA;MAG1C,IAAIzB,UAAC,CAAC0D,kBAAkB,CAACjC,IAAI,CAAC,EAAE,OAAO,KAAK,CAAA;AAC9C,KAAA;AAGA,IAAA,MAAMb,QAAQ,GAAGZ,UAAC,CAACgI,qBAAqB,CAACzG,OAAO,CAAC,CAAA;AACjD,IAAA,MAAMU,KAA8B,GAAG;AAAEI,MAAAA,KAAK,EAAE,KAAK;AAAEzB,MAAAA,QAAAA;KAAU,CAAA;IAEjE,IAAI;MACFZ,UAAC,CAACiI,QAAQ,CAACJ,GAAG,EAAE9F,kBAAkB,EAAEE,KAAK,CAAC,CAAA;KAC3C,CAAC,OAAOiG,CAAC,EAAE;AACV,MAAA,IAAIA,CAAC,KAAKpG,cAAc,EAAE,MAAMoG,CAAC,CAAA;AACnC,KAAA;IAEA,OAAO,CAACjG,KAAK,CAACI,KAAK,CAAA;AACrB,GAAA;AAEA8F,EAAAA,wBAAwBA,CACtB5G,OAAuB,EACvBsG,GAA8B,EAC9B;AACA,IAAA,MAAMO,eAAe,GAAIC,EAAgB,IACvCA,EAAE,IAAFA,IAAAA,GAAAA,EAAE,GAAI,IAAI,CAAC5H,KAAK,CAACsD,kBAAkB,EAAE,CAAA;AAEvC,IAAA,KAAK,IAAI0D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlG,OAAO,CAACC,QAAQ,CAACU,MAAM,EAAEuF,CAAC,EAAE,EAAE;AAChD,MAAA,MAAMhG,IAAI,GAAGF,OAAO,CAACC,QAAQ,CAACiG,CAAC,CAAC,CAAA;AAChC,MAAA,IAAIzH,UAAC,CAAC0B,aAAa,CAACD,IAAI,CAAC,EAAE;QACzB,IAAI,CAAC6C,IAAI,CACP7C,IAAI,CAACrB,QAAQ,EACbJ,UAAC,CAACsI,eAAe,CAACT,GAAG,CAACrG,QAAQ,CAACwF,KAAK,CAACS,CAAC,CAAC,CAACc,GAAG,CAACH,eAAe,CAAC,CAC9D,CAAC,CAAA;AACH,OAAC,MAAM;AACL,QAAA,IAAI,CAAC9D,IAAI,CAAC7C,IAAI,EAAE2G,eAAe,CAACP,GAAG,CAACrG,QAAQ,CAACiG,CAAC,CAAC,CAAC,CAAC,CAAA;AACnD,OAAA;AACF,KAAA;AACF,GAAA;AAEA9C,EAAAA,gBAAgBA,CAACpD,OAAuB,EAAEiH,QAA6B,EAAE;IACvE,IAAIA,QAAQ,KAAK,IAAI,EAAE;MACrB,IAAI,CAAC7F,KAAK,CAAC2B,IAAI,CACbtE,UAAC,CAAC4D,mBAAmB,CACnB5D,UAAC,CAAC8F,cAAc,CAAC,IAAI,CAAC7C,SAAS,CAAC,0BAA0B,CAAC,EAAE,EAAE,CACjE,CACF,CAAC,CAAA;AACD,MAAA,OAAA;AACF,KAAA;AACA,IAAA,IAAI,CAAC1B,OAAO,CAACC,QAAQ,EAAE,OAAA;IASvB,IAAI,IAAI,CAACoG,qBAAqB,CAACrG,OAAO,EAAEiH,QAAQ,CAAC,EAAE;AACjD,MAAA,IAAI,CAACL,wBAAwB,CAAC5G,OAAO,EAAEiH,QAAQ,CAAC,CAAA;AAChD,MAAA,OAAA;AACF,KAAA;AAKA,IAAA,MAAMzD,KAAK,GAAG,CAACzD,YAAY,CAACC,OAAO,CAAC,IAAIA,OAAO,CAACC,QAAQ,CAACU,MAAM,CAAA;IAM/D,MAAM4C,OAAO,GAAG,IAAI,CAACA,OAAO,CAAC0D,QAAQ,EAAEzD,KAAK,CAAC,CAAA;AAE7C,IAAA,IAAI/E,UAAC,CAACmC,YAAY,CAAC2C,OAAO,CAAC,EAAE;AAG3B0D,MAAAA,QAAQ,GAAG1D,OAAO,CAAA;AACpB,KAAC,MAAM;MACL0D,QAAQ,GAAG,IAAI,CAAC/H,KAAK,CAAC0F,gCAAgC,CAACqC,QAAQ,CAAC,CAAA;MAChE,IAAI,CAAC9F,WAAW,CAAC+F,GAAG,CAACD,QAAQ,CAACvH,IAAI,CAAC,CAAA;AACnC,MAAA,IAAI,CAAC0B,KAAK,CAAC2B,IAAI,CAAC,IAAI,CAACF,wBAAwB,CAACoE,QAAQ,EAAE1D,OAAO,CAAC,CAAC,CAAA;AACnE,KAAA;AAEA,IAAA,KAAK,IAAI2C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlG,OAAO,CAACC,QAAQ,CAACU,MAAM,EAAEuF,CAAC,EAAE,EAAE;AAChD,MAAA,MAAMhG,IAAI,GAAGF,OAAO,CAACC,QAAQ,CAACiG,CAAC,CAAC,CAAA;MAGhC,IAAI,CAAChG,IAAI,EAAE,SAAA;AAEX,MAAA,IAAIiH,OAAO,CAAA;AAEX,MAAA,IAAI1I,UAAC,CAAC0B,aAAa,CAACD,IAAI,CAAC,EAAE;AACzBiH,QAAAA,OAAO,GAAG,IAAI,CAAC5D,OAAO,CAAC0D,QAAQ,CAAC,CAAA;QAChCE,OAAO,GAAG1I,UAAC,CAAC8F,cAAc,CACxB9F,UAAC,CAACoD,gBAAgB,CAACsF,OAAO,EAAE1I,UAAC,CAACqD,UAAU,CAAC,OAAO,CAAC,CAAC,EAClD,CAACrD,UAAC,CAAC4F,cAAc,CAAC6B,CAAC,CAAC,CACtB,CAAC,CAAA;QAID,IAAI,CAACnD,IAAI,CAAC7C,IAAI,CAACrB,QAAQ,EAAEsI,OAAO,CAAC,CAAA;AACnC,OAAC,MAAM;AACLA,QAAAA,OAAO,GAAG1I,UAAC,CAACoD,gBAAgB,CAACoF,QAAQ,EAAExI,UAAC,CAAC4F,cAAc,CAAC6B,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA;AACjE,QAAA,IAAI,CAACnD,IAAI,CAAC7C,IAAI,EAAEiH,OAAO,CAAC,CAAA;AAC1B,OAAA;AACF,KAAA;AACF,GAAA;AAEAlF,EAAAA,IAAIA,CAACjC,OAAe,EAAEoH,GAAiB,EAAE;AAIvC,IAAA,IAAI,CAAC3I,UAAC,CAACsF,iBAAiB,CAACqD,GAAG,CAAC,IAAI,CAAC3I,UAAC,CAAC0D,kBAAkB,CAACiF,GAAG,CAAC,EAAE;MAC3D,MAAMC,IAAI,GAAG,IAAI,CAACnI,KAAK,CAACoI,qBAAqB,CAACF,GAAG,EAAE,IAAI,CAAC,CAAA;AACxD,MAAA,IAAIC,IAAI,EAAE;AACR,QAAA,IAAI,CAACjG,KAAK,CAAC2B,IAAI,CAAC,IAAI,CAACF,wBAAwB,CAACwE,IAAI,EAAE5I,UAAC,CAAC8D,SAAS,CAAC6E,GAAG,CAAC,CAAC,CAAC,CAAA;AACtEA,QAAAA,GAAG,GAAGC,IAAI,CAAA;AACZ,OAAA;AACF,KAAA;AAEA,IAAA,IAAI,CAACtE,IAAI,CAAC/C,OAAO,EAAEoH,GAAG,CAAC,CAAA;IAEvB,OAAO,IAAI,CAAChG,KAAK,CAAA;AACnB,GAAA;AACF,CAAA;AAOO,SAASoE,wBAAwBA,CACtC+B,YAAiB,EACjBnC,MAAoB,EACpBlG,KAAY,EACZwC,SAA4B,EAC5BF,mBAA4B,EAC5BC,WAAoB,EACF;EAGlB,MAAMjC,IAAI,GAAG,EAAE,CAAA;EACf,IAAIgI,UAAU,GAAG,IAAI,CAAA;EACrB,IAAIC,kBAAkB,GAAG,KAAK,CAAA;AAC9B,EAAA,KAAK,IAAIvB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqB,YAAY,CAAC5G,MAAM,EAAEuF,CAAC,EAAE,EAAE;AAC5C,IAAA,MAAM5F,IAAI,GAAGiH,YAAY,CAACrB,CAAC,CAAC,CAAA;AAC5B,IAAA,MAAML,GAAG,GAAGvF,IAAI,CAACuF,GAAG,CAAA;IACpB,IAAIpH,UAAC,CAACmC,YAAY,CAACiF,GAAG,CAAC,IAAI,CAACvF,IAAI,CAACwF,QAAQ,EAAE;MACzCtG,IAAI,CAACuD,IAAI,CAACtE,UAAC,CAACiJ,aAAa,CAAC7B,GAAG,CAACnG,IAAI,CAAC,CAAC,CAAA;KACrC,MAAM,IAAIjB,UAAC,CAACkJ,iBAAiB,CAAC9B,GAAG,CAAC,EAAE;MACnCrG,IAAI,CAACuD,IAAI,CAACtE,UAAC,CAAC8D,SAAS,CAACsD,GAAG,CAAC,CAAC,CAAA;AAC3B4B,MAAAA,kBAAkB,GAAG,IAAI,CAAA;KAC1B,MAAM,IAAIhJ,UAAC,CAACmH,SAAS,CAACC,GAAG,CAAC,EAAE;AAE3BrG,MAAAA,IAAI,CAACuD,IAAI,CAACtE,UAAC,CAACiJ,aAAa,CAACE,MAAM,CAAC/B,GAAG,CAACN,KAAK,CAAC,CAAC,CAAC,CAAA;KAC9C,MAAM,IAAI9G,UAAC,CAACoJ,aAAa,CAAChC,GAAG,CAAC,EAAE,CAEhC,MAAM;MACLrG,IAAI,CAACuD,IAAI,CAACtE,UAAC,CAAC8D,SAAS,CAACsD,GAAG,CAAC,CAAC,CAAA;AAC3B2B,MAAAA,UAAU,GAAG,KAAK,CAAA;AACpB,KAAA;AACF,GAAA;AAEA,EAAA,IAAIjC,KAAK,CAAA;AACT,EAAA,IAAI/F,IAAI,CAACmB,MAAM,KAAK,CAAC,EAAE;IACrB,MAAMmH,aAAa,GAAGrG,WAAW,GAC7BhD,UAAC,CAACoD,gBAAgB,CAACpD,UAAC,CAACqD,UAAU,CAAC,QAAQ,CAAC,EAAErD,UAAC,CAACqD,UAAU,CAAC,QAAQ,CAAC,CAAC,GAClEJ,SAAS,CAAC,SAAS,CAAC,CAAA;IACxB6D,KAAK,GAAG9G,UAAC,CAAC8F,cAAc,CAACuD,aAAa,EAAE,CACtCrJ,UAAC,CAACsJ,gBAAgB,CAAC,EAAE,CAAC,EACtBtJ,UAAC,CAACuJ,kBAAkB,CAAC,CACnBvJ,UAAC,CAAC8F,cAAc,CAAC7C,SAAS,CAAC,0BAA0B,CAAC,EAAE,CACtDjD,UAAC,CAAC8D,SAAS,CAAC6C,MAAM,CAAC,CACpB,CAAC,EACF3G,UAAC,CAAC8D,SAAS,CAAC6C,MAAM,CAAC,CACpB,CAAC,CACH,CAAC,CAAA;AACJ,GAAC,MAAM;AACL,IAAA,IAAI6C,aAA2B,GAAGxJ,UAAC,CAACsI,eAAe,CAACvH,IAAI,CAAC,CAAA;IAEzD,IAAI,CAACgI,UAAU,EAAE;MACfS,aAAa,GAAGxJ,UAAC,CAAC8F,cAAc,CAC9B9F,UAAC,CAACoD,gBAAgB,CAACoG,aAAa,EAAExJ,UAAC,CAACqD,UAAU,CAAC,KAAK,CAAC,CAAC,EACtD,CAACJ,SAAS,CAAC,eAAe,CAAC,CAC7B,CAAC,CAAA;AACH,KAAC,MAAM,IAAI,CAAC+F,kBAAkB,IAAI,CAAChJ,UAAC,CAACyJ,SAAS,CAAChJ,KAAK,CAACiJ,KAAK,CAAC,EAAE;AAE3D,MAAA,MAAMC,YAAY,GAAGlJ,KAAK,CAACmJ,gBAAgB,EAAE,CAAA;AAC7C,MAAA,MAAMrG,EAAE,GAAGoG,YAAY,CAAClD,qBAAqB,CAAC,UAAU,CAAC,CAAA;MAEzDkD,YAAY,CAACrF,IAAI,CAAC;QAChBf,EAAE;AACFC,QAAAA,IAAI,EAAEgG,aAAa;AACnB5G,QAAAA,IAAI,EAAE,OAAA;AACR,OAAC,CAAC,CAAA;AAEF4G,MAAAA,aAAa,GAAGxJ,UAAC,CAAC8D,SAAS,CAACP,EAAE,CAAC,CAAA;AACjC,KAAA;IAEAuD,KAAK,GAAG9G,UAAC,CAAC8F,cAAc,CACtB7C,SAAS,CAAC,CAA0BF,uBAAAA,EAAAA,mBAAmB,GAAG,OAAO,GAAG,EAAE,EAAE,CAAC,EACzE,CAAC/C,UAAC,CAAC8D,SAAS,CAAC6C,MAAM,CAAC,EAAE6C,aAAa,CACrC,CAAC,CAAA;AACH,GAAA;AACA,EAAA,OAAO1C,KAAK,CAAA;AACd,CAAA;AAEO,SAAS+C,0BAA0BA,CACxCzE,IAAqC,EACrCnC,SAA4B,EAC5BH,mBAA4B,EAC5BD,eAAwB,EACxBE,mBAA4B,EAC5BC,WAAoB,EACpB;EACA,MAAM;IAAEjD,IAAI;AAAEU,IAAAA,KAAAA;AAAM,GAAC,GAAG2E,IAAI,CAAA;AAE5B,EAAA,MAAM0E,QAAQ,GAAG/J,IAAI,CAAC6C,IAAI,CAAA;AAC1B,EAAA,MAAMmH,OAAO,GAAGhK,IAAI,CAACiK,GAAG,CAAA;EACxB,MAAMrH,KAAK,GAAG,EAAE,CAAA;AAEhB,EAAA,KAAK,IAAI8E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG1H,IAAI,CAACkK,YAAY,CAAC/H,MAAM,EAAEuF,CAAC,EAAE,EAAE;AACjD,IAAA,MAAMpD,MAAM,GAAGtE,IAAI,CAACkK,YAAY,CAACxC,CAAC,CAAC,CAAA;AAEnC,IAAA,MAAMjB,SAAS,GAAGnC,MAAM,CAACb,IAAI,CAAA;AAC7B,IAAA,MAAMjC,OAAO,GAAG8C,MAAM,CAACd,EAAE,CAAA;AAEzB,IAAA,MAAM2G,aAAuC,GAC3C,IAAI5H,wBAAwB,CAAC;MAE3BG,UAAU,EAAE1C,IAAI,CAACoE,WAAW;AAC5BxB,MAAAA,KAAK,EAAEA,KAAK;AACZlC,MAAAA,KAAK,EAAEA,KAAK;MACZmC,IAAI,EAAE7C,IAAI,CAAC6C,IAA4C;MACvDC,eAAe;MACfC,mBAAmB;MACnBE,WAAW;MACXD,mBAAmB;AACnBE,MAAAA,SAAAA;AACF,KAAC,CAAC,CAAA;AAEJ,IAAA,IAAIjD,UAAC,CAACuG,SAAS,CAAChF,OAAO,CAAC,EAAE;AAExB2I,MAAAA,aAAa,CAAC1G,IAAI,CAACjC,OAAO,EAAoCiF,SAAS,CAAC,CAAA;MAExE,IAAI,CAACiB,CAAC,KAAK1H,IAAI,CAACkK,YAAY,CAAC/H,MAAM,GAAG,CAAC,EAAE;AAGvClC,QAAAA,UAAC,CAACmK,QAAQ,CAACxH,KAAK,CAACA,KAAK,CAACT,MAAM,GAAG,CAAC,CAAC,EAAEmC,MAAM,CAAC,CAAA;AAC7C,OAAA;AACF,KAAC,MAAM;AACL1B,MAAAA,KAAK,CAAC2B,IAAI,CACRtE,UAAC,CAACmK,QAAQ,CACRD,aAAa,CAAC5G,uBAAuB,CAAC/B,OAAO,EAAEiF,SAAS,CAAC,EACzDnC,MACF,CACF,CAAC,CAAA;AACH,KAAA;AACF,GAAA;EAEA,IAAI+F,IAAkC,GAAG,IAAI,CAAA;EAC7C,IAAIC,QAAQ,GAAG,EAAE,CAAA;AACjB,EAAA,KAAK,MAAMtK,IAAI,IAAI4C,KAAK,EAAE;AACxB,IAAA,IAAI3C,UAAC,CAACsK,qBAAqB,CAACvK,IAAI,CAAC,EAAE;MACjC,IAAIqK,IAAI,KAAK,IAAI,EAAE;QAEjBA,IAAI,CAACH,YAAY,CAAC3F,IAAI,CAAC,GAAGvE,IAAI,CAACkK,YAAY,CAAC,CAAA;AAC5C,QAAA,SAAA;AACF,OAAC,MAAM;QAELlK,IAAI,CAAC6C,IAAI,GAAGkH,QAAQ,CAAA;AACpBM,QAAAA,IAAI,GAAGrK,IAAI,CAAA;AACb,OAAA;AACF,KAAC,MAAM;AACLqK,MAAAA,IAAI,GAAG,IAAI,CAAA;AACb,KAAA;AAEA,IAAA,IAAI,CAACrK,IAAI,CAACiK,GAAG,EAAE;MACbjK,IAAI,CAACiK,GAAG,GAAGD,OAAO,CAAA;AACpB,KAAA;AACAM,IAAAA,QAAQ,CAAC/F,IAAI,CAACvE,IAAI,CAAC,CAAA;AACrB,GAAA;EAEA,IACEsK,QAAQ,CAACnI,MAAM,KAAK,CAAC,IACrBlC,UAAC,CAACsK,qBAAqB,CAACD,QAAQ,CAAC,CAAC,CAAC,CAAC,IACpCrK,UAAC,CAACuK,qBAAqB,CAACF,QAAQ,CAAC,CAAC,CAAC,CAAC,IACpCrK,UAAC,CAAC+H,gBAAgB,CAACsC,QAAQ,CAAC,CAAC,CAAC,CAAC7E,UAAU,CAAC,IAC1C6E,QAAQ,CAAC,CAAC,CAAC,CAACJ,YAAY,CAAC/H,MAAM,KAAK,CAAC,EACrC;AAOA,IAAA,MAAMsI,IAAI,GAAGH,QAAQ,CAAC,CAAC,CAAC,CAAC7E,UAAU,CAAA;AACnCgF,IAAAA,IAAI,CAACC,SAAS,GAAG,CAACJ,QAAQ,CAAC,CAAC,CAAC,CAACJ,YAAY,CAAC,CAAC,CAAC,CAACzG,IAAI,CAAC,CAAA;IACnD6G,QAAQ,GAAG,CAACG,IAAI,CAAC,CAAA;AACnB,GAAC,MAAM;AAEL,IAAA,IACExK,UAAC,CAAC0K,cAAc,CAACtF,IAAI,CAACuF,MAAM,EAAE;AAAEnH,MAAAA,IAAI,EAAEzD,IAAAA;AAAK,KAAC,CAAC,IAC7C,CAACsK,QAAQ,CAACrJ,IAAI,CAAC4J,CAAC,IAAI5K,UAAC,CAACsK,qBAAqB,CAACM,CAAC,CAAC,CAAC,EAC/C;AACA,MAAA,KAAK,IAAInD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4C,QAAQ,CAACnI,MAAM,EAAEuF,CAAC,EAAE,EAAE;AACxC,QAAA,MAAM1H,IAAY,GAAGsK,QAAQ,CAAC5C,CAAC,CAAC,CAAA;AAChC,QAAA,IAAIzH,UAAC,CAACuK,qBAAqB,CAACxK,IAAI,CAAC,EAAE;AACjCsK,UAAAA,QAAQ,CAAC5C,CAAC,CAAC,GAAG1H,IAAI,CAACyF,UAAU,CAAA;AAC/B,SAAA;AACF,OAAA;AACF,KAAA;AACF,GAAA;AAEA,EAAA,IAAI6E,QAAQ,CAACnI,MAAM,KAAK,CAAC,EAAE;AACzBkD,IAAAA,IAAI,CAACyF,WAAW,CAACR,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAA;AAC/B,GAAC,MAAM;AACLjF,IAAAA,IAAI,CAAC0F,mBAAmB,CAACT,QAAQ,CAAC,CAAA;AACpC,GAAA;EACA5J,KAAK,CAACsK,KAAK,EAAE,CAAA;AACf,CAAA;AAEO,SAASC,2BAA2BA,CACzC5F,IAA4D,EAC5DnC,SAA4B,EAC5BH,mBAA4B,EAC5BD,eAAwB,EACxBE,mBAA4B,EAC5BC,WAAoB,EACpB;EACA,MAAM;IAAEjD,IAAI;IAAEU,KAAK;AAAEwK,IAAAA,UAAAA;AAAW,GAAC,GAAG7F,IAAI,CAAA;EAExC,MAAMzC,KAAqC,GAAG,EAAE,CAAA;AAEhD,EAAA,MAAMuH,aAAa,GAAG,IAAI5H,wBAAwB,CAAC;IACjDpC,QAAQ,EAAEH,IAAI,CAACG,QAAQ;AACvBO,IAAAA,KAAK,EAAEA,KAAK;AACZkC,IAAAA,KAAK,EAAEA,KAAK;IACZG,mBAAmB;IACnBD,eAAe;IACfE,mBAAmB;IACnBC,WAAW;AACXC,IAAAA,SAAAA;AACF,GAAC,CAAC,CAAA;AAEF,EAAA,IAAI0F,GAAwB,CAAA;AAC5B,EAAA,IACG,CAACsC,UAAU,CAACV,qBAAqB,EAAE,IAClC,CAACU,UAAU,CAACC,oBAAoB,EAAE,IACpC9F,IAAI,CAAC+F,kBAAkB,EAAE,EACzB;IACAxC,GAAG,GAAGlI,KAAK,CAAC0F,gCAAgC,CAACpG,IAAI,CAACiG,KAAK,EAAE,KAAK,CAAC,CAAA;IAE/DrD,KAAK,CAAC2B,IAAI,CACRtE,UAAC,CAACiE,mBAAmB,CAAC,KAAK,EAAE,CAACjE,UAAC,CAACkE,kBAAkB,CAACyE,GAAG,EAAE5I,IAAI,CAACiG,KAAK,CAAC,CAAC,CACtE,CAAC,CAAA;IAED,IAAIhG,UAAC,CAACsF,iBAAiB,CAACvF,IAAI,CAACiG,KAAK,CAAC,EAAE;MACnCkE,aAAa,CAACxH,WAAW,CAAC+F,GAAG,CAACE,GAAG,CAAC1H,IAAI,CAAC,CAAA;AACzC,KAAA;AACF,GAAA;AAEAiJ,EAAAA,aAAa,CAAC1G,IAAI,CAACzD,IAAI,CAACgG,IAAI,EAAE4C,GAAG,IAAI5I,IAAI,CAACiG,KAAK,CAAC,CAAA;AAEhD,EAAA,IAAI2C,GAAG,EAAE;AACP,IAAA,IAAIsC,UAAU,CAACG,yBAAyB,EAAE,EAAE;MAC1ChG,IAAI,CAACyF,WAAW,CAAC7K,UAAC,CAACoB,cAAc,CAAC,EAAE,CAAC,CAAC,CAAA;AACtCuB,MAAAA,KAAK,CAAC2B,IAAI,CAACtE,UAAC,CAACqL,eAAe,CAACrL,UAAC,CAAC8D,SAAS,CAAC6E,GAAG,CAAC,CAAC,CAAC,CAAA;AACjD,KAAC,MAAM;AACLhG,MAAAA,KAAK,CAAC2B,IAAI,CAACtE,UAAC,CAAC4D,mBAAmB,CAAC5D,UAAC,CAAC8D,SAAS,CAAC6E,GAAG,CAAC,CAAC,CAAC,CAAA;AACrD,KAAA;AACF,GAAA;AAEAvD,EAAAA,IAAI,CAAC0F,mBAAmB,CAACnI,KAAK,CAAC,CAAA;EAC/BlC,KAAK,CAACsK,KAAK,EAAE,CAAA;AACf;;ACpxBA,SAASO,0CAA0CA,CACjDvL,IAA2B,EAC3B;AACA,EAAA,KAAK,MAAMsE,MAAM,IAAItE,IAAI,CAACkK,YAAY,EAAE;AACtC,IAAA,IAAIjK,UAAC,CAACuG,SAAS,CAAClC,MAAM,CAACd,EAAE,CAAC,IAAIc,MAAM,CAACd,EAAE,CAACgI,IAAI,KAAK,aAAa,EAAE;AAC9D,MAAA,OAAO,IAAI,CAAA;AACb,KAAA;AACF,GAAA;AACA,EAAA,OAAO,KAAK,CAAA;AACd,CAAA;AAQA,YAAeC,yBAAO,CAAC,CAACC,GAAG,EAAEC,OAAgB,KAAK;EAAA,IAAAC,IAAA,EAAAC,eAAA,EAAAC,KAAA,EAAAC,qBAAA,EAAAC,KAAA,EAAAC,gBAAA,CAAA;AAChDP,EAAAA,GAAG,CAACQ,aAAa,CAAkB,CAAE,CAAC,CAAA;EAEtC,MAAM;AAAEjJ,IAAAA,WAAW,GAAG,KAAA;AAAM,GAAC,GAAG0I,OAAO,CAAA;EAEvC,MAAM7I,eAAe,IAAA8I,IAAA,GAAA,CAAAC,eAAA,GACnBH,GAAG,CAACS,UAAU,CAAC,iBAAiB,CAAC,KAAA,IAAA,GAAAN,eAAA,GAAIF,OAAO,CAACS,KAAK,KAAA,IAAA,GAAAR,IAAA,GAAI,KAAK,CAAA;EAC7D,MAAM7I,mBAAmB,IAAA+I,KAAA,GAAA,CAAAC,qBAAA,GACvBJ,OAAO,CAACU,cAAc,KAAA,IAAA,GAAAN,qBAAA,GAAIL,GAAG,CAACS,UAAU,CAAC,qBAAqB,CAAC,KAAA,IAAA,GAAAL,KAAA,GAAI,KAAK,CAAA;EAC1E,MAAM9I,mBAAmB,IAAAgJ,KAAA,GAAA,CAAAC,gBAAA,GACvBP,GAAG,CAACS,UAAU,CAAC,qBAAqB,CAAC,KAAA,IAAA,GAAAF,gBAAA,GAAIN,OAAO,CAACS,KAAK,KAAA,IAAA,GAAAJ,KAAA,GAAI,KAAK,CAAA;EAEjE,OAAO;AACL9K,IAAAA,IAAI,EAAE,yBAAyB;AAE/BoL,IAAAA,OAAO,EAAE;MACPC,sBAAsBA,CAAClH,IAAI,EAAE;AAC3B,QAAA,MAAMmH,WAAW,GAAGnH,IAAI,CAACzE,GAAG,CAAC,aAAa,CAAC,CAAA;AAC3C,QAAA,IAAI,CAAC4L,WAAW,CAACjC,qBAAqB,EAAE,EAAE,OAAA;AAC1C,QAAA,IAAI,CAACgB,0CAA0C,CAACiB,WAAW,CAACxM,IAAI,CAAC,EAC/D,OAAA;AAK+D,QAAA;AAAA,UAAA,IAAAyM,qBAAA,CAAA;UAE/D,CAAAA,qBAAA,GAAApH,IAAI,CAACqH,sBAAsB,KAAAD,IAAAA,GAAAA,qBAAA,GAA3BpH,IAAI,CAACqH,sBAAsB,GAEzBC,OAAO,CAAC,iBAAiB,CAAC,CAACC,QAAQ,CAACC,SAAS,CAACH,sBAAsB,CAAA;AACxE,SAAA;QACArH,IAAI,CAACqH,sBAAsB,EAAE,CAAA;OAC9B;MAEDI,aAAaA,CAACzH,IAA+B,EAAE;QAC7C,MAAM;UAAErF,IAAI;AAAEU,UAAAA,KAAAA;AAAM,SAAC,GAAG2E,IAAI,CAAA;AAC5B,QAAA,MAAMW,IAAI,GAAGhG,IAAI,CAACgG,IAAI,CAAA;AAEtB,QAAA,IAAI/F,UAAC,CAACuG,SAAS,CAACR,IAAI,CAAC,EAAE;AAGrB,UAAA,MAAMwB,IAAI,GAAG9G,KAAK,CAACgG,qBAAqB,CAAC,KAAK,CAAC,CAAA;AAE/C1G,UAAAA,IAAI,CAACgG,IAAI,GAAG/F,UAAC,CAACiE,mBAAmB,CAAC,KAAK,EAAE,CACvCjE,UAAC,CAACkE,kBAAkB,CAACqD,IAAI,CAAC,CAC3B,CAAC,CAAA;UAEFnC,IAAI,CAAC5E,WAAW,EAAE,CAAA;UAClB,MAAMsM,aAAa,GAAI1H,IAAI,CAACrF,IAAI,CAACoB,IAAI,CAAsBA,IAAI,CAAA;UAC/D,MAAMwB,KAAK,GAAG,EAAE,CAAA;UAKhB,IAAImK,aAAa,CAAC5K,MAAM,KAAK,CAAC,IAAIkD,IAAI,CAAC+F,kBAAkB,EAAE,EAAE;AAC3DxI,YAAAA,KAAK,CAACtB,OAAO,CAACrB,UAAC,CAAC4D,mBAAmB,CAACnD,KAAK,CAACsD,kBAAkB,EAAE,CAAC,CAAC,CAAA;AAClE,WAAA;UAEApB,KAAK,CAACtB,OAAO,CACXrB,UAAC,CAAC4D,mBAAmB,CACnB5D,UAAC,CAAC6D,oBAAoB,CAAC,GAAG,EAAEkC,IAAI,EAAE/F,UAAC,CAAC8D,SAAS,CAACyD,IAAI,CAAC,CACrD,CACF,CAAC,CAAA;AAEDlH,UAAAA,wBAAwB,CAAC+E,IAAI,EAAEzC,KAAK,CAAC,CAAA;UACrClC,KAAK,CAACsK,KAAK,EAAE,CAAA;AACb,UAAA,OAAA;AACF,SAAA;AAEA,QAAA,IAAI,CAAC/K,UAAC,CAACsK,qBAAqB,CAACvE,IAAI,CAAC,EAAE,OAAA;QAEpC,MAAMxE,OAAO,GAAGwE,IAAI,CAACkE,YAAY,CAAC,CAAC,CAAC,CAAC1G,EAAE,CAAA;AACvC,QAAA,IAAI,CAACvD,UAAC,CAACuG,SAAS,CAAChF,OAAO,CAAC,IAAIA,OAAO,CAACgK,IAAI,KAAK,aAAa,EAAE,OAAA;AAE7D,QAAA,MAAMnE,GAAG,GAAG3G,KAAK,CAACgG,qBAAqB,CAAC,KAAK,CAAC,CAAA;QAC9C1G,IAAI,CAACgG,IAAI,GAAG/F,UAAC,CAACiE,mBAAmB,CAAC8B,IAAI,CAACnD,IAAI,EAAE,CAC3C5C,UAAC,CAACkE,kBAAkB,CAACkD,GAAG,EAAE,IAAI,CAAC,CAChC,CAAC,CAAA;QAEF,MAAMzE,KAAqC,GAAG,EAAE,CAAA;AAEhD,QAAA,MAAMuH,aAAa,GAAG,IAAI5H,wBAAwB,CAAC;UACjDM,IAAI,EAAEmD,IAAI,CAACnD,IAA4C;AACvDnC,UAAAA,KAAK,EAAEA,KAAK;AACZkC,UAAAA,KAAK,EAAEA,KAAK;UACZG,mBAAmB;UACnBD,eAAe;UACfE,mBAAmB;UACnBC,WAAW;AACXC,UAAAA,SAAS,EAAEhC,IAAI,IAAI,IAAI,CAACgC,SAAS,CAAChC,IAAI,CAAA;AACxC,SAAC,CAAC,CAAA;AAEFiJ,QAAAA,aAAa,CAAC1G,IAAI,CAACjC,OAAO,EAAE6F,GAAG,CAAC,CAAA;AAEhC/G,QAAAA,wBAAwB,CAAC+E,IAAI,EAAEzC,KAAK,CAAC,CAAA;QACrClC,KAAK,CAACsK,KAAK,EAAE,CAAA;OACd;AAEDgC,MAAAA,WAAWA,CAAC;QAAEhN,IAAI;AAAEU,QAAAA,KAAAA;AAAM,OAAC,EAAE;AAC3B,QAAA,MAAMc,OAAO,GAAGxB,IAAI,CAACiN,KAAK,CAAA;AAC1B,QAAA,IAAI,CAAChN,UAAC,CAACuG,SAAS,CAAChF,OAAO,CAAC,EAAE,OAAA;AAE3B,QAAA,MAAMoH,GAAG,GAAGlI,KAAK,CAACgG,qBAAqB,CAAC,KAAK,CAAC,CAAA;QAC9C1G,IAAI,CAACiN,KAAK,GAAGrE,GAAG,CAAA;QAEhB,MAAMhG,KAAqC,GAAG,EAAE,CAAA;AAEhD,QAAA,MAAMuH,aAAa,GAAG,IAAI5H,wBAAwB,CAAC;AACjDM,UAAAA,IAAI,EAAE,KAAK;AACXnC,UAAAA,KAAK,EAAEA,KAAK;AACZkC,UAAAA,KAAK,EAAEA,KAAK;UACZG,mBAAmB;UACnBD,eAAe;UACfE,mBAAmB;UACnBC,WAAW;AACXC,UAAAA,SAAS,EAAEhC,IAAI,IAAI,IAAI,CAACgC,SAAS,CAAChC,IAAI,CAAA;AACxC,SAAC,CAAC,CAAA;AACFiJ,QAAAA,aAAa,CAAC1G,IAAI,CAACjC,OAAO,EAAEoH,GAAG,CAAC,CAAA;AAEhC5I,QAAAA,IAAI,CAACoB,IAAI,CAACA,IAAI,GAAG,CAAC,GAAGwB,KAAK,EAAE,GAAG5C,IAAI,CAACoB,IAAI,CAACA,IAAI,CAAC,CAAA;QAC9CV,KAAK,CAACsK,KAAK,EAAE,CAAA;OACd;AAEDkC,MAAAA,oBAAoBA,CAAC7H,IAAI,EAAEnD,KAAK,EAAE;QAChC,IAAI,CAACjC,UAAC,CAACuG,SAAS,CAACnB,IAAI,CAACrF,IAAI,CAACgG,IAAI,CAAC,EAAE,OAAA;AAClCiF,QAAAA,2BAA2B,CACzB5F,IAAI,EACJnE,IAAI,IAAIgB,KAAK,CAACgB,SAAS,CAAChC,IAAI,CAAC,EAC7B6B,mBAAmB,EACnBD,eAAe,EACfE,mBAAmB,EACnBC,WACF,CAAC,CAAA;OACF;AAEDkK,MAAAA,mBAAmBA,CAAC9H,IAAI,EAAEnD,KAAK,EAAE;QAC/B,MAAM;UAAElC,IAAI;AAAE4K,UAAAA,MAAAA;AAAO,SAAC,GAAGvF,IAAI,CAAA;AAC7B,QAAA,IAAIpF,UAAC,CAACmN,eAAe,CAACxC,MAAM,CAAC,EAAE,OAAA;AAC/B,QAAA,IAAI,CAACA,MAAM,IAAI,CAACvF,IAAI,CAACgI,SAAS,EAAE,OAAA;AAChC,QAAA,IAAI,CAAC9B,0CAA0C,CAACvL,IAAI,CAAC,EAAE,OAAA;AACvD8J,QAAAA,0BAA0B,CACxBzE,IAAI,EACJnE,IAAI,IAAIgB,KAAK,CAACgB,SAAS,CAAChC,IAAI,CAAC,EAC7B6B,mBAAmB,EACnBD,eAAe,EACfE,mBAAmB,EACnBC,WACF,CAAC,CAAA;AACH,OAAA;AACF,KAAA;GACD,CAAA;AACH,CAAC,CAAC;;;;;;"}