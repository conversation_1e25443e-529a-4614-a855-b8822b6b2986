import React from 'react';
import { 
  FiX, 
  FiUser, 
  FiMail, 
  FiPhone, 
  FiMapPin, 
  FiBuilding, 
  FiStar,
  FiCalendar,
  FiEdit
} from 'react-icons/fi';
import './PersonDetailModal.css';

const PersonDetailModal = ({ person, onClose, onEdit }) => {
  if (!person) return null;

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getNatureLabel = (nature) => {
    const natureMap = {
      1: 'Individual',
      2: 'Corporate',
      3: 'Partnership',
      4: 'Government'
    };
    return natureMap[nature] || 'Unknown';
  };

  const getGenderLabel = (gender) => {
    const genderMap = {
      1: 'Male',
      2: 'Female',
      3: 'Other'
    };
    return genderMap[gender] || 'Not specified';
  };

  const renderStarRating = (rating) => {
    if (!rating) return <span className="no-rating">No rating</span>;
    
    return (
      <div className="star-rating">
        {[1, 2, 3, 4, 5].map(star => (
          <FiStar
            key={star}
            className={star <= rating ? 'star filled' : 'star'}
            size={16}
          />
        ))}
        <span className="rating-text">({rating}/5)</span>
      </div>
    );
  };

  return (
    <div className="person-detail-overlay">
      <div className="person-detail-modal">
        <div className="modal-header">
          <div className="header-left">
            <FiUser className="header-icon" />
            <div>
              <h2>{person.name}</h2>
              {person.firmName && <p className="firm-name">{person.firmName}</p>}
            </div>
          </div>
          <div className="header-actions">
            <button 
              className="btn btn-primary"
              onClick={() => onEdit(person)}
              title="Edit Person"
            >
              <FiEdit />
              Edit
            </button>
            <button 
              className="close-button"
              onClick={onClose}
              title="Close"
            >
              <FiX />
            </button>
          </div>
        </div>

        <div className="modal-body">
          <div className="detail-sections">
            {/* Basic Information */}
            <div className="detail-section">
              <h3>Basic Information</h3>
              <div className="detail-grid">
                <div className="detail-item">
                  <label>Full Name</label>
                  <span>{person.name}</span>
                </div>
                {person.firmName && (
                  <div className="detail-item">
                    <label>Firm/Company</label>
                    <span>{person.firmName}</span>
                  </div>
                )}
                <div className="detail-item">
                  <label>Nature</label>
                  <span className={`nature-badge nature-${person.nature}`}>
                    {getNatureLabel(person.nature)}
                  </span>
                </div>
                <div className="detail-item">
                  <label>Gender</label>
                  <span>{getGenderLabel(person.gender)}</span>
                </div>
                <div className="detail-item">
                  <label>Star Rating</label>
                  {renderStarRating(person.starRating)}
                </div>
              </div>
            </div>

            {/* Contact Information */}
            <div className="detail-section">
              <h3>Contact Information</h3>
              <div className="detail-grid">
                <div className="detail-item">
                  <label><FiPhone size={14} /> Mobile Number</label>
                  <span>{person.mobileNumber || 'Not provided'}</span>
                </div>
                <div className="detail-item">
                  <label><FiMail size={14} /> Email</label>
                  <span>{person.email || 'Not provided'}</span>
                </div>
                <div className="detail-item">
                  <label><FiMapPin size={14} /> Address</label>
                  <span>{person.address || 'Not provided'}</span>
                </div>
                <div className="detail-item">
                  <label>District</label>
                  <span>{person.district || 'Not provided'}</span>
                </div>
                <div className="detail-item">
                  <label>Working State</label>
                  <span>{person.workingState || 'Not provided'}</span>
                </div>
              </div>
            </div>

            {/* Organization Information */}
            <div className="detail-section">
              <h3>Organization</h3>
              <div className="detail-grid">
                <div className="detail-item">
                  <label><FiBuilding size={14} /> Division</label>
                  <span>{person.division?.name || 'Not assigned'}</span>
                </div>
                <div className="detail-item">
                  <label>Category</label>
                  <span>{person.category?.name || 'Not assigned'}</span>
                </div>
                <div className="detail-item">
                  <label>Sub Category</label>
                  <span>{person.subCategory?.name || 'Not assigned'}</span>
                </div>
              </div>
            </div>

            {/* Additional Information */}
            <div className="detail-section">
              <h3>Additional Information</h3>
              <div className="detail-grid">
                <div className="detail-item">
                  <label><FiCalendar size={14} /> Created</label>
                  <span>{formatDate(person.createdAt)}</span>
                </div>
                <div className="detail-item">
                  <label><FiCalendar size={14} /> Last Updated</label>
                  <span>{formatDate(person.updatedAt)}</span>
                </div>
                {person.remarks && (
                  <div className="detail-item full-width">
                    <label>Remarks</label>
                    <span>{person.remarks}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PersonDetailModal;
