{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Advanced\\\\Advanced CRM filed all use\\\\frontend\\\\src\\\\components\\\\PersonManagement.js\",\n  _s = $RefreshSig$();\nimport { useState } from 'react';\nimport FormBuilder from './forms/FormBuilder';\nimport DynamicPersonForm from './forms/DynamicPersonForm';\nimport PersonList from './PersonList';\nimport ImportPersons from './import/ImportPersons';\nimport './PersonManagement.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PersonManagement = () => {\n  _s();\n  const [currentView, setCurrentView] = useState('list'); // 'list', 'create', 'edit', 'formBuilder', 'import'\n  const [selectedPerson, setSelectedPerson] = useState(null);\n  const [notification, setNotification] = useState(null);\n  const showNotification = (message, type = 'success') => {\n    setNotification({\n      message,\n      type\n    });\n    setTimeout(() => setNotification(null), 5000);\n  };\n  const handleCreatePerson = () => {\n    setSelectedPerson(null);\n    setCurrentView('create');\n  };\n  const handleEditPerson = person => {\n    setSelectedPerson(person);\n    setCurrentView('edit');\n  };\n  const handlePersonSubmit = () => {\n    const action = currentView === 'create' ? 'created' : 'updated';\n    showNotification(`Person ${action} successfully!`);\n    setCurrentView('list');\n    setSelectedPerson(null);\n  };\n  const handleFormBuilderOpen = () => {\n    setCurrentView('formBuilder');\n  };\n  const handleFormBuilderSave = config => {\n    showNotification(`Form configuration \"${config.name}\" saved successfully!`);\n    setCurrentView('list');\n  };\n  const handleImportOpen = () => {\n    setShowImportModal(true);\n  };\n  const handleImportSuccess = results => {\n    showNotification(`Import completed! ${results.successfulRows} persons imported successfully.`);\n    setShowImportModal(false);\n    // Refresh the person list if we're on the list view\n    if (currentView === 'list') {\n      // The PersonList component should handle refreshing\n    }\n  };\n  const handleImportClose = () => {\n    setShowImportModal(false);\n  };\n  const handleViewForms = () => {\n    setShowImportModal(false);\n    setCurrentView('create'); // This will trigger the form selection view\n  };\n  const handleCancel = () => {\n    setCurrentView('list');\n    setSelectedPerson(null);\n  };\n  const renderCurrentView = () => {\n    switch (currentView) {\n      case 'create':\n        return /*#__PURE__*/_jsxDEV(DynamicPersonForm, {\n          mode: \"create\",\n          onSubmit: handlePersonSubmit,\n          onCancel: handleCancel\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this);\n      case 'edit':\n        return /*#__PURE__*/_jsxDEV(DynamicPersonForm, {\n          mode: \"edit\",\n          initialData: selectedPerson,\n          onSubmit: handlePersonSubmit,\n          onCancel: handleCancel\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this);\n      case 'formBuilder':\n        return /*#__PURE__*/_jsxDEV(FormBuilder, {\n          onSave: handleFormBuilderSave,\n          onCancel: handleCancel\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this);\n      case 'list':\n      default:\n        return /*#__PURE__*/_jsxDEV(PersonList, {\n          onEditPerson: handleEditPerson\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"person-management\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"management-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-content\",\n        children: /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Person Management System\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-nav\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setCurrentView('list'),\n          className: `nav-btn ${currentView === 'list' ? 'active' : ''}`,\n          children: \"\\uD83D\\uDCCB Person List\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleCreatePerson,\n          className: `nav-btn ${currentView === 'create' ? 'active' : ''}`,\n          children: \"\\u2795 Create Person\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleFormBuilderOpen,\n          className: `nav-btn ${currentView === 'formBuilder' ? 'active' : ''}`,\n          children: \"\\uD83D\\uDD27 Form Builder\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleImportOpen,\n          className: \"nav-btn\",\n          children: \"\\uD83D\\uDCE5 Import Persons\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 7\n    }, this), notification && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `notification ${notification.type}`,\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        children: notification.message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setNotification(null),\n        className: \"notification-close\",\n        children: \"\\xD7\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"management-content\",\n      children: renderCurrentView()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 159,\n      columnNumber: 7\n    }, this), showImportModal && /*#__PURE__*/_jsxDEV(ImportPersons, {\n      onClose: handleImportClose,\n      onSuccess: handleImportSuccess,\n      onViewForms: handleViewForms\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 112,\n    columnNumber: 5\n  }, this);\n};\n_s(PersonManagement, \"F2+AD5zXJ0JrARTGQ73zSSHcPjc=\");\n_c = PersonManagement;\nexport default PersonManagement;\nvar _c;\n$RefreshReg$(_c, \"PersonManagement\");", "map": {"version": 3, "names": ["useState", "FormBuilder", "DynamicPersonForm", "PersonList", "<PERSON><PERSON>rt<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "PersonManagement", "_s", "current<PERSON>iew", "set<PERSON><PERSON><PERSON>View", "<PERSON><PERSON><PERSON>", "setSelected<PERSON><PERSON>", "notification", "setNotification", "showNotification", "message", "type", "setTimeout", "handleCreate<PERSON>erson", "handleEditPerson", "person", "handlePersonSubmit", "action", "handleFormBuilderOpen", "handleFormBuilderSave", "config", "name", "handleImportOpen", "setShowImportModal", "handleImportSuccess", "results", "successfulRows", "handleImportClose", "handleViewForms", "handleCancel", "renderCurrentView", "mode", "onSubmit", "onCancel", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "initialData", "onSave", "onEdit<PERSON>erson", "className", "children", "onClick", "showImportModal", "onClose", "onSuccess", "onViewForms", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/components/PersonManagement.js"], "sourcesContent": ["import { useState } from 'react';\nimport FormBuilder from './forms/FormBuilder';\nimport DynamicPersonForm from './forms/DynamicPersonForm';\nimport PersonList from './PersonList';\nimport ImportPersons from './import/ImportPersons';\n\nimport './PersonManagement.css';\n\nconst PersonManagement = () => {\n  const [currentView, setCurrentView] = useState('list'); // 'list', 'create', 'edit', 'formBuilder', 'import'\n  const [selectedPerson, setSelectedPerson] = useState(null);\n  const [notification, setNotification] = useState(null);\n\n  const showNotification = (message, type = 'success') => {\n    setNotification({ message, type });\n    setTimeout(() => setNotification(null), 5000);\n  };\n\n  const handleCreatePerson = () => {\n    setSelectedPerson(null);\n    setCurrentView('create');\n  };\n\n  const handleEditPerson = (person) => {\n    setSelectedPerson(person);\n    setCurrentView('edit');\n  };\n\n  const handlePersonSubmit = () => {\n    const action = currentView === 'create' ? 'created' : 'updated';\n    showNotification(`Person ${action} successfully!`);\n    setCurrentView('list');\n    setSelectedPerson(null);\n  };\n\n  const handleFormBuilderOpen = () => {\n    setCurrentView('formBuilder');\n  };\n\n  const handleFormBuilderSave = (config) => {\n    showNotification(`Form configuration \"${config.name}\" saved successfully!`);\n    setCurrentView('list');\n  };\n\n  const handleImportOpen = () => {\n    setShowImportModal(true);\n  };\n\n  const handleImportSuccess = (results) => {\n    showNotification(`Import completed! ${results.successfulRows} persons imported successfully.`);\n    setShowImportModal(false);\n    // Refresh the person list if we're on the list view\n    if (currentView === 'list') {\n      // The PersonList component should handle refreshing\n    }\n  };\n\n  const handleImportClose = () => {\n    setShowImportModal(false);\n  };\n\n  const handleViewForms = () => {\n    setShowImportModal(false);\n    setCurrentView('create'); // This will trigger the form selection view\n  };\n\n  const handleCancel = () => {\n    setCurrentView('list');\n    setSelectedPerson(null);\n  };\n\n  const renderCurrentView = () => {\n    switch (currentView) {\n      case 'create':\n        return (\n          <DynamicPersonForm\n            mode=\"create\"\n            onSubmit={handlePersonSubmit}\n            onCancel={handleCancel}\n          />\n        );\n      \n      case 'edit':\n        return (\n          <DynamicPersonForm\n            mode=\"edit\"\n            initialData={selectedPerson}\n            onSubmit={handlePersonSubmit}\n            onCancel={handleCancel}\n          />\n        );\n      \n      case 'formBuilder':\n        return (\n          <FormBuilder\n            onSave={handleFormBuilderSave}\n            onCancel={handleCancel}\n          />\n        );\n      \n      case 'list':\n      default:\n        return (\n          <PersonList\n            onEditPerson={handleEditPerson}\n          />\n        );\n    }\n  };\n\n  return (\n    <div className=\"person-management\">\n      {/* Header */}\n      <div className=\"management-header\">\n        <div className=\"header-content\">\n          <h1>Person Management System</h1>\n        </div>\n        \n        {/* Navigation */}\n        <div className=\"header-nav\">\n          <button\n            onClick={() => setCurrentView('list')}\n            className={`nav-btn ${currentView === 'list' ? 'active' : ''}`}\n          >\n            📋 Person List\n          </button>\n          <button\n            onClick={handleCreatePerson}\n            className={`nav-btn ${currentView === 'create' ? 'active' : ''}`}\n          >\n            ➕ Create Person\n          </button>\n          <button\n            onClick={handleFormBuilderOpen}\n            className={`nav-btn ${currentView === 'formBuilder' ? 'active' : ''}`}\n          >\n            🔧 Form Builder\n          </button>\n          <button\n            onClick={handleImportOpen}\n            className=\"nav-btn\"\n          >\n            📥 Import Persons\n          </button>\n        </div>\n      </div>\n\n      {/* Notification */}\n      {notification && (\n        <div className={`notification ${notification.type}`}>\n          <span>{notification.message}</span>\n          <button onClick={() => setNotification(null)} className=\"notification-close\">\n            ×\n          </button>\n        </div>\n      )}\n\n      {/* Main Content */}\n      <div className=\"management-content\">\n        {renderCurrentView()}\n      </div>\n\n\n\n      {/* Import Modal */}\n      {showImportModal && (\n        <ImportPersons\n          onClose={handleImportClose}\n          onSuccess={handleImportSuccess}\n          onViewForms={handleViewForms}\n        />\n      )}\n    </div>\n  );\n};\n\nexport default PersonManagement;\n"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,iBAAiB,MAAM,2BAA2B;AACzD,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,aAAa,MAAM,wBAAwB;AAElD,OAAO,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhC,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGV,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;EACxD,MAAM,CAACW,cAAc,EAAEC,iBAAiB,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACa,YAAY,EAAEC,eAAe,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAEtD,MAAMe,gBAAgB,GAAGA,CAACC,OAAO,EAAEC,IAAI,GAAG,SAAS,KAAK;IACtDH,eAAe,CAAC;MAAEE,OAAO;MAAEC;IAAK,CAAC,CAAC;IAClCC,UAAU,CAAC,MAAMJ,eAAe,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;EAC/C,CAAC;EAED,MAAMK,kBAAkB,GAAGA,CAAA,KAAM;IAC/BP,iBAAiB,CAAC,IAAI,CAAC;IACvBF,cAAc,CAAC,QAAQ,CAAC;EAC1B,CAAC;EAED,MAAMU,gBAAgB,GAAIC,MAAM,IAAK;IACnCT,iBAAiB,CAACS,MAAM,CAAC;IACzBX,cAAc,CAAC,MAAM,CAAC;EACxB,CAAC;EAED,MAAMY,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMC,MAAM,GAAGd,WAAW,KAAK,QAAQ,GAAG,SAAS,GAAG,SAAS;IAC/DM,gBAAgB,CAAC,UAAUQ,MAAM,gBAAgB,CAAC;IAClDb,cAAc,CAAC,MAAM,CAAC;IACtBE,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMY,qBAAqB,GAAGA,CAAA,KAAM;IAClCd,cAAc,CAAC,aAAa,CAAC;EAC/B,CAAC;EAED,MAAMe,qBAAqB,GAAIC,MAAM,IAAK;IACxCX,gBAAgB,CAAC,uBAAuBW,MAAM,CAACC,IAAI,uBAAuB,CAAC;IAC3EjB,cAAc,CAAC,MAAM,CAAC;EACxB,CAAC;EAED,MAAMkB,gBAAgB,GAAGA,CAAA,KAAM;IAC7BC,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMC,mBAAmB,GAAIC,OAAO,IAAK;IACvChB,gBAAgB,CAAC,qBAAqBgB,OAAO,CAACC,cAAc,iCAAiC,CAAC;IAC9FH,kBAAkB,CAAC,KAAK,CAAC;IACzB;IACA,IAAIpB,WAAW,KAAK,MAAM,EAAE;MAC1B;IAAA;EAEJ,CAAC;EAED,MAAMwB,iBAAiB,GAAGA,CAAA,KAAM;IAC9BJ,kBAAkB,CAAC,KAAK,CAAC;EAC3B,CAAC;EAED,MAAMK,eAAe,GAAGA,CAAA,KAAM;IAC5BL,kBAAkB,CAAC,KAAK,CAAC;IACzBnB,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAC;EAC5B,CAAC;EAED,MAAMyB,YAAY,GAAGA,CAAA,KAAM;IACzBzB,cAAc,CAAC,MAAM,CAAC;IACtBE,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMwB,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,QAAQ3B,WAAW;MACjB,KAAK,QAAQ;QACX,oBACEH,OAAA,CAACJ,iBAAiB;UAChBmC,IAAI,EAAC,QAAQ;UACbC,QAAQ,EAAEhB,kBAAmB;UAC7BiB,QAAQ,EAAEJ;QAAa;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;MAGN,KAAK,MAAM;QACT,oBACErC,OAAA,CAACJ,iBAAiB;UAChBmC,IAAI,EAAC,MAAM;UACXO,WAAW,EAAEjC,cAAe;UAC5B2B,QAAQ,EAAEhB,kBAAmB;UAC7BiB,QAAQ,EAAEJ;QAAa;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;MAGN,KAAK,aAAa;QAChB,oBACErC,OAAA,CAACL,WAAW;UACV4C,MAAM,EAAEpB,qBAAsB;UAC9Bc,QAAQ,EAAEJ;QAAa;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;MAGN,KAAK,MAAM;MACX;QACE,oBACErC,OAAA,CAACH,UAAU;UACT2C,YAAY,EAAE1B;QAAiB;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC;IAER;EACF,CAAC;EAED,oBACErC,OAAA;IAAKyC,SAAS,EAAC,mBAAmB;IAAAC,QAAA,gBAEhC1C,OAAA;MAAKyC,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChC1C,OAAA;QAAKyC,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC7B1C,OAAA;UAAA0C,QAAA,EAAI;QAAwB;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,eAGNrC,OAAA;QAAKyC,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB1C,OAAA;UACE2C,OAAO,EAAEA,CAAA,KAAMvC,cAAc,CAAC,MAAM,CAAE;UACtCqC,SAAS,EAAE,WAAWtC,WAAW,KAAK,MAAM,GAAG,QAAQ,GAAG,EAAE,EAAG;UAAAuC,QAAA,EAChE;QAED;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTrC,OAAA;UACE2C,OAAO,EAAE9B,kBAAmB;UAC5B4B,SAAS,EAAE,WAAWtC,WAAW,KAAK,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;UAAAuC,QAAA,EAClE;QAED;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTrC,OAAA;UACE2C,OAAO,EAAEzB,qBAAsB;UAC/BuB,SAAS,EAAE,WAAWtC,WAAW,KAAK,aAAa,GAAG,QAAQ,GAAG,EAAE,EAAG;UAAAuC,QAAA,EACvE;QAED;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTrC,OAAA;UACE2C,OAAO,EAAErB,gBAAiB;UAC1BmB,SAAS,EAAC,SAAS;UAAAC,QAAA,EACpB;QAED;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL9B,YAAY,iBACXP,OAAA;MAAKyC,SAAS,EAAE,gBAAgBlC,YAAY,CAACI,IAAI,EAAG;MAAA+B,QAAA,gBAClD1C,OAAA;QAAA0C,QAAA,EAAOnC,YAAY,CAACG;MAAO;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACnCrC,OAAA;QAAQ2C,OAAO,EAAEA,CAAA,KAAMnC,eAAe,CAAC,IAAI,CAAE;QAACiC,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAAC;MAE7E;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACN,eAGDrC,OAAA;MAAKyC,SAAS,EAAC,oBAAoB;MAAAC,QAAA,EAChCZ,iBAAiB,CAAC;IAAC;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC,EAKLO,eAAe,iBACd5C,OAAA,CAACF,aAAa;MACZ+C,OAAO,EAAElB,iBAAkB;MAC3BmB,SAAS,EAAEtB,mBAAoB;MAC/BuB,WAAW,EAAEnB;IAAgB;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACnC,EAAA,CAtKID,gBAAgB;AAAA+C,EAAA,GAAhB/C,gBAAgB;AAwKtB,eAAeA,gBAAgB;AAAC,IAAA+C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}