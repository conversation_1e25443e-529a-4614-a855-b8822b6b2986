{"name": "@jest/expect-utils", "version": "30.0.4", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/expect-utils"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "require": "./build/index.js", "import": "./build/index.mjs", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"@jest/get-type": "30.0.1"}, "devDependencies": {"immutable": "^5.1.2", "jest-matcher-utils": "30.0.4"}, "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "f4296d2bc85c1405f84ddf613a25d0bc3766b7e5"}